module.exports = {
  transform: {
    '^.+\\.svelte$': [
      'svelte-jester',
      {
        preprocess: true
      }
    ],
    '^.+\\.(ts|tsx)$': 'ts-jest',
    '^.+\\.(js|jsx)$': 'babel-jest'
  },
  transformIgnorePatterns: ['node_modules/(?!svelte)'],
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'svelte'],
  moduleNameMapper: {
    '^\\$lib/(.*)$': '<rootDir>/src/lib/$1',
    '^\\$env/dynamic/public$': '<rootDir>/types/env.d.ts'
  },
  preset: 'ts-jest'
};
