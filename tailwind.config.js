import tailwindcssAnimate from 'tailwindcss-animate';
import plugin from 'tailwindcss/plugin';

/** @type {import('tailwindcss').Config} */
const config = {
  darkMode: ['class'],
  content: ['./src/**/*.{html,js,svelte,ts}'],
  safelist: ['dark'],
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px'
      }
    },
    extend: {
      colors: {
        border: 'hsl(var(--border) / <alpha-value>)',
        input: 'hsl(var(--input) / <alpha-value>)',
        ring: 'hsl(var(--ring) / <alpha-value>)',
        background: 'hsl(var(--background) / <alpha-value>)',
        foreground: 'hsl(var(--foreground) / <alpha-value>)',
        destructive: {
          DEFAULT: 'hsl(var(--destructive) / <alpha-value>)',
          foreground: 'hsl(var(--destructive-foreground) / <alpha-value>)'
        },
        muted: {
          DEFAULT: 'hsl(var(--muted) / <alpha-value>)',
          foreground: 'hsl(var(--muted-foreground) / <alpha-value>)'
        },
        accent: {
          DEFAULT: 'hsl(var(--accent) / <alpha-value>)',
          foreground: 'hsl(var(--accent-foreground) / <alpha-value>)'
        },
        popover: {
          DEFAULT: 'hsl(var(--popover) / <alpha-value>)',
          foreground: 'hsl(var(--popover-foreground) / <alpha-value>)'
        },
        sidebar: {
          DEFAULT: 'hsl(var(--sidebar-background))',
          foreground: 'hsl(var(--sidebar-foreground))',
          primary: 'hsl(var(--sidebar-primary))',
          'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
          accent: 'hsl(var(--sidebar-accent))',
          'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
          border: 'hsl(var(--sidebar-border))',
          ring: 'hsl(var(--sidebar-ring))'
        },
        // Storm App
        link: 'var(--color-link)',
        warning: 'var(--color-warning)',
        danger: 'var(--color-danger)',
        success: 'var(--color-success)',
        info: 'var(--color-info)',
        navBgColor: 'var(--color-nav-drawer)',
        sidebarBg: 'var(--color-sidebar-background)',
        bgColor: 'var(--color-background)',
        component: 'var(--color-component)',
        accentText: 'var(--color-accent-text)',
        primaryText: 'var(--color-text)',
        invertText: 'var(--color-invert-text)',
        primary: 'var(--color-primary)',
        secondary: 'var(--color-secondary)'
      },
      spacing: {
        xs: 'var(--size-xs)',
        sm: 'var(--size-s)',
        md: 'var(--size-m)',
        lg: 'var(--size-l)',
        xl: 'var(--size-xl)',
        xxl: 'calc(var(--size-s) * 5)',
        xxxl: 'calc(var(--size-s) * 6)',
        '4xl': 'calc(var(--size-s) * 10)',
        '5xl': 'calc(var(--size-s) * 15)',
        '6xl': 'calc(var(--size-s) * 24)',
        '7xl': 'calc(var(--size-s) * 50)',
        content: '1.5rem',
        'content-mobile': '1rem'
      },
      borderRadius: {
        none: '0',
        xs: 'var(--rounding-xs)',
        sm: 'var(--rounding-s)',
        DEFAULT: 'var(--rounding-s)',
        md: 'var(--rounding-m)',
        lg: 'var(--rounding-l)',
        xl: 'var(--rounding-xl)',
        inner: 'var(--border-radius-inner)',
        full: '100%'
      },
      screens: {
        md: '840px',
        sm: '480px',
        mobile: { max: '767px' },
        desktop: { min: '767px' }
      },
      fontFamily: {
        base: 'var(--font-primary)'
      },
      maxWidth: {
        xs: '15rem',
        sm: '20rem',
        md: '24rem',
        lg: '32rem'
      },
      minWidth: {
        xs: '5rem',
        sm: '10rem',
        md: '15rem',
        lg: '20rem'
      },
      borderColor: {
        borderColor: 'var(--color-border)'
      },
      keyframes: {
        'accordion-down': {
          from: { maxHeight: '0px', opacity: '0' },
          to: { maxHeight: 'var(--bits-accordion-content-height)', opacity: '1' }
        },
        'accordion-up': {
          from: { maxHeight: 'var(--bits-accordion-content-height)', opacity: '1' },
          to: { maxHeight: '0px', opacity: '0' }
        },
        'caret-blink': {
          '0%,70%,100%': { opacity: '1' },
          '20%,50%': { opacity: '0' }
        }
      },
      animation: {
        'accordion-down': 'accordion-down 0.15s ease-out',
        'accordion-up': 'accordion-up 0.15s ease-out',
        'caret-blink': 'caret-blink 1.25s ease-out infinite'
      }
    }
  },
  plugins: [
    tailwindcssAnimate,
    plugin(function ({ addUtilities }) {
      addUtilities({
        '.custom-scroll::-webkit-scrollbar': {
          width: '0.25rem',
          height: '0.5rem',
          backgroundColor: 'var(--accent)'
        },
        '.custom-scroll::-webkit-scrollbar-thumb': {
          backgroundColor: 'var(--background)'
        }
      });
    })
  ]
};

export default config;
