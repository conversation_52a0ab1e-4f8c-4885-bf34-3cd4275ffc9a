# Storm UI

Storm application has 3 modules:

- [`Forecasting outages`](https://bitbucket.org/trovedata/storm-knowledge-base/wiki/Forecasting%20module)

- [`Shifts`](https://bitbucket.org/trovedata/storm-knowledge-base/wiki/Shifts%20module)

- [`ETR (estimated time to restoration)`](https://bitbucket.org/trovedata/storm-knowledge-base/wiki/ETR%20module)

The application has dark (default) and light modes, desktop and mobile versions.

Project stack:

- Node v16

- npm v8

- [`Sveltekit`](https://kit.svelte.dev/)

- [`Typescript`](https://www.typescriptlang.org/)

- [`Tailwind`](https://tailwindcss.com/)

- [`shadcn-svelte`](https://www.shadcn-svelte.com/)

- [`Vite bundler`](https://vitejs.dev/)

- [`Vitest testing framework`](https://vitest.dev/)

- [`ESlint linter`](https://eslint.org/)

- [`Prettier formatter`](https://prettier.io/)

- [`Keycloak authorization`](https://www.keycloak.org/)

## Developing

To start development:

1. Clone project `<NAME_EMAIL>:trovedata/storm-outage-prediction-ui.git`

2. Checkout `development` branch (main is for production, staging - for staging environment)

3. Install dependencies `npm i`

4. Create `.env` file and copy one of the client's environment setups from `.env.example`

5. Install proxy package globally ` npm i -g local-cors-proxy`

6. Run the proxy script for the same client you used in the previous step `npm run proxy-internal`

7. Run `npm run dev`

8. Open in browser

## Building

To create a production version of your app:

`npm run build`

## Lint, format, test

To create a production version of your app:

`npm run lint`

`npm run format`

`npm run test:unit`

`npm run test`
