pipeline {
  agent {
    kubernetes {
      inheritFrom 'default-agent'
      yaml """
      apiVersion: v1
      kind: Pod
      spec:
        containers:
        - name: node
          image: node:20-alpine
          command: ['sleep', '99d']
        - name: docker-client
          image: docker:20
          command: ['sleep', '99d']
          env:
            - name: DOCKER_HOST
              value: tcp://localhost:2375
        - name: snyk
          image: snyk/snyk:node
          command: ['sleep', '99d']
        - name: snyk-docker
          image: snyk/snyk:docker
          command: ['sleep', '99d']
          env:
            - name: DOCKER_HOST
              value: tcp://localhost:2375
        - name: aws-cli
          image: amazon/aws-cli:latest
          command: ['sleep', '99d']

        - name: docker-daemon
          image: docker:20-dind
          env:
            - name: DOCKER_TLS_CERTDIR
              value: ""
          securityContext:
            privileged: true
          volumeMounts:
              - name: cache
                mountPath: /var/lib/docker
        volumes:
          - name: cache
            emptyDir: {}
      """
    }
  }
  triggers {
    // Run the pipeline on a schedule for fresh image scanning -> every Saturday at 12:01 AM
    cron((env.BR<PERSON>CH_NAME in ["development", "develop"]) ? "1 0 * * 6" : "")
  }
  environment {
    DEFAULT_IMAGE_NAME = 'esource/storm-outage-prediction-ui'
    DEFAULT_SEVERITY = 'high'
    DEFAULT_FAIL_ON = 'all'
    DEFAULT_FORCE_PUSH = 'false'
  }
  parameters {
    choice(
      name: 'SNYK_SEVERITY_THRESHOLD',
      description: 'SNYK Severity Threshold - See: https://docs.snyk.io/manage-issues/issue-management/severity-levels',
      choices: ['high', 'medium', 'low', 'trivial']
    )
    choice(
      name: 'SNYK_FAIL_ON',
      description: 'Fail only when there are vulnerabilities that can be fixed - See https://docs.snyk.io/snyk-cli/commands/container-test',
      choices: ["all", "upgradable"]
    )
  }
  stages {
    stage('Prepare Environment') {
      steps {
        script {
          env.TIMER_TRIGGERED = 'false' // Default value
          // Get the list of causes for the current build
          def causes = currentBuild.getBuildCauses()
          // Iterate over each cause and check if it's a timer trigger
          for (cause in causes) {
              echo "Build cause: ${cause}"
              if (cause.shortDescription == 'Started by timer') {
                  // If the cause is 'Started by timer', set the environment variable
                  env.TIMER_TRIGGERED = 'true'
              }
          }
          echo "TIMER_TRIGGERED: ${env.TIMER_TRIGGERED}"
        }
      }
    }
    stage('Create canonical version') {
      steps {
        script {
          timestamp = sh(script: 'date +%s', returnStdout: true).trim()
          escaped_branch = sh(script: 'echo $GIT_BRANCH | sed "s=/=_=g"', returnStdout: true).trim()
          env.VERSION = "${escaped_branch}-${GIT_COMMIT[0..7]}-${timestamp}"

          // Initialize the parameters with default values if they are null - prevent first build from failing
          env.IMAGE_NAME = "${env.DEFAULT_IMAGE_NAME}"
          env.SNYK_SEVERITY_THRESHOLD = params.SNYK_SEVERITY_THRESHOLD ?: env.DEFAULT_SEVERITY
          env.SNYK_FAIL_ON = params.SNYK_FAIL_ON ?: env.DEFAULT_FAIL_ON
          env.BUILD_IMAGE = "${env.IMAGE_NAME}:${env.VERSION}"
        }
      }
    }
    stage('Build') {
      steps {
        container('node') {
          sh 'rm -rf node_modules package-lock.json'
          sh 'npm cache clean --force'

          sh 'npm config set fetch-retry-maxtimeout 600000'
          sh 'npm config set fetch-retry-mintimeout 200000'
          sh 'npm config set registry https://registry.npmjs.org/'

          sh 'npm install -g npm@latest'
          sh 'npm install --registry=https://registry.npmjs.org/'
          sh 'npm run build'
        }
      }
    }
    stage('Code Quality') {
      steps {
        container('node') {
          sh 'npm run lint'
        }
      }
    }
    stage('Lighthouse') {
      steps {
        container('node') {
          sh 'npm run lighthouse'
        }
      }
    }
    stage('Build Docker Image') {
      steps {
        container('docker-client') {
          sh "docker build -t ${env.BUILD_IMAGE} ."
        }
      }
    }
    stage('Snyk Security Scan - Node') {
      when {
        anyOf {
          // we just want a fresh image for our security scans
          expression {return env.TIMER_TRIGGERED == 'false'}
        }
      }
      steps {
        container('snyk') {
          withCredentials([string(credentialsId: 'snyk_token', variable: 'SNYK_TOKEN')]) {
            sh "snyk test \
              --file=package.json \
              --fail-on=${env.SNYK_FAIL_ON} \
              --severity-threshold=${env.SNYK_SEVERITY_THRESHOLD} \
              --print-deps \
              --color"
          }
        }
      }
    }
    stage('Snyk Security Scan - Docker') {
      when {
        anyOf {
          // we just want a fresh image for our security scans
          expression {return env.TIMER_TRIGGERED == 'false'}
        }
      }
      steps {
        container('snyk-docker') {
          withCredentials([string(credentialsId: 'snyk_token', variable: 'SNYK_TOKEN')]) {
            sh "snyk container test ${env.BUILD_IMAGE} \
              --file=Dockerfile --fail-on=${env.SNYK_FAIL_ON} \
              --severity-threshold=${env.SNYK_SEVERITY_THRESHOLD} \
              --print-deps \
              --color"
          }
        }
      }
    }
    stage('Push Docker Image to AWS ECR') {
      steps {
        script {
          docker_pwd = ''
          imageName = "${env.BUILD_IMAGE}"
          repository = '411985166407.dkr.ecr.us-east-1.amazonaws.com'
          container('aws-cli') {
            docker_pwd = sh(script: 'aws ecr get-login-password --region us-east-1', returnStdout: true)
          }
          container('docker-client') {
            sh "echo \"${docker_pwd}\" | docker login -u AWS --password-stdin ${repository}"
            sh "docker tag ${imageName} ${repository}/${imageName}"
            sh "docker push ${repository}/${imageName}"
          }
        }
      }
    }
    stage('Deploy to APC Development Azure CR') {
      when {
        branch 'development'
      }
      steps {
        container('docker-client') {
          script {
            repository_a = "aprampdvacr.azurecr.io"
            withCredentials([usernamePassword(credentialsId: 'apc_azurecr_credentials', usernameVariable: 'DOCKER_USERNAME', passwordVariable: 'DOCKER_PASSWORD')]) {
              sh "docker login -u $DOCKER_USERNAME -p $DOCKER_PASSWORD ${repository_a}"
            }
            sh "docker tag $IMAGE_NAME:$VERSION ${repository_a}/$IMAGE_NAME:$VERSION"
            sh "docker push ${repository_a}/$IMAGE_NAME:$VERSION"
            sh "docker tag $IMAGE_NAME:$VERSION ${repository_a}/$IMAGE_NAME:latest"
            sh "docker push ${repository_a}/$IMAGE_NAME:latest"
          }
        }
      }
    }
    stage('Deploy to APC UA Azure CR') {
      when {
        branch 'staging'
      }
      steps {
        container('docker-client') {
          script {
            repository_b = "aprampuaacr.azurecr.io"
            withCredentials([usernamePassword(credentialsId: 'apc_ua_azurecr_credentials', usernameVariable: 'DOCKER_USERNAME', passwordVariable: 'DOCKER_PASSWORD')]) {
              sh "docker login -u $DOCKER_USERNAME -p $DOCKER_PASSWORD ${repository_b}"
            }
            sh "docker tag $IMAGE_NAME:$VERSION ${repository_b}/$IMAGE_NAME:$VERSION"
            sh "docker push ${repository_b}/$IMAGE_NAME:$VERSION"
            sh "docker tag $IMAGE_NAME:$VERSION ${repository_b}/$IMAGE_NAME:latest"
            sh "docker push ${repository_b}/$IMAGE_NAME:latest"
          }
        }
      }
    }
  }
}
