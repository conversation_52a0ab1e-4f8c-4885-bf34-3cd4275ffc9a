{"name": "storm-outage-prediction-ui", "version": "2.14", "scripts": {"proxy-apc": "lcp --proxyUrl https://dv-storm.sococloudapp.com/ --port 8012", "proxy-dlc": "lcp --proxyUrl https://development.dlc.ds.esource.com --port 8012", "proxy-evergy": "lcp --proxyUrl https://staging.evergy.ds.esource.com --port 8012", "proxy-internal": "lcp --proxyUrl https://internal-storm.hub.ds.esource.com --port 8012", "proxy-lge": "lcp --proxyUrl https://staging.lge.ds.esource.com --port 8012", "proxy-pge": "lcp --proxyUrl https://staging.pge.ds.esource.com --port 8012", "proxy-ppl": "lcp --proxyUrl https://staging.ppl.ds.esource.com/ --port 8012", "proxy-rie": "lcp --proxyUrl https://staging.rie.ds.esource.com --port 8012", "dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "test:unit": "vitest", "test": "playwright test --ui", "test-coverage:unit": "vitest run --coverage", "lint": "eslint --fix .", "format": "prettier --write .", "lighthouse": "exit 0 && echo 'TODO'", "prepare": "husky install"}, "dependencies": {"@azure/msal-browser": "^3.4.0", "@microsoft/clarity": "^1.0.0", "@stomp/stompjs": "^7.0.0", "@sveltejs/vite-plugin-svelte": "^5.0.3", "ag-charts-enterprise": "^10.1.0", "ag-grid-enterprise": "^32.3.3", "axios": "^1.8.3", "dotenv": "^16.4.7", "esbuild": "^0.24.0", "event-source-polyfill": "^1.0.31", "jest-environment-jsdom": "^29.7.0", "keycloak-js": "^26.1.5", "lucide-svelte": "^0.509.0", "luxon": "^3.4.3", "mapbox-gl": "^3.0.1", "mapboxgl-legend": "^1.13.0", "micromatch": "^4.0.8", "nanoid": "^5.1.2", "qs": "^6.13.0", "resolve": "1.22.8", "uuid": "^9.0.1", "ws": "^8.14.2", "yup": "^1.6.1"}, "devDependencies": {"@fontsource/fira-mono": "^5.0.8", "@lucide/svelte": "^0.509.0", "@neoconfetti/svelte": "^1.0.0", "@playwright/test": "^1.49.1", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/adapter-node": "^5.2.12", "@sveltejs/kit": "^2.20.7", "@testing-library/jest-dom": "^6.4.8", "@testing-library/svelte": "^5.2.1", "@types/cookie": "^0.5.3", "@types/event-source-polyfill": "^1.0.3", "@types/jest": "^29.5.12", "@types/luxon": "^3.3.3", "@types/mapbox-gl": "^2.7.17", "@types/node": "^22.10.5", "@types/qs": "^6.9.16", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "@vitest/coverage-v8": "^2.1.9", "autoprefixer": "^10.4.16", "bits-ui": "^1.4.8", "clsx": "^2.1.1", "d3": "^7.8.5", "embla-carousel-svelte": "^8.6.0", "eslint": "^8.52.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-svelte": "^2.34.0", "husky": "^8.0.0", "jest": "^29.7.0", "postcss": "^8.4.31", "prettier": "^3.0.3", "prettier-plugin-svelte": "^3.0.3", "svelte": "^5.28.2", "svelte-check": "^4.2.2", "svelte-jester": "^5.0.0", "svelte-preprocess": "^6.0.3", "tailwind-merge": "^3.2.0", "tailwind-variants": "^1.0.0", "tailwindcss": "^3.3.3", "tailwindcss-animate": "^1.0.7", "ts-jest": "^29.2.4", "tslib": "^2.6.2", "typescript": "^5.2.2", "vite": "^6.3.3", "vitest": "^2.1.9"}, "overrides": {"glob": "^9.0.0", "cross-spawn": "^7.0.5"}, "type": "module"}