FROM node:20-bookworm-slim as builder

# Security Updates
RUN apt update \
    && apt upgrade -y \
    && apt autoremove -y \
    && apt clean \
    && apt autoclean \
    && rm -rf /var/lib/apt/lists/*
RUN npm install -g npm@11.0.0

WORKDIR /home/<USER>/app
COPY . .

RUN chown -R node:node /home/<USER>/app
USER node

RUN npm install

# Run the server after build by default
ENV RUN_SERVER=true

CMD [ "sh", "./docker-entrypoint.sh" ]
