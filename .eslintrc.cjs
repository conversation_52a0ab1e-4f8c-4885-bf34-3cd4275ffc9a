module.exports = {
  root: true,
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:svelte/recommended',
    'prettier'
  ],
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint'],
  parserOptions: {
    sourceType: 'module',
    ecmaVersion: 2020,
    extraFileExtensions: ['.svelte']
  },
  env: {
    browser: true,
    es2017: true,
    node: true
  },
  overrides: [
    {
      files: ['*.svelte'],
      parser: 'svelte-eslint-parser',
      parserOptions: {
        parser: '@typescript-eslint/parser'
      }
    },
    {
      files: ['src/lib/types/*.ts', 'src/lib/utils/*.ts', 'src/lib/config/*.ts'],
      rules: {
        '@typescript-eslint/no-magic-numbers': 'off'
      }
    }
  ],
  rules: {
    '@typescript-eslint/no-empty-function': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
    'svelte/no-inner-declarations': 'off',
    'svelte/infinite-reactive-loop': 'error',
    'svelte/no-store-async': 'error',
    'svelte/valid-prop-names-in-kit-pages': 'error',
    'svelte/require-stores-init': 'error',
    'svelte/no-ignored-unsubscribe': 'error',
    '@typescript-eslint/max-params': ['error', { max: 5 }],
    '@typescript-eslint/no-magic-numbers': ['error', { ignore: [0, 1, -1, 2, 100] }],
    '@typescript-eslint/prefer-for-of': 'error',
    '@typescript-eslint/consistent-type-imports': 'error'
  }
};
