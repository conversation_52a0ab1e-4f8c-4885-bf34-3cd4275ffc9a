import { test, expect } from '@playwright/test';
import { DateTime } from 'luxon';

test.describe('Daily Page', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/forecast/');
    await page.waitForLoadState('networkidle');
  });

  test('As of date is within 2 hours of current time', async ({ page }) => {
    const asOfDateText = await page.textContent('text=/as of/i');
    const [, asOfDate] = asOfDateText?.match(/as of (.+)/i) || [];
    const dateTime = DateTime.fromFormat(asOfDate, 'MMMM dd, h:mma z', {
      zone: process.env.PUBLIC_TIMEZONE
    });

    expect(dateTime.diffNow('hours').hours).toBeLessThanOrEqual(2);
  });

  test('Forecast cards contain next 5 days of data', async ({ page }) => {
    const forecastCards = await page.locator('.forecast-card');
    const cardCount = await forecastCards.count();
    expect(cardCount).toBe(5);

    const today = DateTime.now();
    for (let i = 0; i < 5; i++) {
      const expectedDate = today.plus({ days: i }).toFormat('yyyy-MM-dd');
      const cardDate = await forecastCards.nth(i).locator('.forecast-date').textContent();
      expect(cardDate).toContain(expectedDate);
    }
  });
});
