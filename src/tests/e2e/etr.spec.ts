import { test, expect } from '@playwright/test';
import { DateTime } from 'luxon';

test.describe('ETR Page', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/etr/');
    await page.waitForLoadState('networkidle');
  });

  test('As of date is within 1 hour of current time', async ({ page }) => {
    const asOfDateText = await page.textContent('text=/as of/i');
    const [, asOfDate] = asOfDateText?.match(/as of (.+)/i) || [];
    const dateTime = DateTime.fromFormat(asOfDate, 'MMMM dd, h:mma z', {
      zone: process.env.PUBLIC_TIMEZONE
    });

    expect(dateTime.diffNow('hours').hours).toBeLessThanOrEqual(1);
  });
});
