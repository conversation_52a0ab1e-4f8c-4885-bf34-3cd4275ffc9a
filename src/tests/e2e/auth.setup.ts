import { test } from '@playwright/test';

const authFile = 'playwright/.auth/user.json';

const username = process.env.KEYCLOAK_USERNAME || '';
const password = process.env.KEYCLOAK_PASSWORD || '';
const baseUrl = process.env.UI_BASE_URL || '';

test('authenticate', async ({ page }) => {
  const requestUrls: string[] = [];

  await page.context().clearCookies();

  await page.goto('/');
  await page.locator('id=username').fill(username);
  await page.locator('id=password').fill(password);

  page.on('request', (request) => {
    requestUrls.push(request.url());
  });

  await page.locator('id=kc-login').click();

  const callBackUrl = requestUrls.find((element) => element.includes(baseUrl));

  if (callBackUrl) {
    await page.goto(callBackUrl);
  }

  await page.context().storageState({ path: authFile });
});
