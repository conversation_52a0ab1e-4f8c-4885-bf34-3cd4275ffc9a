import { test, expect } from '@playwright/test';
import { DateTime } from 'luxon';

test.describe('Hourly Page', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/hourly/');
    await page.waitForLoadState('networkidle');
  });

  test('As of date is within 2 hours of current time', async ({ page }) => {
    const asOfDateText = await page.textContent('text=/as of/i');
    const [, asOfDate] = asOfDateText?.match(/as of (.+)/i) || [];
    const dateTime = DateTime.fromFormat(asOfDate, 'MMMM dd, h:mma z', {
      zone: process.env.PUBLIC_TIMEZONE
    });

    expect(dateTime.diffNow('hours').hours).toBeLessThanOrEqual(2);
  });
});
