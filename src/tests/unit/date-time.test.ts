import { describe, it, expect } from 'vitest';
import { validateDate, validateTime, validatePeriod, formatDuration } from '$lib/utils/date-time';

describe('validateDate', () => {
  it('should return true for a valid date', () => {
    const validDate = '12/31/2022';
    expect(validateDate(validDate)).toBe(true);
  });

  it('should return false for an invalid date', () => {
    const invalidDate = '12/301/2022';
    expect(validateDate(invalidDate)).toBe(false);
  });
});

describe('validateTime', () => {
  it('should return true for a valid time', () => {
    const validTime = '12:30';
    expect(validateTime(validTime)).toBe(true);
  });

  it('should return false for an invalid time', () => {
    const invalidTime = '25:00';
    expect(validateTime(invalidTime)).toBe(false);
  });
});

describe('validatePeriod', () => {
  it('should return true for a valid period', () => {
    const amvValidPeriod = 'AM';
    expect(validatePeriod(amvValidPeriod)).toBe(true);

    const pmValidPeriod = 'PM';
    expect(validatePeriod(pmValidPeriod)).toBe(true);
  });

  it('should return false for an invalid period', () => {
    const invalidPeriod = 'ZZ';
    expect(validatePeriod(invalidPeriod)).toBe(false);
  });
});

describe('formatDuration', () => {
  test('should format milliseconds into days, hours, and minutes', () => {
    expect(formatDuration(90061000)).toBe('1 day, 1 hour, 1 minute');
    expect(formatDuration(86400000)).toBe('1 day');
    expect(formatDuration(90000000)).toBe('1 day, 1 hour');
    expect(formatDuration(3600000)).toBe('1 hour');
    expect(formatDuration(7200000)).toBe('2 hours');
    expect(formatDuration(60000)).toBe('1 minute');
    expect(formatDuration(120000)).toBe('2 minutes');
    expect(formatDuration(3661000)).toBe('1 hour, 1 minute');
    expect(formatDuration(86400000 + 3661000)).toBe('1 day, 1 hour, 1 minute');
    expect(formatDuration(0)).toBe('');
  });

  test('should handle large durations correctly', () => {
    expect(formatDuration(172800000)).toBe('2 days');
    expect(formatDuration(172800000 + 7200000 + 1800000)).toBe('2 days, 2 hours, 30 minutes');
    expect(formatDuration(2592000000)).toBe('30 days');
  });

  test('should handle exactly one day correctly', () => {
    expect(formatDuration(86400000)).toBe('1 day');
  });

  test('should handle exactly one hour correctly', () => {
    expect(formatDuration(3600000)).toBe('1 hour');
  });

  test('should handle exactly one minute correctly', () => {
    expect(formatDuration(60000)).toBe('1 minute');
  });

  test('should handle cases with no remaining hours or minutes correctly', () => {
    expect(formatDuration(172800000)).toBe('2 days');
    expect(formatDuration(3600000)).toBe('1 hour'); // 1 hour exactly
  });
});
