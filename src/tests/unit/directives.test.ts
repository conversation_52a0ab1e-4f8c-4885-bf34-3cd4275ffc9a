import { describe, expect, beforeEach, afterEach, test, vi } from 'vitest';
import { clickOutside } from '$lib/utils/directives';

describe('clickOutside', () => {
  let node: HTMLElement;
  // @ts-expect-error-next-line
  let eventListener: vi.Mock;

  beforeEach(() => {
    node = document.createElement('div');
    eventListener = vi.fn();
    clickOutside(node);
  });

  afterEach(() => {
    document.removeEventListener('click', eventListener);
  });

  test('should call the event listener when a click occurs outside the node', () => {
    document.addEventListener('click', eventListener);

    const event = new MouseEvent('click', { bubbles: true });
    document.dispatchEvent(event);

    expect(eventListener).toHaveBeenCalled();
  });

  test('should not call the event listener when a click occurs inside the node', () => {
    document.addEventListener('click', eventListener);

    const event = new MouseEvent('click', { bubbles: true });
    node.dispatchEvent(event);

    expect(eventListener).not.toHaveBeenCalled();
  });
});
