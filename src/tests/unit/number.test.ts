import { describe, it, expect } from 'vitest';
import { roundFloat, formatNumbers, formatRange } from '$lib/utils/number';

describe('roundFloat', () => {
  it('should round a float number to the specified number of decimal places', () => {
    expect(roundFloat(3.14159, 2)).toBe(3.14);
    expect(roundFloat(3.14159, 3)).toBe(3.142);
    expect(roundFloat(3.14159, 0)).toBe(3);
  });

  it('should round a negative float number to the specified number of decimal places', () => {
    expect(roundFloat(-3.14159, 2)).toBe(-3.14);
    expect(roundFloat(-3.14159, 3)).toBe(-3.142);
    expect(roundFloat(-3.14159, 0)).toBe(-3);
  });

  it('should round a float number with trailing zeros to the specified number of decimal places', () => {
    expect(roundFloat(3.14, 2)).toBe(3.14);
    expect(roundFloat(3.14, 3)).toBe(3.14);
    expect(roundFloat(3.14, 0)).toBe(3);
  });
});

describe('formatNumbers', () => {
  it('should format a number with the specified precision', () => {
    expect(formatNumbers(1234.5678, 2)).toBe('1,234.57');
    expect(formatNumbers(1234.5678, 3)).toBe('1,234.568');
    expect(formatNumbers(1234.5678, 0)).toBe('1,235');
  });

  it('should format undefined as an empty string', () => {
    expect(formatNumbers(undefined, 2)).toBe('-');
  });

  it('should format null as an empty string', () => {
    expect(formatNumbers(null, 2)).toBe('-');
  });
});

describe('formatRange', () => {
  it('should format a range of numbers with the specified precision', () => {
    expect(formatRange(1.234, 5.678, 2)).toBe('1.23 - 5.68');
    expect(formatRange(1.234, 5.678, 3)).toBe('1.234 - 5.678');
    expect(formatRange(1.234, 5.678, 0)).toBe('1 - 6');
    expect(formatRange(1.234, 1.234, 3)).toBe('1.234');
  });

  it('should format undefined values as an empty string', () => {
    expect(formatRange(undefined, 5.678, 2)).toBe('-');
    expect(formatRange(1.234, undefined, 2)).toBe('-');
    expect(formatRange(undefined, undefined, 2)).toBe('-');
  });

  it('should format null values as an empty string', () => {
    expect(formatRange(null, 5.678, 2)).toBe('-');
    expect(formatRange(1.234, null, 2)).toBe('-');
    expect(formatRange(null, null, 2)).toBe('-');
  });
});
