import { describe, it, expect } from 'vitest';
import { getDonutSumAmount, getPercentage } from '$lib/utils/chart';

describe('getDonutSumAmount', () => {
  it('should return the correct sum of values', () => {
    const data = [{ value: 10 }, { value: 20 }, { value: 30 }];
    expect(getDonutSumAmount(data)).toBe(60);
  });

  it('should return 0 if data is empty', () => {
    const data: { value: number }[] = [];
    expect(getDonutSumAmount(data)).toBe(0);
  });

  it('should handle negative numbers', () => {
    const data = [{ value: -10 }, { value: 20 }, { value: -30 }];
    expect(getDonutSumAmount(data)).toBe(-20);
  });
});

describe('getPercentage', () => {
  it('should return the correct percentage value', () => {
    const data = { value: 25 };
    const dataList = [{ value: 10 }, { value: 20 }, { value: 30 }];

    expect(getPercentage(data, dataList)).toBe('42%');
  });

  it('should return "100%" if the sum of dataList is 0', () => {
    const data = { value: 25 };
    const dataList: { value: number }[] = [];

    expect(getPercentage(data, dataList)).toBe('100%');
  });

  it('should correctly handle data when sum is not zero', () => {
    const data = { value: 50 };
    const dataList = [{ value: 25 }, { value: 25 }];

    expect(getPercentage(data, dataList)).toBe('100%');
  });

  it('should handle when data.value is 0', () => {
    const data = { value: 0 };
    const dataList = [{ value: 25 }, { value: 25 }];

    expect(getPercentage(data, dataList)).toBe('0%');
  });
});
