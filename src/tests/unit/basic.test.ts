import { describe, it, expect } from 'vitest';
import { getSortedKeysSubset, convertArrayToObject } from '$lib/utils/basic';

describe('getSortedKeysSubset', () => {
  it('should return a subset of sorted keys from an object', () => {
    const obj = { a: 1, b: 2, c: 3 };
    const length = 2;
    const result = getSortedKeysSubset(obj, length);
    expect(Object.keys(result)).toEqual(['a', 'b']);
  });

  it('should return an empty object if length is 0', () => {
    const obj = { a: 1, b: 2, c: 3 };
    const length = 0;
    const result = getSortedKeysSubset(obj, length);
    expect(result).toEqual({});
  });

  it('should return the entire object if length is greater than the number of keys', () => {
    const obj = { a: 1, b: 2, c: 3 };
    const length = 5;
    const result = getSortedKeysSubset(obj, length);
    expect(result).toEqual(obj);
  });
});

describe('convertArrayToObject', () => {
  it('should convert an array to an object using the specified key', () => {
    const array = [
      { id: 1, name: '<PERSON>' },
      { id: 2, name: 'Jane' },
      { id: 3, name: 'Bob' }
    ];
    const key = 'id';
    const result = convertArrayToObject(array, key);
    expect(result).toEqual({
      1: { id: 1, name: 'John' },
      2: { id: 2, name: 'Jane' },
      3: { id: 3, name: 'Bob' }
    });
  });

  it('should handle empty array', () => {
    const array: any[] = [];
    const key = 'id';
    const result = convertArrayToObject(array, key);
    expect(result).toEqual({});
  });

  it('should handle array with duplicate keys', () => {
    const array = [
      { id: 1, name: 'John' },
      { id: 2, name: 'Jane' },
      { id: 1, name: 'Bob' }
    ];
    const key = 'id';
    const result = convertArrayToObject(array, key);
    expect(result).toEqual({
      1: { id: 1, name: 'Bob' },
      2: { id: 2, name: 'Jane' }
    });
  });
});
