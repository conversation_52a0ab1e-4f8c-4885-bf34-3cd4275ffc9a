import { describe, it, expect } from 'vitest';
import { getClosestTimestamp } from '$lib/utils/map';
import { type AggregateMetricRecordWithRegion } from '$lib/types';

describe('getClosestTimestamp', () => {
  it('should return the closest timestamp to the given currentFrameTime', () => {
    const systemOverviewData: Record<string, AggregateMetricRecordWithRegion[]> = {
      '1000': [
        {
          region: 'region1',
          cumulativeAffectedCustomers: 100,
          cumulativeCustomersRestored: 50,
          cumulativeIncidents: 10,
          cumulativeRestorations: 5,
          restoredPerWorker: 1.2,
          timestamp: '1000',
          totalAffectedCustomerRestorations: 30,
          totalAffectedCustomers: 100,
          totalIncidents: 10,
          totalResources: 15,
          totalRestorations: 5,
          uniqueAffectedCustomers: 90,
          uniqueIncidentsCount: 9,
          windGustThresholds: { over20mph: 1, over30mph: 2, over40mph: 3 },
          windGustKmh: 45,
          averageWindSpeed: 20,
          totalRawIncidents: 10,
          totalRawAffectedCustomers: 150
        }
      ],
      '2000': [
        {
          region: 'region2',
          cumulativeAffectedCustomers: 200,
          cumulativeCustomersRestored: 100,
          cumulativeIncidents: 20,
          cumulativeRestorations: 10,
          restoredPerWorker: 1.4,
          timestamp: '2000',
          totalAffectedCustomerRestorations: 60,
          totalAffectedCustomers: 200,
          totalIncidents: 20,
          totalResources: 30,
          totalRestorations: 10,
          uniqueAffectedCustomers: 180,
          uniqueIncidentsCount: 18,
          windGustThresholds: { over20mph: 1, over30mph: 2, over40mph: 3 },
          windGustKmh: 55,
          averageWindSpeed: 25,
          totalRawIncidents: 10,
          totalRawAffectedCustomers: 150
        }
      ],
      '3000': [
        {
          region: 'region3',
          cumulativeAffectedCustomers: 300,
          cumulativeCustomersRestored: 150,
          cumulativeIncidents: 30,
          cumulativeRestorations: 15,
          restoredPerWorker: 1.6,
          timestamp: '3000',
          totalAffectedCustomerRestorations: 90,
          totalAffectedCustomers: 300,
          totalIncidents: 30,
          totalResources: 45,
          totalRestorations: 15,
          uniqueAffectedCustomers: 270,
          uniqueIncidentsCount: 27,
          windGustThresholds: { over20mph: 1, over30mph: 2, over40mph: 3 },
          windGustKmh: 65,
          averageWindSpeed: 30,
          totalRawIncidents: 10,
          totalRawAffectedCustomers: 150
        }
      ]
    };
    const currentFrameTime = 2500;

    const closestTimestamp = getClosestTimestamp(systemOverviewData, currentFrameTime);

    expect(closestTimestamp).toBe('2000');
  });

  it('should return null if no timestamps are available', () => {
    const systemOverviewData: Record<string, AggregateMetricRecordWithRegion[]> = {};
    const currentFrameTime = 2500;

    const closestTimestamp = getClosestTimestamp(systemOverviewData, currentFrameTime);

    expect(closestTimestamp).toBeNull();
  });
});
