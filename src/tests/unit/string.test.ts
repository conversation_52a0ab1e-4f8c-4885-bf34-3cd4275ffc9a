import { describe, it, expect } from 'vitest';
import { capitalize, titleCase, filterNonNull } from '$lib/utils/string';

describe('capitalize', () => {
  it('should capitalize the first letter of a string', () => {
    expect(capitalize('hello')).toBe('Hello');
    expect(capitalize('world')).toBe('World');
  });

  it('should return an empty string if the input is null', () => {
    expect(capitalize(null)).toBe('');
  });
});

describe('titleCase', () => {
  it('should capitalize the first letter of each word in a string', () => {
    expect(titleCase('hello world')).toBe('Hello World');
  });

  it('should return an empty string if the input is null', () => {
    expect(titleCase(null)).toBe('');
  });
});

describe('filterNonNull', () => {
  it('should filter out null values from an object', () => {
    const obj = {
      name: '<PERSON>',
      age: 30,
      address: null,
      occupation: 'Engineer'
    };

    const filteredObj = filterNonNull(obj);

    expect(filteredObj).toEqual({
      name: '<PERSON>',
      age: 30,
      occupation: 'Engineer'
    });
  });

  it('should not filter out 0 or false values', () => {
    const obj = {
      value: 0,
      enabled: false
    };

    const filteredObj = filterNonNull(obj);

    expect(filteredObj).toEqual({
      value: 0,
      enabled: false
    });
  });

  it('should return an empty object if all values in the input object are null', () => {
    const obj = {
      prop1: null,
      prop2: null,
      prop3: null
    };

    const filteredObj = filterNonNull(obj);

    expect(filteredObj).toEqual({});
  });

  it('should return an empty object if all values in the input object are undefined', () => {
    const obj = {
      prop1: undefined,
      prop2: undefined,
      prop3: undefined
    };

    const filteredObj = filterNonNull(obj);

    expect(filteredObj).toEqual({});
  });

  it('should return an empty object if all values in the input object are empty strings', () => {
    const obj = {
      prop1: '',
      prop2: '',
      prop3: ''
    };

    const filteredObj = filterNonNull(obj);

    expect(filteredObj).toEqual({});
  });

  it('should return the same object if there are no null values', () => {
    const obj = {
      name: 'John',
      age: 30,
      occupation: 'Engineer'
    };

    const filteredObj = filterNonNull(obj);

    expect(filteredObj).toEqual(obj);
  });
});
