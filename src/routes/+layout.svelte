<script lang="ts">
  import { onMount, type Snippet } from 'svelte';
  import { Settings } from 'luxon';
  import { base } from '$app/paths';
  import {
    getAppConfig,
    getComponentConfigs,
    getDisplayConfigs,
    getEventLevels,
    getFeatureFlags,
    getRegions,
    getRoleConfigs,
    getThemeConfigs
  } from '$lib/api';
  import { signIn, initAuthClient } from '$lib/authentication';
  import { Header, SideBar, Banner, Toasts } from '$lib/components';
  import * as Alert from '$lib/components/ui/alert';
  import { Button } from '$lib/components/ui/button';
  import { isDarkMode } from '$lib/stores/theme';
  import store from '$lib/stores/app.svelte';
  import {
    authenticationMethod,
    client,
    showOutagesBanner,
    timezone,
    warningMessage
  } from '$lib/stores/constants';
  import logo from '$lib/images/logo.png';
  import logoLight from '$lib/images/logo-light.png';
  import { Loader } from '$lib/components';
  import { setTheme, setCustomTheme } from '$lib/utils';
  import { startClarity } from '$lib/utils/clarity';
  import './styles.css';

  interface Props {
    children?: Snippet;
  }

  let { children }: Props = $props();

  const SCREEN_BREAKPOINT = 767;

  const showSplashPage = authenticationMethod === 'azure';

  let errorMessage = $state<string>('');
  let innerWidth = $state<number>(0);
  let isExpanded = $state<boolean>(false);
  let isLoading = $state<boolean>(true);

  let isDesktopView = $derived<boolean>(innerWidth > SCREEN_BREAKPOINT);

  $effect(() => {
    if (store.isAuthenticated) {
      initialize();
    }
  });

  $effect(() => {
    if (store.themeConfig && Object.keys(store.themeConfig).length) {
      setCustomTheme(store.themeConfig, $isDarkMode);
    }
  });

  onMount(async () => {
    startClarity();
    initAuthClient();
    if (!showSplashPage) signIn();
    isExpanded = localStorage.getItem('isExpanded') === 'false' ? false : true;
    setTheme();
  });

  async function initialize() {
    Settings.defaultZone = timezone;
    try {
      await Promise.allSettled([
        fetchFeatureFlags(),
        fetchEventLevels(),
        fetchComponentsConfig(),
        fetchAppConfig(),
        fetchRegions(),
        fetchDisplayConfig(),
        fetchRoleConfig(),
        fetchThemeConfig()
      ]);
      isLoading = false;
    } catch (e) {
      console.error(e);
    }
  }

  async function fetchRegions() {
    try {
      const rs = await getRegions();
      store.regions = rs.sort((a, b) => a.name.localeCompare(b.name));
      store.territories = store.regions.filter((i) => i.hasSubregions);
    } catch (err: any) {
      console.error(err);
    }
  }

  async function fetchFeatureFlags() {
    try {
      const ff = await getFeatureFlags();
      store.featureFlags = ff;
    } catch (err: any) {
      errorMessage = 'Please reload the page';
      console.error(err);
    }
  }

  async function fetchEventLevels() {
    try {
      const levels = await getEventLevels();
      store.eventLevels = levels.sort((a, b) => +a.eventCode - +b.eventCode);
    } catch (err: any) {
      console.error(err);
    }
  }

  async function fetchComponentsConfig() {
    try {
      const config = await getComponentConfigs();
      store.componentsConfig = config;
    } catch (err: any) {
      console.error(err);
    }
  }

  async function fetchDisplayConfig() {
    try {
      const config = await getDisplayConfigs();
      store.displayConfig = config;
    } catch (err: any) {
      console.error(err);
    }
  }

  async function fetchRoleConfig() {
    try {
      const config = await getRoleConfigs();
      store.roleConfig = config;
    } catch (err: any) {
      console.error(err);
    }
  }

  async function fetchThemeConfig() {
    try {
      const config = await getThemeConfigs();
      store.themeConfig = config;
    } catch (err: any) {
      console.error(err);
    }
  }

  async function fetchAppConfig() {
    try {
      const config = await getAppConfig();
      store.appConfig = config;
    } catch (err: any) {
      console.error(err);
    }
  }

  function onToggle() {
    const state = !isExpanded;
    isExpanded = state;
    localStorage.setItem('isExpanded', state.toString());
  }
</script>

<svelte:window bind:innerWidth />

<Toasts />

{#if store.isAuthenticated}
  <div class="app">
    {#if isLoading}
      <Loader />
    {:else}
      <SideBar {isExpanded} {onToggle} />
      <main class={isExpanded ? 'expanded' : 'collapsed'}>
        {#if warningMessage}
          <div class="mt-md mx-lg mobile:mt-20">
            <Alert.Root variant="warning">
              <Alert.Title>{warningMessage}</Alert.Title>
            </Alert.Root>
          </div>
        {/if}
        <Header {isDesktopView} />
        <div
          class={`content px-content pb-content mobile:p-content-mobile ${!warningMessage && 'mobile:mt-16'}`}
        >
          {#if errorMessage}
            <Alert.Root variant="info">
              <Alert.Title>System Updating:</Alert.Title>
              <Alert.Description>{errorMessage}</Alert.Description>
            </Alert.Root>
          {:else}
            {#if showOutagesBanner && !isDesktopView}
              <Banner />
            {/if}
            {@render children?.()}
          {/if}
        </div>
      </main>
    {/if}
  </div>
{:else if showSplashPage}
  <div class="splash-page">
    <img src="{base}/images/spear_logo_small.png" alt="spear logo" height="70px" width="70px" />
    <h1 class="text-6xl">SPEAR</h1>
    <div class="flex justify-center items-center gap-9 mb-10">
      <img
        src="{base}/images/{client}_{$isDarkMode ? 'header_logo_dark' : 'header_logo_light'}.png"
        alt="{client} logo"
        width="194px"
        class="!m-0"
      />
      <img src={$isDarkMode ? logo : logoLight} alt="esource logo" width="104px" class="!m-0" />
    </div>
    <Button variant="link" class="text-secondary font-bold text-md" onclick={signIn}>Sign On</Button
    >
  </div>
{/if}

<style>
  .splash-page {
    padding-top: 100px;
    position: absolute;
    top: 0;
    bottom: 0;
    width: 100%;
    text-align: center;
    background-color: var(--color-background);
  }

  .splash-page h1 {
    font-weight: 100;
    margin: 8px 0 24px;
  }

  img {
    margin: 0 auto;
  }

  .app {
    display: flex;
    flex-direction: column;
    height: 100vh;
    min-height: 100vh;
    position: relative;
  }

  main {
    display: flex;
    flex-direction: column;
    flex: 1 1 0;
    width: 100%;
  }

  .content {
    flex: 1 1 0%;
    min-height: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .expanded {
    margin-left: calc(var(--size-sidebar-expanded));
    width: calc(100% - var(--size-sidebar-expanded));
  }

  .collapsed {
    margin-left: calc(var(--size-sidebar));
    width: calc(100% - var(--size-sidebar));
  }

  @media (max-width: 767px) {
    main {
      margin-left: 0 !important;
      width: 100% !important;
    }
  }
</style>
