<script lang="ts">
  import { goto } from '$app/navigation';
  import { base } from '$app/paths';
  import { page } from '$app/stores';
  import {
    getDailyHourlyPredictionSummary,
    getDailyEvents,
    getHistoricalPredictions,
    getHistoricalDonutChartData,
    getMapConfigs,
    getPSSessionId,
    getRegionalWeatherData,
    getSessionPredictions,
    getWSSessionId
  } from '$lib/api';
  import {
    Ag<PERSON>hart,
    ButtonGroup,
    DateRangePicker,
    EmptyForecastCard,
    EmptyPointForecastCard,
    ForecastCard,
    ForecastCardMobile,
    PointForecastCard,
    PointForecastCardMobile,
    DailyMap,
    Table,
    TogglingInfoModal,
    ForecastCardLoading,
    PointForecastCardLoading,
    Tooltip,
    DesktopView,
    MobileView
  } from '$lib/components';
  import * as Alert from '$lib/components/ui/alert';
  import { Card } from '$lib/components/ui/card';
  import { Button } from '$lib/components/ui/button';
  import { Label } from '$lib/components/ui/label';
  import {
    ACTUALS_STROKE_COLOR,
    OUTAGES_STROKE_COLOR,
    tooltipRenderer,
    customFormatter,
    mobileViewTooltipRenderer,
    LINECHART_X_AXIS_FORMAT_MONTHLY
  } from '$lib/config/chart';
  import { DEFAULT_GRID_OPTIONS, getForecastHistoricalColumns } from '$lib/config/table';
  import { isDarkMode } from '$lib/stores/theme';
  import store from '$lib/stores/app.svelte';
  import {
    isSystemic,
    showPointForecast,
    showProbabilisticForecast,
    timezoneAbbr
  } from '$lib/stores/constants';
  import type {
    CustomTooltipRendererParams,
    DailyEvent,
    DailyHourlyValue,
    DailyPredictedSessionData,
    HistoricalPredictedSessions,
    HistoricalPredictedSessionsWithDate,
    MapConfig,
    RegionalWeather,
    SessionPrediction,
    SpatialData
  } from '$lib/types';
  import { Components, FeatureFlag, PageModes } from '$lib/types';
  import {
    capitalize,
    convertArrayToObject,
    formatNumbers,
    getSortedKeysSubset,
    validateRoleRouteAccess
  } from '$lib/utils';
  import { formatAsOfTimestamp, formatDate, getThreatLevelCrossLines } from '$lib/config/utils';
  import { DateTime } from 'luxon';
  import { onMount } from 'svelte';
  import type { AgAxisLabelFormatterParams, AgNodeClickEvent } from 'ag-charts-enterprise';

  type DonutChartData = {
    label: string;
    value: number;
  };

  const DAY_OF = 0;
  const ONE_DAY = 1;
  const TWO_DAY = 2;
  const THREE_DAY = 3;
  const FOUR_DAY = 4;
  const HTTP_STATUS_FORBIDDEN = 403;
  const DAYS_TO_SHOW = 5;
  const Y_AXIS_CUSHION = 25;
  const INDEX_DAYS = ['Day Of', '1 Day', '2 Day', '3 Day', '4 Day'];

  const validationFields =
    store.componentsConfig?.[Components.FORECAST_HISTORICAL_VALIDATION]?.validations ?? [];
  const showActiveIncidents = validationFields.includes('actualIncidents');

  let chartData = $state<HistoricalPredictedSessionsWithDate[]>([]);
  let errorMessage = $state<string>('');
  let isLoading = $state<boolean>(true);
  let isLoadingMetrics = $state<boolean>(true);
  let rowData = $state<HistoricalPredictedSessions[]>([]);
  let selectedSessionDate = $state<string | null>(null);

  let selectedIndex = $state<number>(0);

  let sStartDate = $state<DateTime>(DateTime.now().minus({ months: 2 }));
  let startDate = $state<DateTime>(DateTime.now().minus({ months: 2 }));
  let sEndDate = $state<DateTime>(DateTime.now());
  let endDate = $state<DateTime>(DateTime.now());

  let selectedDay = $state<string | null>(null);

  let dailyHourlyPredictions = $state<DailyHourlyValue[]>([]);
  let dailySession = $state<DailyPredictedSessionData | undefined>();
  let sessionPredictions = $state<SessionPrediction[]>([]);

  let dailySystemEvents = $state<DailyEvent[]>([]);
  let systemWeatherData = $state<RegionalWeather[]>([]);

  // Controls
  let activePageMode = $state<PageModes>(PageModes.weather);
  let showDualView = $state<boolean>(false);

  // Spatial View
  let dailyEvents = $state<DailyEvent[]>([]);
  let isPointForecastView = showPointForecast && !showProbabilisticForecast;
  let mapConfigs = $state<MapConfig[]>([]);
  let selectedMapIndex = $state<0 | 1>(0);
  let spatialData = $state<SpatialData[]>([]);
  let weatherData = $state<RegionalWeather[]>([]);

  let performanceChartSelectedKeys = $state<number[]>([]);

  let donutChartData = $state<DonutChartData[]>([
    { label: 'Same Zone', value: 0 },
    { label: 'Within 1 Zone', value: 0 },
    { label: 'Within 2 Zones', value: 0 },
    { label: 'Other', value: 0 }
  ]);

  let groupedSystemDailyEvents = $derived(
    getSortedKeysSubset(convertArrayToObject(dailySystemEvents, 'date'), DAYS_TO_SHOW)
  );
  let groupedSystemWeatherData = $derived(
    getSortedKeysSubset(convertArrayToObject(systemWeatherData, 'day'), DAYS_TO_SHOW)
  );
  let isInvalidDateRange = $derived(
    startDate.startOf('day') >= endDate.minus({ days: 6 }).startOf('day')
  );
  let isSearchDisabled = $derived(
    sStartDate.hasSame(startDate, 'day') && sEndDate.hasSame(endDate, 'day')
  );
  let maxDates = $derived(findMaxDates(chartData));
  let nextDayDate = $derived<Date>(
    selectedDay ? DateTime.fromISO(selectedDay).plus({ days: 1 }).toJSDate() : new Date()
  );
  let selectedDayDate = $derived<Date>(
    selectedDay ? DateTime.fromISO(selectedDay).toJSDate() : new Date()
  );
  let threatLevelCrossLines = $derived(getThreatLevelCrossLines('system', 'system', $isDarkMode));
  let yAxisDomainMax = $derived(findYAxisDomainMax(chartData));
  // Spatial View
  let filteredDailyEvents = $derived(dailyEvents.filter(({ date }) => date === selectedDay));
  let filteredWeatherData = $derived(weatherData.filter(({ day }) => day === selectedDay));

  $effect(() => {
    spatialData = formatSpatialData(filteredDailyEvents, filteredWeatherData);
  });

  onMount(() => {
    if (!validateRouteEntry() || !validateRoleRouteAccess($page.route.id!)) goto(`${base}/`);
    const sessionDate = $page.url.searchParams.get('sessionDate');
    if (sessionDate) onSelectDailySession(sessionDate);
    fetchHistoricalForecastData();
    fetchDonutChartData();
    setupTableCallbacks();
  });

  const donutLabelFormatter = (params: any) => {
    return params.value.toString();
  };

  const donutTooltipRenderer = (params: any) => {
    return {
      title: params.datum.label,
      content: params.datum.value.toString()
    };
  };

  function validateRouteEntry() {
    return store.featureFlags?.[FeatureFlag.forecastRetrospective];
  }

  async function fetchHistoricalForecastData() {
    rowData = [];

    try {
      const start = startDate.toFormat('yyyy-MM-dd');
      const end = endDate.toFormat('yyyy-MM-dd');
      const { sessions } = await getHistoricalPredictions(start, end, selectedIndex);
      rowData = sessions;
      chartData = sessions.map((s) => ({
        ...s,
        date: DateTime.fromISO(s.sessionDate).toJSDate()
      }));
    } catch (e: any) {
      if (e.response && e.response.status === HTTP_STATUS_FORBIDDEN) {
        errorMessage =
          'Current user is unauthorized to view this data. Please use the Report Issue button to request access.';
      } else {
        errorMessage = 'Failed to load historical forecast data.';
      }
    } finally {
      isLoading = false;
    }
  }

  async function fetchDonutChartData() {
    try {
      const start = formatDate(startDate.toJSDate());
      const end = formatDate(endDate.toJSDate());

      const data = await getHistoricalDonutChartData(start, end, selectedIndex);

      donutChartData = [
        { label: 'Same Zone', value: data.match },
        { label: 'Within 1 Zone', value: data.off1 },
        { label: 'Within 2 Zones', value: data.off2 },
        { label: 'Other', value: data.other }
      ];
    } catch (error) {
      console.error('Failed to fetch donut chart data:', error);
    } finally {
      isLoadingMetrics = false;
    }
  }

  function combinedOutageTooltipRenderer(params: CustomTooltipRendererParams) {
    const { outageRangeHigh, outageRangeLow, totalOutages, actualIncidents, sessionDate } =
      params.datum;

    let content = `
    <div class="ag-chart-tooltip-title text-center">
      ${sessionDate}
    </div>
    <div class="ag-chart-tooltip-content !p-0 !w-[225px]">
  `;

    if (showActiveIncidents && actualIncidents !== undefined) {
      content += `
        <div class="flex items-center justify-between gap-2 p-2 border-b border-solid border-gray-100">
        <div class="flex items-center gap-2">
          <span class="w-4 h-4 rounded-full" style="background-color: ${ACTUALS_STROKE_COLOR};"></span>
          <span>Actual ${store.capitalizedOutagesLabel}:</span>
        </div>
        <span>${formatNumbers(actualIncidents)}</span>
      </div>
    `;
    }

    if (
      validationFields.includes('outageRangeHigh') ||
      validationFields.includes('outageRangeLow') ||
      validationFields.includes('totalOutages')
    ) {
      content += `
      <div class="flex flex-col p-2 border-b border-solid border-gray-100"">
        <div class="flex items-center gap-2">
          <span class="inline-block w-4 h-4 rounded-full" style="background-color: ${OUTAGES_STROKE_COLOR};"></span>
          <span>Forecasted ${store.capitalizedOutagesLabel}</span>
        </div>
    `;

      if (validationFields.includes('outageRangeHigh') && outageRangeHigh !== undefined) {
        content += `
        <div class="flex justify-between w-full">
          <span>High:</span> <span>${formatNumbers(outageRangeHigh)}</span>
        </div>
      `;
      }

      if (validationFields.includes('totalOutages') && totalOutages !== undefined) {
        content += `
        <div class="flex justify-between w-full">
          <span>Point Estimate:</span> <span>${formatNumbers(totalOutages)}</span>
        </div>
      `;
      }

      if (validationFields.includes('outageRangeLow') && outageRangeLow !== undefined) {
        content += `
        <div class="flex justify-between w-full">
          <span>Low:</span> <span>${formatNumbers(outageRangeLow)}</span>
        </div>
      `;
      }

      content += '</div>';
    }

    content += `
        <button onclick="window.onSelectDailySession('${sessionDate}')" class="text-blue-500 underline text-center block w-full my-2">
          View Historical Session
        </button>
      `;

    content += '</div>';

    return content;
  }

  async function onSelectDailySession(date: string) {
    const adjustedDate = DateTime.fromISO(date)
      .minus({ days: selectedIndex })
      .toFormat('yyyy-MM-dd');
    isLoading = true;
    selectedDay = adjustedDate;
    $page.url.searchParams.set('sessionDate', adjustedDate);
    goto(`?${$page.url.searchParams.toString()}`, { replaceState: true });

    try {
      const { session: dailySessionId, latestDate } = await getPSSessionId('daily', adjustedDate);
      const { session: dailyZoneSessionId } = await getPSSessionId('daily_zone', adjustedDate);
      const { session: hourlyZoneSessionId } = await getPSSessionId('hourly_zone', adjustedDate);
      const promises = [
        fetchSpatialData(adjustedDate),
        fetchDailyEvents(dailySessionId, dailyZoneSessionId),
        fetchDailyHourlyPredictions(hourlyZoneSessionId)
      ];
      if (!isPointForecastView) promises.push(fetchPredictionData(dailySessionId));
      await Promise.all(promises);
      selectedSessionDate = latestDate;
      errorMessage = '';
    } catch (e) {
      errorMessage = 'Failed to retrieve historical session data.';
    } finally {
      isLoading = false;
    }
  }

  async function fetchPredictionData(sessionId: string) {
    try {
      sessionPredictions = await getSessionPredictions(sessionId);
      selectedDay = sessionPredictions[0].date;
    } catch (e) {
      console.error(e);
    }
  }

  async function fetchDailyHourlyPredictions(sessionId: string) {
    try {
      dailySession = await getDailyHourlyPredictionSummary(sessionId);
      dailyHourlyPredictions = dailySession.dailyHourlyValues
        .slice(0, DAYS_TO_SHOW)
        .flatMap((d) => d.values)
        .map((i) => ({ ...i, targetDate: new Date(i.targetDate) }));
    } catch (e) {
      console.error(e);
    }
  }

  async function fetchSpatialData(date: string) {
    try {
      mapConfigs = await getMapConfigs();
      const dailyZoneSessionId = await getWSSessionId('daily_zone', date);
      weatherData = await getRegionalWeatherData(dailyZoneSessionId, 'daily_zone');

      if (isPointForecastView) {
        const dailySessionId = await getWSSessionId('daily', date);
        systemWeatherData = await getRegionalWeatherData(dailySessionId, 'daily');
      }
    } catch (e) {
      console.error(e);
    }
  }

  async function fetchDailyEvents(dailySessionId: string, dailyZoneSessionId: string) {
    try {
      // Needs daily_zone session ID for system=false
      const dailyRegionalEvents = await getDailyEvents(dailyZoneSessionId, false);
      // Needs daily session ID for system=true
      dailySystemEvents = await getDailyEvents(dailySessionId, true);
      dailyEvents = [...(dailyRegionalEvents ?? []), ...(dailySystemEvents ?? [])];
    } catch (e) {
      console.error(e);
    }
  }

  function formatSpatialData(events: DailyEvent[], weather: RegionalWeather[]) {
    const eventsObj = events.reduce((acc: Record<string, DailyEvent>, event) => {
      acc[event.territory] = event;
      return acc;
    }, {});
    const weatherObj = weather.reduce((acc: Record<string, RegionalWeather>, event) => {
      acc[event.displayName] = event;
      return acc;
    }, {});
    return Object.entries(weatherObj).map(([key, value]) => {
      const outages = eventsObj[key]?.outages ?? 0;

      return {
        name: value.name,
        attributes: {
          ...value.weatherAttributes,
          outages,
          customersAffected: eventsObj[key]?.customersAffected ?? 0,
          threatLevel:
            store.eventLevels.find(
              ({ range, view, name }) =>
                range?.length &&
                Math.round(outages) >= range[0] &&
                Math.round(outages) <= range[1] &&
                name === value.name &&
                view === (isSystemic ? 'system' : 'workgroup')
            )?.category ?? ''
        }
      };
    });
  }

  function findMaxDates(data: HistoricalPredictedSessionsWithDate[]) {
    let maxActualIncidentsDate: string | null = null;
    let maxTotalOutagesDate: string | null = null;

    data.forEach((item) => {
      if (item.actualIncidents > 0) {
        if (!maxActualIncidentsDate || item.date > new Date(maxActualIncidentsDate)) {
          maxActualIncidentsDate = item.sessionDate;
        }
      }

      if (item.totalOutages > 0) {
        if (!maxTotalOutagesDate || item.date > new Date(maxTotalOutagesDate)) {
          maxTotalOutagesDate = item.sessionDate;
        }
      }
    });

    return {
      actualIncidents: maxActualIncidentsDate ? maxActualIncidentsDate : 'N/A',
      totalOutages: maxTotalOutagesDate ? maxTotalOutagesDate : 'N/A'
    };
  }

  function findYAxisDomainMax(data: HistoricalPredictedSessionsWithDate[]) {
    const highestEventLevelValue =
      store.eventLevels?.filter(({ range }) => !!range).at(-1)?.range[0] ?? 0;
    const highestDataPointValue = data.reduce((max, item) => {
      return Math.max(max, item.actualIncidents, item.outageRangeHigh);
    }, 0);
    return Math.max(highestDataPointValue, highestEventLevelValue) + Y_AXIS_CUSHION;
  }

  function setupTableCallbacks() {
    window.onSelectDailySession = (date: string) => {
      onSelectDailySession(date);
    };
  }

  async function onFetchForecastData() {
    await fetchHistoricalForecastData();
    await fetchDonutChartData();
    sStartDate = startDate;
    sEndDate = endDate;
  }
</script>

<svelte:head>
  <title>Forecast Retrospective</title>
  <meta name="Storm Forecast Retrospective" content="Retrospective" />
</svelte:head>

<div class="forecast-retrospective flex flex-col h-full gap-sm">
  {#if errorMessage}
    <Alert.Root variant="error">
      <Alert.Title>Error:</Alert.Title>
      <Alert.Description>{errorMessage}</Alert.Description>
    </Alert.Root>
  {/if}
  {#if selectedSessionDate}
    <span class="flex text-accentText text-xs">
      As of {formatAsOfTimestamp(selectedSessionDate)}
    </span>

    <div class="flex items-center mb-2 gap-md">
      <Button
        size="icon"
        class="rounded-full"
        onclick={() => {
          selectedSessionDate = null;
          selectedDay = null;
          $page.url.searchParams.delete('sessionDate');
          goto(`${base}/forecast-retrospective`);
        }}
        tooltip="Back to Forecast Retrospective"
      >
        <span class="material-icons">arrow_back</span>
      </Button>
      <h2 class="text-lg">
        Daily Forecast {formatDate(selectedSessionDate)}
      </h2>
    </div>

    <div class="flex flex-col gap-3 flex-1 min-h-[{isPointForecastView ? '569' : '504'}px]">
      {#if isPointForecastView}
        <MobileView class="w-full">
          {#if isLoading}
            <PointForecastCardLoading class="w-full" />
          {:else if Object.keys(groupedSystemWeatherData).length}
            <PointForecastCardMobile
              selectedDay={selectedDay!}
              {groupedSystemDailyEvents}
              {groupedSystemWeatherData}
              onDayChange={(date) => (selectedDay = date)}
            >
              <DailyMap
                {mapConfigs}
                {spatialData}
                selectedIndex={selectedMapIndex}
                loading={isLoading}
              />
            </PointForecastCardMobile>
          {:else}
            <EmptyPointForecastCard class="!w-full" />
          {/if}
        </MobileView>

        <DesktopView>
          <div class="first-row flex flex-wrap justify-between w-full">
            {#if isLoading}
              <PointForecastCardLoading class="forecast-card" />
              <PointForecastCardLoading class="forecast-card" />
              <PointForecastCardLoading class="forecast-card" />
              <PointForecastCardLoading class="forecast-card" />
              <PointForecastCardLoading class="forecast-card" />
            {:else if Object.keys(groupedSystemWeatherData).length}
              {#each Object.entries(groupedSystemWeatherData) as [date, data]}
                <PointForecastCard
                  {date}
                  outageData={groupedSystemDailyEvents[date]}
                  selected={selectedDay === date}
                  weatherData={data as RegionalWeather}
                  onClick={() => (selectedDay = date)}
                  onKeydown={({ key }) => {
                    if (key === 'Enter') selectedDay = date;
                  }}
                />
              {/each}
            {:else}
              <EmptyPointForecastCard />
              <EmptyPointForecastCard />
              <EmptyPointForecastCard />
              <EmptyPointForecastCard />
              <EmptyPointForecastCard />
            {/if}
          </div>
        </DesktopView>
      {:else}
        <MobileView>
          {#if isLoading}
            <ForecastCardLoading class="w-full" />
          {:else if sessionPredictions.length}
            <ForecastCardMobile
              {selectedDay}
              {sessionPredictions}
              onDayChange={(date) => (selectedDay = date)}
            />
          {:else}
            <EmptyForecastCard class="w-full" />
          {/if}
        </MobileView>
        <DesktopView>
          <div class="first-row flex flex-wrap justify-between w-full">
            {#if isLoading}
              <ForecastCardLoading class="forecast-card" />
              <ForecastCardLoading class="forecast-card" />
              <ForecastCardLoading class="forecast-card" />
              <ForecastCardLoading class="forecast-card" />
              <ForecastCardLoading class="forecast-card" />
            {:else if sessionPredictions.length}
              {#each sessionPredictions as prediction}
                <ForecastCard
                  data={prediction}
                  selected={selectedDay === prediction.date}
                  onClick={() => (selectedDay = prediction.date)}
                  onKeydown={({ key }) => {
                    if (key === 'Enter') selectedDay = prediction.date;
                  }}
                />
              {/each}
            {:else}
              <EmptyForecastCard class="forecast-card" />
              <EmptyForecastCard class="forecast-card" />
              <EmptyForecastCard class="forecast-card" />
              <EmptyForecastCard class="forecast-card" />
              <EmptyForecastCard class="forecast-card" />
            {/if}
          </div>
        </DesktopView>
      {/if}

      <MobileView>
        <Card class="w-full min-h-[204px]">
          <AgChart
            loading={isLoading}
            data={dailySession?.dailyHourlyValues?.find((d) => d.day === selectedDay)?.values ?? []}
            options={{
              title: { text: `Forecasted ${store.capitalizedOutagesLabel}`, fontSize: 14 },
              axes: [
                {
                  nice: false,
                  position: 'bottom',
                  title: { text: `Hour (${timezoneAbbr})` },
                  type: 'number',
                  label: {
                    formatter: ({ value }: AgAxisLabelFormatterParams) => {
                      return DateTime.fromFormat(selectedDay!, LINECHART_X_AXIS_FORMAT_MONTHLY)
                        .set({
                          hour: value
                        })
                        .toFormat('ha');
                    }
                  }
                },
                {
                  position: 'left',

                  type: 'number'
                }
              ],
              height: 180,
              legend: { enabled: false },
              padding: { right: 50 },
              series: [
                {
                  tooltip: { renderer: mobileViewTooltipRenderer },
                  type: 'line',
                  xKey: 'hour',
                  yKey: 'totalOutagesSum',
                  yName: `Forecasted ${store.capitalizedOutagesLabel}`
                }
              ]
            }}
          />
        </Card>
      </MobileView>

      <DesktopView>
        <Card class="w-full !h-[18vh] min-h-[170px]">
          <AgChart
            loading={isLoading}
            data={dailyHourlyPredictions}
            options={{
              axes: [
                {
                  crossLines: [
                    {
                      type: 'range',
                      range: [selectedDayDate, nextDayDate],
                      strokeWidth: 0
                    }
                  ],
                  label: {
                    formatter: (value: AgAxisLabelFormatterParams) =>
                      customFormatter(value, LINECHART_X_AXIS_FORMAT_MONTHLY)
                  },
                  nice: true,
                  position: 'bottom',
                  type: 'time'
                },
                {
                  position: 'left',
                  title: { text: `Forecasted \n ${store.capitalizedOutagesLabel}` },
                  type: 'number',
                  min: 0
                }
              ],
              height: 180,
              legend: { enabled: false },
              padding: { right: 50 },
              series: [
                {
                  tooltip: { renderer: tooltipRenderer },
                  type: 'line',
                  xKey: 'targetDate',
                  yKey: 'totalOutagesSum',
                  yName: `Forecasted ${store.capitalizedOutagesLabel}`
                }
              ]
            }}
          />
        </Card>
      </DesktopView>

      <DesktopView class="flex flex-col gap-md h-full">
        <div class="flex justify-between items-start">
          <div class="flex gap-sm">
            {#if store.componentsConfig?.[Components.TERRITORY_SUMMARY]}
              <TogglingInfoModal config={store.componentsConfig[Components.TERRITORY_SUMMARY]} />
            {/if}

            <ButtonGroup bind:value={activePageMode}>
              <Button tooltip={PageModes.weather} value={PageModes.weather}>
                <span class="material-icons">foggy</span>
              </Button>
              <Button disabled={true} tooltip={PageModes.outage} value={PageModes.outage}>
                <span class="material-icons">stacked_line_chart</span>
              </Button>
            </ButtonGroup>
          </div>

          {#if activePageMode === PageModes.weather}
            <DesktopView>
              <Button
                size="icon"
                variant="ghost"
                tooltip={showDualView ? 'Show Single' : 'Show Dual'}
                onclick={() => (showDualView = !showDualView)}
              >
                <span class="material-icons">
                  {showDualView ? 'splitscreen_left' : 'fullscreen_portrait'}
                </span>
              </Button>
            </DesktopView>
          {/if}
        </div>

        <div class="flex w-full h-full gap-sm">
          {#if showDualView}
            <DailyMap
              {mapConfigs}
              {spatialData}
              selectedIndex={selectedMapIndex}
              loading={isLoading}
            />
            <DailyMap
              {mapConfigs}
              {spatialData}
              selectedIndex={selectedMapIndex + 1}
              loading={isLoading}
            />
          {:else}
            <DailyMap
              {mapConfigs}
              {spatialData}
              selectedIndex={selectedMapIndex}
              loading={isLoading}
            />
          {/if}
        </div>
      </DesktopView>
    </div>
  {:else}
    {#if showActiveIncidents}
      <div class="flex flex-col w-full gap-sm xl:flex-row">
        <div class="flex flex-col gap-sm w-full xl:w-[510px]">
          <Card class="xl:w-[510px] w-full">
            <div class="flex flex-col w-full gap-md">
              <div class="flex flex-wrap items-end gap-sm">
                <Tooltip className="shrink-0" align="start">
                  {#snippet content()}
                    <div>
                      Note: The data on this page is based on the {INDEX_DAYS[
                        selectedIndex
                      ].toLowerCase()} forecasts made at {DateTime.fromFormat('8:00AM', 'h:mma', {
                        zone: 'America/New_York'
                      }).toFormat('h:mma ZZZZ')}.

                      {#if selectedIndex === 0}
                        <br />
                        <br />
                        The predicted {store.outageLabel}s shown includes the hours between 12AM and {DateTime.fromFormat(
                          '8:00AM',
                          'h:mma',
                          {
                            zone: 'America/New_York'
                          }
                        )

                          .toFormat('h:mma')}.

                        <br />
                        <br />
                        When selecting a date, the displayed data starts from {DateTime.fromFormat(
                          '8:00AM',
                          'h:mma',
                          {
                            zone: 'America/New_York'
                          }
                        )

                          .toFormat('h:mma')} so slight differences may occur.
                      {/if}

                      <div class="mt-sm border-solid border-t !border-borderColor pt-sm">
                        Data as of:
                        {#if showActiveIncidents}
                          <div>
                            <span class="text-accentText">Actuals:</span>
                            {maxDates.actualIncidents}
                          </div>
                        {/if}
                        <div>
                          <span class="text-accentText">Forecasts:</span>
                          {maxDates.totalOutages}
                        </div>
                      </div>
                    </div>
                  {/snippet}

                  {#snippet trigger()}
                    <span class="material-icons mb-2">info</span>
                  {/snippet}
                </Tooltip>

                <DateRangePicker
                  class="w-[250px]"
                  isInvalid={isInvalidDateRange}
                  invalidText="Min. 1 week required"
                  label="{capitalize(store.outageLabel)} Period"
                  maxDate={DateTime.now().toFormat('MM/dd/yyyy')}
                  valueFrom={startDate.toFormat('MM/dd/yyyy')}
                  valueTo={endDate.toFormat('MM/dd/yyyy')}
                  onChange={(value) => {
                    startDate = DateTime.fromFormat(value.from, 'MM/dd/yyyy');
                    endDate = DateTime.fromFormat(value.to, 'MM/dd/yyyy');
                  }}
                />

                <Button
                  class="ml-auto"
                  disabled={isInvalidDateRange || isSearchDisabled || isLoading}
                  onclick={onFetchForecastData}
                >
                  Search
                </Button>
              </div>

              <div class="flex flex-col mt-1">
                <Label class="text-xs mb-2" id="lead-time-label">Lead Time</Label>
                <MobileView>
                  <ButtonGroup
                    bind:value={selectedIndex}
                    onClick={onFetchForecastData}
                    disabled={isLoading}
                  >
                    <Button value={DAY_OF}>Day Of</Button>
                    <Button value={ONE_DAY}>1d</Button>
                    <Button value={TWO_DAY}>2d</Button>
                    <Button value={THREE_DAY}>3d</Button>
                    <Button value={FOUR_DAY}>4d</Button>
                  </ButtonGroup>
                </MobileView>
                <DesktopView>
                  <ButtonGroup
                    bind:value={selectedIndex}
                    onClick={onFetchForecastData}
                    disabled={isLoading}
                  >
                    <Button value={DAY_OF}>Day Of</Button>
                    <Button value={ONE_DAY}>1 Day</Button>
                    <Button value={TWO_DAY}>2 Day</Button>
                    <Button value={THREE_DAY}>3 Day</Button>
                    <Button value={FOUR_DAY}>4 Day</Button>
                  </ButtonGroup>
                </DesktopView>
              </div>
            </div>
          </Card>

          <Card class="!h-[300px] min-h-[300px] xl:!h-full" loading={isLoadingMetrics}>
            <AgChart
              data={donutChartData}
              palette={{
                fills: $isDarkMode
                  ? ['#218d3e', '#a65600', '#930093', '#850000']
                  : ['#00b04f', '#ffa329', '#ff01ff', '#ff0000']
              }}
              options={{
                series: [
                  {
                    type: 'donut',
                    angleKey: 'value',
                    labelKey: 'label',
                    legendItemKey: 'label',
                    radius: 70,
                    innerRadiusRatio: 0.8,
                    strokeWidth: 0,
                    label: {
                      enabled: true,
                      formatter: donutLabelFormatter,
                      fontSize: 14
                    },
                    innerLabels: [
                      {
                        text: formatNumbers(
                          donutChartData.reduce(
                            (sum, item, index) =>
                              performanceChartSelectedKeys.includes(index) ? sum : sum + item.value,
                            0
                          )
                        ),
                        fontWeight: 'bold',
                        fontSize: 18
                      }
                    ],
                    tooltip: {
                      renderer: donutTooltipRenderer
                    }
                  }
                ],
                title: {
                  text: 'Prediction Performance'
                },
                legend: {
                  listeners: {
                    legendItemClick: ({ itemId }: { itemId: number }) => {
                      const set = new Set(performanceChartSelectedKeys);
                      set.has(itemId) ? set.delete(itemId) : set.add(itemId);
                      performanceChartSelectedKeys = [...set];
                    }
                  }
                }
              }}
            />
          </Card>
        </div>
        <Card
          class="w-full forecast-historical-chart-card min-h-[524px] mobile:!min-h-[300px] {isPointForecastView
            ? 'pf'
            : ''}"
        >
          <AgChart
            className="forecast-historical-chart"
            data={chartData}
            loading={isLoading}
            options={{
              axes: [
                {
                  label: {
                    formatter: (value: AgAxisLabelFormatterParams) =>
                      customFormatter(value, LINECHART_X_AXIS_FORMAT_MONTHLY)
                  },
                  nice: false,
                  position: 'bottom',
                  type: 'time'
                },
                {
                  crossLines: threatLevelCrossLines,
                  max: yAxisDomainMax,
                  min: 0,
                  nice: false,
                  position: 'left',
                  title: { text: `${store.capitalizedOutagesLabel}` },
                  type: 'number',
                  label: {
                    formatter: function (params: AgAxisLabelFormatterParams) {
                      return formatNumbers(params.value);
                    }
                  }
                }
              ],
              listeners: {
                seriesNodeClick: ({ datum }: AgNodeClickEvent<string, { sessionDate: string }>) => {
                  onSelectDailySession(datum.sessionDate);
                }
              },
              title: { text: `Historical Forecast ${store.capitalizedOutagesLabel}` },
              series: [
                {
                  interpolation: { type: 'smooth' },
                  showInLegend: false,
                  tooltip: { enabled: false },
                  type: 'range-area',
                  xKey: 'date',
                  yLowKey: 'outageRangeLow',
                  yHighKey: 'outageRangeHigh'
                },
                {
                  marker: { fill: OUTAGES_STROKE_COLOR, stroke: OUTAGES_STROKE_COLOR },
                  stroke: OUTAGES_STROKE_COLOR,
                  tooltip: {
                    renderer: combinedOutageTooltipRenderer,
                    interaction: {
                      enabled: true
                    }
                  },
                  type: 'line',
                  xKey: 'date',
                  yKey: 'totalOutages',
                  yName: `Forecasted ${store.capitalizedOutagesLabel}`
                },
                {
                  marker: { fill: ACTUALS_STROKE_COLOR, stroke: ACTUALS_STROKE_COLOR },
                  stroke: ACTUALS_STROKE_COLOR,
                  tooltip: {
                    renderer: combinedOutageTooltipRenderer,
                    interaction: {
                      enabled: true
                    }
                  },
                  type: 'line',
                  xKey: 'date',
                  yKey: 'actualIncidents',
                  yName: `Actual ${store.capitalizedOutagesLabel}`
                }
              ],
              navigator: {
                enabled: true
              }
            }}
          />
        </Card>
      </div>
    {:else}
      <div class="filters flex flex-wrap gap-md items-end">
        <DateRangePicker
          class="w-[200px]"
          isInvalid={isInvalidDateRange}
          invalidText="Min. 1 week required"
          label="{capitalize(store.outageLabel)} Period"
          maxDate={DateTime.now().toFormat('MM/dd/yyyy')}
          valueFrom={startDate.toFormat('MM/dd/yyyy')}
          valueTo={endDate.toFormat('MM/dd/yyyy')}
          onChange={(value) => {
            startDate = DateTime.fromFormat(value.from, 'MM/dd/yyyy');
            endDate = DateTime.fromFormat(value.to, 'MM/dd/yyyy');
          }}
        />

        <Button
          disabled={isInvalidDateRange || isSearchDisabled || isLoading}
          onclick={async () => {
            await fetchHistoricalForecastData();
            await fetchDonutChartData();
            sStartDate = startDate;
            sEndDate = endDate;
          }}
        >
          Search
        </Button>

        <div class="flex flex-col mt-1">
          <Label class="text-xs mb-2" id="lead-time-label">Lead Time</Label>
          <MobileView>
            <ButtonGroup
              bind:value={selectedIndex}
              onClick={onFetchForecastData}
              disabled={isLoading}
            >
              <Button value={DAY_OF}>Day Of</Button>
              <Button value={ONE_DAY}>1d</Button>
              <Button value={TWO_DAY}>2d</Button>
              <Button value={THREE_DAY}>3d</Button>
              <Button value={FOUR_DAY}>4d</Button>
            </ButtonGroup>
          </MobileView>
          <DesktopView>
            <ButtonGroup
              bind:value={selectedIndex}
              onClick={onFetchForecastData}
              disabled={isLoading}
            >
              <Button value={DAY_OF}>Day Of</Button>
              <Button value={ONE_DAY}>1 Day</Button>
              <Button value={TWO_DAY}>2 Day</Button>
              <Button value={THREE_DAY}>3 Day</Button>
              <Button value={FOUR_DAY}>4 Day</Button>
            </ButtonGroup>
          </DesktopView>
        </div>
      </div>
      <Card
        class="w-full forecast-historical-chart-card min-h-[524px] mobile:!min-h-[300px] {isPointForecastView
          ? 'pf'
          : ''}"
      >
        <AgChart
          className="forecast-historical-chart"
          data={chartData}
          loading={isLoading}
          options={{
            axes: [
              {
                label: {
                  formatter: (value: AgAxisLabelFormatterParams) =>
                    customFormatter(value, LINECHART_X_AXIS_FORMAT_MONTHLY)
                },
                nice: false,
                position: 'bottom',
                type: 'time'
              },
              {
                crossLines: threatLevelCrossLines,
                max: yAxisDomainMax,
                min: 0,
                nice: false,
                position: 'left',
                title: { text: `${store.capitalizedOutagesLabel}` },
                type: 'number',
                label: {
                  formatter: function (params: AgAxisLabelFormatterParams) {
                    return formatNumbers(params.value);
                  }
                }
              }
            ],
            listeners: {
              seriesNodeClick: ({ datum }: AgNodeClickEvent<string, { sessionDate: string }>) => {
                onSelectDailySession(datum.sessionDate);
              }
            },
            title: { text: `Historical Forecast ${store.capitalizedOutagesLabel}` },
            series: [
              {
                fill: 'rgba(123, 177, 67, 0.3)',
                interpolation: { type: 'smooth' },
                showInLegend: false,
                tooltip: { enabled: false },
                type: 'range-area',
                xKey: 'date',
                yLowKey: 'outageRangeLow',
                yHighKey: 'outageRangeHigh'
              },
              {
                marker: { fill: OUTAGES_STROKE_COLOR, stroke: OUTAGES_STROKE_COLOR },
                stroke: OUTAGES_STROKE_COLOR,
                tooltip: {
                  renderer: combinedOutageTooltipRenderer,
                  interaction: {
                    enabled: true
                  }
                },
                type: 'line',
                xKey: 'date',
                yKey: 'totalOutages',
                yName: `Forecasted ${store.capitalizedOutagesLabel}`
              },
              ...(showActiveIncidents
                ? [
                    {
                      marker: { fill: ACTUALS_STROKE_COLOR, stroke: ACTUALS_STROKE_COLOR },
                      stroke: ACTUALS_STROKE_COLOR,
                      tooltip: {
                        renderer: combinedOutageTooltipRenderer,
                        interaction: {
                          enabled: true
                        }
                      },
                      type: 'line',
                      xKey: 'date',
                      yKey: 'actualIncidents',
                      yName: `Actual ${store.capitalizedOutagesLabel}`
                    }
                  ]
                : [])
            ]
          }}
        />
      </Card>
    {/if}

    <Card class="flex-1 min-h-[225px] w-full">
      <Table
        columnDefs={getForecastHistoricalColumns(onSelectDailySession)}
        csvTitle="ForecastHistoricalEvents"
        domLayout="autoHeight"
        gridOptions={{
          ...DEFAULT_GRID_OPTIONS,
          getRowId: ({ data }) => data.sessionId,
          rowSelection: 'single',
          suppressRowClickSelection: false
        }}
        {isLoading}
        {rowData}
      />
    </Card>
  {/if}
</div>

<style>
  :global(div.forecast-historical-chart-card) {
    height: 100%;
  }

  :global(div.forecast-historical-chart-card.pf) {
    height: 100%;
  }

  :global(.forecast-retrospective .forecast-card) {
    transition: filter 0.3s ease-in-out;
    width: 19.5%;
  }

  :global(.forecast-retrospective .forecast-card:hover) {
    filter: brightness(95%);
  }

  :global(.forecast-retrospective .forecast-card .headerIcon) {
    display: none;
  }
  @media (max-width: 1555px) {
    .forecast-retrospective .first-row {
      gap: 0.5rem;
      justify-content: flex-start;
    }

    :global(.forecast-retrospective .forecast-card) {
      width: calc(33.33% - 0.5rem);
    }
  }

  @media (max-width: 1075px) {
    :global(.forecast-retrospective .forecast-card .headerIcon, .weatherIcon) {
      display: block;
    }

    :global(.forecast-retrospective .alertIcon) {
      display: none;
    }
  }

  @media (max-width: 767px) {
    :global(div.forecast-historical-chart-card) {
      min-height: 0;
    }

    :global(.forecast-retrospective .card-grid) {
      min-height: auto;
    }
  }
</style>
