@import './styles/variables.css';
@import './styles/mapbox.css';
@import './styles/mapbox-legend.css';
@import './styles/tailwind.css';
@import './styles/tooltip.css';
@import 'ag-grid-enterprise/styles/ag-grid.css';
@import 'ag-grid-enterprise/styles/ag-theme-alpine.css';

/* shadcn-svelte generated | start ------------------------------------------ */
/* To be removed in TW4 */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 221.2 38.8% 56.3%;
    --primary-foreground: 0 0% 98%;
    --navigation: 216.9 25.5% 18.8%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 72.22% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 0.5rem;
    --accentText: 0 0% 50%;
  }
  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 8.6%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 8.6%;
    --popover-foreground: 0 0% 98%;
    --primary: 221.2 38.8% 56.3%;
    --navigation: 0 0% 8.6%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --accentText: 0 0% 60%;
  }
}

body {
  background-color: var(--color-background);
  color: var(--color-text);
  font-family: var(--font-primary);
}

h1,
h2 {
  font-weight: 400;
}

a {
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

h1 {
  font-size: 1.5rem;
}

h2 {
  font-size: 1.25rem;
}
pre {
  font-size: 16px;
  font-family: var(--font-mono);
  background-color: rgba(255, 255, 255, 0.45);
  border-radius: 3px;
  box-shadow: 2px 2px 6px rgb(255 255 255 / 25%);
  padding: 0.5em;
  overflow-x: auto;
  color: var(--color-text);
}
input,
button {
  font-size: inherit;
  font-family: inherit;
}

button:focus:not(:focus-visible) {
  outline: none;
}

.visually-hidden {
  border: 0;
  clip: rect(0 0 0 0);
  height: auto;
  margin: 0;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
  white-space: nowrap;
}

.material-icons {
  font-family: 'Material Symbols Outlined' !important;
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  /* Preferred icon size */
  display: inline-block;
  line-height: 1;
  text-transform: none;
  letter-spacing: normal;
  word-wrap: normal;
  white-space: nowrap;
  direction: ltr;

  /* Support for all WebKit browsers. */
  -webkit-font-smoothing: antialiased;
  /* Support for Safari and Chrome. */
  text-rendering: optimizeLegibility;

  /* Support for Firefox. */
  -moz-osx-font-smoothing: grayscale;

  /* Support for IE. */
  font-feature-settings: 'liga';
}

.mapboxgl-ctrl-bottom-right {
  display: none !important;
}
