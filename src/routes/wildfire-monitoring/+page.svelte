<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { base } from '$app/paths';
  import store from '$lib/stores/app.svelte';
  import { FeatureFlag } from '$lib/types';
  import { ResizableContainer, Table } from '$lib/components';
  import WildfireMap from '$lib/components/Map/WildfireMap.svelte';
  import * as Tabs from '$lib/components/ui/tabs';

  let container: HTMLDivElement | undefined = $state();

  function validateRouteEntry() {
    return store.featureFlags?.[FeatureFlag.wildfire];
  }

  onMount(async () => {
    if (!validateRouteEntry()) {
      goto(`${base}/`);
    }
  });
</script>

<svelte:head>
  <title>Wildfire Monitoring</title>
  <meta name="Wildfire Monitoring" content="Monitoring" />
</svelte:head>

<div class="flex flex-col h-full" bind:this={container}>
  <div class="flex flex-col flex-1 gap-sm">
    <WildfireMap />
  </div>
  <ResizableContainer {container}>
    <div class="flex flex-col w-full gap-sm p-md">
      <Tabs.Root value="network-distribution">
        <Tabs.List>
          {#if store.appConfig?.networkDistributionUrl}
            <Tabs.Trigger value="network-distribution">Distribution</Tabs.Trigger>
          {/if}
          {#if store.appConfig?.devicesUrl}
            <Tabs.Trigger value="Devices">Devices</Tabs.Trigger>
          {/if}
          <Tabs.Trigger value="Poles" disabled>Poles</Tabs.Trigger>
          <Tabs.Trigger value="Transmission" disabled>Transmission</Tabs.Trigger>
        </Tabs.List>
      </Tabs.Root>
      <Table columnDefs={[]} rowData={[]} />
    </div>
  </ResizableContainer>
</div>
