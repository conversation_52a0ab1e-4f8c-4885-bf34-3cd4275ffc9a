<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { base } from '$app/paths';
  import * as Accordion from '$lib/components/ui/accordion';
  import * as Alert from '$lib/components/ui/alert';
  import * as Carousel from '$lib/components/ui/carousel';
  import { Skeleton } from '$lib/components/ui/skeleton';
  import type { GridApi } from 'ag-grid-enterprise';
  import store from '$lib/stores/app.svelte';
  import { FeatureFlag, type StormAnalog, type AnalogEvent } from '$lib/types';
  import { getForecastAnalogs } from '$lib/api';
  import { formatAsOfTimestamp, formatDateTime } from '$lib/config/utils';
  import { getAnalogEventsColumns } from '$lib/config/table';
  import { formatNumbers, formatRange, isDataTimestampOutdated } from '$lib/utils';
  import { Table, Timestamp } from '$lib/components';
  import { Card } from '$lib/components/ui/card';
  import { Button } from '$lib/components/ui/button';

  const METRICS_MAP: Record<string, { label: string; precision: number }> = {
    outageCount: { label: store.capitalizedOutagesLabel, precision: 0 },
    precipMax: { label: 'Max Precipitation (in.)', precision: 2 },
    temperatureMean: { label: 'Avg. Temperature (°F)', precision: 2 },
    windGustMax: { label: 'Max Wind Gusts (mph)', precision: 2 },
    windSustainedMean: { label: 'Avg. Sustained Wind Gusts (mph)', precision: 2 },
    threatLevelMax: { label: 'Max Threat Level', precision: 0 }
  };

  let asOfTimestamp = $state<string>('');
  let errorMessage = $state<string>('');
  let forecastAnalogs = $state<StormAnalog[]>([]);
  let gridApi = $state<GridApi | undefined>();
  let isDataOutdated = $state<boolean>(false);
  let isLoadingAnalogs = $state<boolean>(true);
  let selectedEvents = $state<AnalogEvent[]>([]);
  let selectedStormAnalog = $state<StormAnalog | null>(null);
  let selectedStormAnalogMetrics = $state<(AnalogEvent & { eventDuration: number })[]>([]);
  let similarEventAttributes = $state<Record<string, string>>({});

  onMount(async () => {
    if (validateRouteEntry()) {
      fetchForecastAnalogs();
    } else {
      goto(`${base}/`);
    }
  });

  function validateRouteEntry() {
    return store.featureFlags?.[FeatureFlag.analogs];
  }

  async function fetchForecastAnalogs() {
    try {
      const data = await getForecastAnalogs();
      if (data?.stormAnalogs) {
        forecastAnalogs = data.stormAnalogs;
        isDataOutdated = isDataTimestampOutdated(data.generationTime);
        asOfTimestamp = formatAsOfTimestamp(data.generationTime);
        selectedStormAnalog = forecastAnalogs[0];
        similarEventAttributes = getSimilarStormAttributes(selectedStormAnalog);
        selectedStormAnalogMetrics = selectedStormAnalog?.analogEvents.map((event) => ({
          ...event,
          eventDuration: new Date(event.endTime).getTime() - new Date(event.startTime).getTime()
        }));
      }
    } catch (err) {
      errorMessage = 'Unable to retrieve analogs. Please try again.';
      forecastAnalogs = [];
    } finally {
      isLoadingAnalogs = false;
    }
  }

  function getSimilarStormAttributes(event: StormAnalog | null) {
    if (!event) {
      return {};
    }

    const similarEventMetrics: Record<string, { min: number; max: number }> = {};

    event.analogEvents.forEach((analog) => {
      Object.entries(analog.metrics).forEach(([key, value]) => {
        if (typeof value === 'number' && METRICS_MAP[key]) {
          if (!similarEventMetrics[key]) {
            similarEventMetrics[key] = { min: value, max: value };
          } else {
            similarEventMetrics[key].min = Math.min(similarEventMetrics[key].min, value);
            similarEventMetrics[key].max = Math.max(similarEventMetrics[key].max, value);
          }
        }
      });
    });

    return Object.fromEntries(
      Object.entries(similarEventMetrics).map(([key, { min, max }]) => [
        METRICS_MAP[key].label,
        formatRange(min, max, METRICS_MAP[key].precision)
      ])
    );
  }
</script>

<svelte:head>
  <title>Analogs</title>
  <meta name="Analogs" content="Analogs" />
</svelte:head>

<div class="analogs flex flex-col gap-sm h-full">
  {#if errorMessage}
    <Alert.Root variant="error">
      <Alert.Title>Error:</Alert.Title>
      <Alert.Description>{errorMessage}</Alert.Description>
    </Alert.Root>
  {:else if !isLoadingAnalogs && forecastAnalogs.length === 0}
    <Alert.Root variant="info">
      <Alert.Title>No Upcoming Weather Events:</Alert.Title>
      <Alert.Description>
        Analogs display when there are forecasted weather events. There are no analogs to display at
        this moment.
      </Alert.Description>
    </Alert.Root>
  {:else}
    {#if asOfTimestamp}
      <Timestamp {isDataOutdated} lastUpdatedDate={asOfTimestamp} />
    {:else if isLoadingAnalogs}
      <Skeleton class="w-[200px]" />
    {:else}
      <span class="text-accentText text-xs"> Data Time Unknown</span>
    {/if}

    <div class="flex items-end gap-md">
      {#if isLoadingAnalogs}
        <Skeleton class="w-[250px] h-[24px]" />
      {:else}
        <h2>Analogs</h2>
        {#if forecastAnalogs.length > 1}
          <div class="flex gap-sm">
            {#each forecastAnalogs as analog, index}
              <Button
                size="sm"
                variant={analog.forecastedEventId === selectedStormAnalog?.forecastedEventId
                  ? 'default'
                  : 'outline'}
                onclick={() => {
                  if (selectedStormAnalog?.forecastedEventId !== analog.forecastedEventId) {
                    selectedStormAnalog = analog;
                    similarEventAttributes = getSimilarStormAttributes(selectedStormAnalog);
                    selectedEvents = [];
                    gridApi!.deselectAll();
                  }
                }}
              >
                Event {index + 1}
              </Button>
            {/each}
          </div>
        {/if}
      {/if}
    </div>

    <div class="flex gap-sm flex-col lg:flex-row">
      <Card
        class="flex flex-col w-full lg:!w-[375px] lg:!min-w-[375px] !p-0  !h-[332px]"
        loading={isLoadingAnalogs}
      >
        {#if selectedStormAnalog}
          <div class="bg-primary p-4 rounded-t-md text-white">
            <p>
              {formatDateTime(selectedStormAnalog.targetStartTime)}
            </p>
            <span
              >Duration: {formatNumbers(selectedStormAnalog.metrics?.eventDuration)} hours
            </span>
          </div>
          <hr class="!border-borderColor mb-md" />

          <div class="flex flex-col gap-xs p-4">
            <div class="flex flex-wrap justify-between gap-x-md">
              <span class="text-accentText">{store.capitalizedOutagesLabel}:</span>
              <span class="text-right">
                {formatNumbers(selectedStormAnalog.metrics.outageCount)}
              </span>
            </div>
            <hr class="!border-borderColor my-md" />

            <div class="flex flex-wrap justify-between gap-x-md">
              <span class="text-accentText">Max Precipitation:</span>
              <span class="text-right">
                {formatNumbers(selectedStormAnalog.metrics?.precipMax, 2)} in.
              </span>
            </div>
            <div class="flex flex-wrap justify-between gap-x-md">
              <span class="text-accentText">Avg. Temperature:</span>
              <span class="text-right">
                {formatNumbers(selectedStormAnalog.metrics?.temperatureMean, 2)} °F
              </span>
            </div>
            <div class="flex flex-wrap justify-between gap-x-md">
              <span class="text-accentText">Max Wind Gusts:</span>
              <span class="text-right">
                {formatNumbers(selectedStormAnalog.metrics?.windGustMax, 2)} mph
              </span>
            </div>
            <div class="flex flex-wrap justify-between gap-x-md">
              <span class="text-accentText">Avg. Sustained Wind Gusts:</span>
              <span class="text-right">
                {formatNumbers(selectedStormAnalog.metrics?.windSustainedMean, 2)} mph
              </span>
            </div>
            <div class="flex flex-wrap justify-between gap-x-md">
              <span class="text-accentText">Max Threat Level:</span>
              <span class="text-right">
                {formatNumbers(selectedStormAnalog.metrics?.threatLevelMax)}
              </span>
            </div>
          </div>
        {/if}
      </Card>

      {#if isLoadingAnalogs}
        <Skeleton class="w-full h-[48px]" />
      {:else if selectedEvents.length}
        <Carousel.Root class="w-full  overflow-hidden" opts={{ dragFree: true, align: 'start' }}>
          <Carousel.Content class="m-0 gap-sm">
            {#each selectedEvents as event}
              <Carousel.Item class="max-w-full lg:max-w-[375px] p-0">
                <Card class="flex flex-col !p-0">
                  {#if event}
                    <div class="p-4 rounded-t-md">
                      <p>
                        {formatDateTime(event.startTime)}
                      </p>
                      <span>Duration: {formatNumbers(event.metrics?.eventDuration)} hours </span>
                    </div>
                    <hr class="!border-borderColor mb-md" />

                    <div class="flex flex-col gap-xs p-4">
                      <div class="flex flex-wrap justify-between gap-x-md">
                        <span class="text-accentText">{store.capitalizedOutagesLabel}:</span>
                        <span class="text-right">
                          {formatNumbers(event.metrics.outageCount)}
                        </span>
                      </div>
                      <hr class="!border-borderColor my-md" />

                      <div class="flex flex-wrap justify-between gap-x-md">
                        <span class="text-accentText">Max Precipitation:</span>
                        <span class="text-right">
                          {formatNumbers(event.metrics?.precipMax, 2)} in.
                        </span>
                      </div>
                      <div class="flex flex-wrap justify-between gap-x-md">
                        <span class="text-accentText">Avg. Temperature:</span>
                        <span class="text-right">
                          {formatNumbers(event.metrics?.temperatureMean, 2)} °F
                        </span>
                      </div>
                      <div class="flex flex-wrap justify-between gap-x-md">
                        <span class="text-accentText">Max Wind Gusts:</span>
                        <span class="text-right">
                          {formatNumbers(event.metrics?.windGustMax, 2)} mph
                        </span>
                      </div>
                      <div class="flex flex-wrap justify-between gap-x-md">
                        <span class="text-accentText">Avg. Sustained Wind Gusts:</span>
                        <span class="text-right">
                          {formatNumbers(event.metrics?.windSustainedMean, 2)} mph
                        </span>
                      </div>
                      <div class="flex flex-wrap justify-between gap-x-md">
                        <span class="text-accentText">Max Threat Level:</span>
                        <span class="text-right">
                          {formatNumbers(event.metrics?.threatLevelMax)}
                        </span>
                      </div>
                    </div>
                  {/if}
                </Card>
              </Carousel.Item>
            {/each}
          </Carousel.Content>
        </Carousel.Root>
      {:else if selectedStormAnalogMetrics.length}
        <Alert.Root variant="info">
          <Alert.Title>No Selected Events:</Alert.Title>
          <Alert.Description>Select events from the table to compare.</Alert.Description>
        </Alert.Root>
      {/if}
    </div>

    <Card class="flex flex-col gap-sm min-h-[400px] h-full">
      {#if isLoadingAnalogs}
        <Skeleton class="h-[48px]" />
      {:else if selectedStormAnalogMetrics.length}
        <Accordion.Root type="single">
          <Accordion.Item>
            <Accordion.Trigger>
              {selectedStormAnalogMetrics.length}
              Similar {selectedStormAnalogMetrics.length === 1 ? 'Event' : 'Events'} Found:
            </Accordion.Trigger>
            <Accordion.Content>
              <div class="flex flex-wrap gap-sm text-white">
                {#each Object.entries(similarEventAttributes) as [key, value]}
                  <span
                    class="p-xs text-xs border-solid border !border-borderColor bg-primary rounded-md shadow-sm"
                  >
                    {key}:
                    <span>{value}</span>
                  </span>
                {/each}
              </div>
            </Accordion.Content>
          </Accordion.Item>
        </Accordion.Root>
      {:else}
        <Alert.Root variant="info">
          <Alert.Title>No Similar Events:</Alert.Title>
          <Alert.Description>No similar analogs found for the selected event.</Alert.Description>
        </Alert.Root>
      {/if}

      <Table
        class="flex-1 !min-h-[200px]"
        columnDefs={getAnalogEventsColumns()}
        csvTitle="Analogs"
        gridOptions={{
          getRowId: (row) => row?.data.startTime,
          rowSelection: 'multiple',
          suppressRowClickSelection: true,
          onGridReady: ({ api }) => {
            gridApi = api;
          },
          onSelectionChanged: ({ api }) => {
            const selectedNodes = api.getSelectedNodes();
            selectedEvents = selectedNodes.map((node) => node.data);

            selectedNodes.forEach((node) => {
              if (!node.isSelected()) {
                node.setSelected(true);
              }
            });
          }
        }}
        isLoading={isLoadingAnalogs}
        rowData={selectedStormAnalogMetrics}
      />
    </Card>
  {/if}
</div>
