<script lang="ts">
  import { onMount } from 'svelte';
  import { base } from '$app/paths';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import * as Dialog from '$lib/components/ui/dialog';
  import { Button } from '$lib/components/ui/button';
  import * as Carousel from '$lib/components/ui/carousel';
  import type { CellValueChangedEvent, GridApi, GridOptions } from 'ag-grid-enterprise';
  import { getAllStorms, updateStormModel, getRetrospectives, getStormAggregate } from '$lib/api';
  import {
    AgChart,
    ButtonGroup,
    EtrMap,
    Table,
    AnalogCard,
    Toggle,
    Tooltip
  } from '$lib/components';
  import { Card } from '$lib/components/ui/card';
  import * as Accordion from '$lib/components/ui/accordion';
  import * as Alert from '$lib/components/ui/alert';
  import { Skeleton } from '$lib/components/ui/skeleton';
  import PlayPause from '$lib/components/Map/PlayPause.svelte';
  import AmiMap from '$lib/components/Map/AmiMap.svelte';
  import { capitalize, formatNumbers, formatDuration, formatRange } from '$lib/utils';
  import { pieChartTooltipRenderer } from '$lib/config/chart';
  import store from '$lib/stores/app.svelte';
  import { showAmiMap, showMapWeatherComponents } from '$lib/stores/constants';
  import { Components, FeatureFlag, StormMode } from '$lib/types';
  import type {
    AggregateMetricRecord,
    RetrospectiveStorm,
    StormData,
    StormRetrospectiveData,
    AggregateMetricRecordWithRegion
  } from '$lib/types';
  import { DEFAULT_GRID_OPTIONS, getHistoricalColumns } from '$lib/config/table';
  import { MS_IN_SECOND } from '$lib/config/etr';
  import { formatDate } from '$lib/config/utils';

  // svelte-ignore non_reactive_update
  enum PageView {
    SPATIAL = 'spatial',
    ANALOG = 'analog',
    TABULAR = 'tabular'
  }

  interface Attribute {
    min: number;
    max: number;
    type: string;
  }

  const MAX_SIMILAR_STORMS = 3;
  const SKELETON_COUNT = 3;
  const SYSTEM = 'System';

  const gridOptions: GridOptions = {
    ...DEFAULT_GRID_OPTIONS,
    defaultColDef: {
      ...DEFAULT_GRID_OPTIONS.defaultColDef,
      resizable: true
    },
    onCellValueChanged: updateStormName
  };
  const defaultIndex = store.componentsConfig?.[Components.SPATIAL_MAP]?.items?.findIndex(
    (item) => item.default
  );

  let errorMessage = $state<string>('');

  let isLoading = $state<boolean>(true);
  let isUpdating = $state<boolean>(false);
  let selectedStorm = $state<RetrospectiveStorm | null>(null);
  let selectedStormToUpdate = $state<StormData | null>(null);
  let showRemoved = $state<boolean>($page.url.searchParams.get('showRemoved') === 'true');
  let storms = $state<StormData[]>([]);
  let retrospectives = $state<StormRetrospectiveData[]>([]);
  let tableData = $state<RetrospectiveStorm[]>([]);
  let historicalPlaybackData = $state<AggregateMetricRecordWithRegion[][]>([]);
  let currentIndex = $state<number>(0);
  let retrospectivesCache: Record<string, AggregateMetricRecordWithRegion[][]> = {};
  let pageView = $state<PageView>(PageView.SPATIAL);
  let selectedStorms = $state<RetrospectiveStorm[]>([]);
  let showDualView = $state<boolean>(false);

  let currentRecords = $derived(
    historicalPlaybackData[currentIndex]?.filter((r) => r.region !== SYSTEM) ?? []
  );
  let selectedIndex = $derived(defaultIndex);
  let similarStormAttributes = $derived(
    generateStormAttributes([
      ...(selectedStorm ? [selectedStorm] : []),
      ...(selectedStorm?.similarStorms ?? [])
    ])
  );
  let systemRecord = $derived<AggregateMetricRecordWithRegion | undefined>(
    historicalPlaybackData[currentIndex]?.find((r) => r.region === SYSTEM)
  );

  $effect(() => {
    formatRetrospectiveData(storms, retrospectives, showRemoved);
  });

  $effect(() => {
    if (
      pageView === PageView.SPATIAL &&
      tableData.length &&
      !tableData.find((i) => i.id === $page.url.searchParams.get('stormId'))
    ) {
      selectedStorm = tableData[0];
      $page.url.searchParams.set('stormId', tableData[0]?.id);
      goto(`?${$page.url.searchParams.toString()}`, { replaceState: true });
    }
  });

  $effect(() => {
    fetchStormOverviewData(selectedStorm, pageView);
  });

  onMount(() => {
    if (validateRouteEntry()) {
      initialize();
      pageView = ($page.url.searchParams.get('pageView') as PageView) ?? PageView.SPATIAL;
    } else {
      goto(`${base}/`);
    }
  });

  async function initialize() {
    isLoading = true;
    try {
      await Promise.allSettled([fetchStorms(), fetchRetrospectiveData()]);
    } catch (e) {
      errorMessage = 'Failed to load storm data.';
    }
    isLoading = false;
  }

  function onStormSelect(stormId: string) {
    $page.url.searchParams.delete('stormId');
    goto(`${base}/etr-retrospective/events/${stormId}?${$page.url.searchParams.toString()}`);
  }

  function onSelectSimilarStorm(stormId: string) {
    $page.url.searchParams.set('stormId', stormId);
    goto(`?${$page.url.searchParams.toString()}`, { replaceState: true });
    selectedStorm = tableData.find((storm) => storm.id === stormId) ?? null;
    pageView = PageView.ANALOG;
  }

  function onStormModeUpdate(stormId: string) {
    selectedStormToUpdate = storms.find(({ id }) => id === stormId) ?? null;
  }

  function validateRouteEntry() {
    return store.featureFlags?.[FeatureFlag.etr];
  }

  async function fetchStorms() {
    try {
      storms = await getAllStorms();
    } catch (e) {
      errorMessage = 'Failed to load storms.';
    }
  }

  async function fetchRetrospectiveData() {
    try {
      const data = await getRetrospectives();
      retrospectives = data.retrospectives;
    } catch (e) {
      errorMessage = 'Failed to load retrospective data.';
    }
  }

  function formatRetrospectiveData(
    stormData: StormData[],
    retrospectiveData: StormRetrospectiveData[],
    showRemoved: boolean = false
  ) {
    const stormMap: Record<string, RetrospectiveStorm> = stormData.reduce(
      (a, v) => ({ ...a, [v.id]: v }),
      {}
    );
    const retrospectivesMap: Record<string, StormRetrospectiveData> = retrospectiveData.reduce(
      (a, v) => ({ ...a, [v.stormId]: v }),
      {}
    );

    Object.keys(stormMap).forEach((key: string) => {
      const storm = stormMap[key];
      const stormEndSummary = retrospectivesMap[key]?.stormEndSummary ?? {};
      storm.totalCumulativeCustomersAffected =
        stormEndSummary?.[SYSTEM]?.totalCumulativeCustomersAffected;
      storm.totalCumulativeIncidents = stormEndSummary?.[SYSTEM]?.totalCumulativeIncidents;
      storm.outageRecords =
        Object.keys(stormEndSummary)
          .filter((key) => key !== SYSTEM)
          .map((key) => ({ region: key, ...stormEndSummary[key] })) ?? [];
      storm.windGustThresholds = stormEndSummary?.[SYSTEM]?.windGustThresholds;
      storm.averageWindSpeed = stormEndSummary?.[SYSTEM]?.averageWindSpeed;
    });

    Object.keys(stormMap).forEach((key: string) => {
      const storm = stormMap[key];
      storm.similarStorms =
        retrospectivesMap[key]?.similarStorms
          ?.map((i) => stormMap[i.stormId])
          .splice(0, MAX_SIMILAR_STORMS) ?? [];
    });

    if ($page.url.searchParams.get('stormId') !== null) {
      const stormFromSearchParams = stormMap[$page.url.searchParams.get('stormId')!];
      if (stormFromSearchParams) {
        selectedStorm = stormFromSearchParams;
      }
    }

    tableData = Object.values(stormMap).filter(
      (d) => d.stormMode === (showRemoved ? StormMode.REMOVED : StormMode.ARCHIVE)
    );
  }

  async function updateStormName(e: CellValueChangedEvent) {
    if (e.oldValue === e.newValue) return;
    try {
      await updateStormModel(e.data.id, 'displayName', e.newValue);
      e.node.setData({ ...e.node.data, displayName: e.newValue });
      fetchStorms();
    } catch (err) {
      errorMessage = 'Could not update storm name';
    }
  }

  async function updateStormMode() {
    isUpdating = true;

    try {
      const value = showRemoved ? StormMode.ARCHIVE : StormMode.REMOVED;

      if (selectedStormToUpdate) {
        await updateStormModel(selectedStormToUpdate.id, 'stormMode', value);
      }
    } catch (e) {
      errorMessage = 'Could not update storm mode';
    }

    isUpdating = false;
  }

  function handleSelect() {
    $page.url.searchParams.set('pageView', pageView);
    goto(`?${$page.url.searchParams.toString()}`, { replaceState: true });
    errorMessage = '';
    selectedIndex = defaultIndex;
  }

  function onSelectStorm(storm: RetrospectiveStorm) {
    if (storm) {
      selectedStorm = storm;
      $page.url.searchParams.set('stormId', selectedStorm?.id);
      goto(`?${$page.url.searchParams.toString()}`, { replaceState: true });
    }
  }

  async function fetchStormOverviewData(storm: RetrospectiveStorm | null, pageView: PageView) {
    if (!storm || pageView !== PageView.SPATIAL) return;

    if (retrospectivesCache[storm.id]) {
      historicalPlaybackData = retrospectivesCache[storm.id];
      currentIndex = historicalPlaybackData.length - 1;
      errorMessage = '';
      return;
    }

    try {
      const data = await getStormAggregate(storm.id);
      historicalPlaybackData = groupByTimestamp(data.regionalMetrics);
      retrospectivesCache[storm.id] = historicalPlaybackData;
      currentIndex = historicalPlaybackData.length - 1;
      errorMessage = '';
    } catch (e) {
      errorMessage = 'Could not load historical playback data';
      historicalPlaybackData = [];
    }
  }

  function groupByTimestamp(data: Record<string, AggregateMetricRecord[]>) {
    const result: Record<string, AggregateMetricRecordWithRegion[]> = {};

    for (const [location, incidents] of Object.entries(data)) {
      incidents.forEach((incident) => {
        const { timestamp, ...rest } = incident;
        const unixTs: string = (new Date(timestamp).getTime() / MS_IN_SECOND).toString();

        if (!result[unixTs]) {
          result[unixTs] = [];
        }

        result[unixTs].push({ ...rest, region: location, timestamp: unixTs });
      });
    }
    return Object.values(result);
  }

  function onRewind() {
    currentIndex = currentIndex > 0 ? currentIndex - 1 : historicalPlaybackData.length - 1;
  }

  function onForward() {
    currentIndex = currentIndex < historicalPlaybackData.length - 1 ? currentIndex + 1 : 0;
  }

  function onSetGridData(api: GridApi) {
    api.forEachNode((node: any) => {
      if (node.data.id === selectedStorm?.id) {
        node.setSelected(true);
      }
    });
  }

  function onSetSelectedStorms(api: GridApi) {
    const selectedStormIds = selectedStorms.map((storm) => storm.id);

    api.forEachNode((node: any) => {
      if (selectedStormIds.includes(node.data.id)) {
        node.setSelected(true);
      }
    });
  }

  function onToggle(isToggled: boolean) {
    showRemoved = isToggled;
    selectedStorm = tableData[0];
    $page.url.searchParams.set('showRemoved', isToggled.toString());
    goto(`?${$page.url.searchParams.toString()}`, { replaceState: true });
  }

  function getRowData(
    storm: RetrospectiveStorm | null,
    records: RetrospectiveStorm[],
    page: string
  ) {
    if (page === PageView.SPATIAL || !storm || !storm.similarStorms?.length) {
      return records;
    }

    return [storm, ...(storm?.similarStorms ?? [])];
  }

  function onRemoveStorm() {
    selectedStorm = null;
    $page.url.searchParams.delete('stormId');
    goto(`?${$page.url.searchParams.toString()}`, { replaceState: true });
  }

  const formatAttributes = (
    attributes: Record<string, Attribute>,
    storm: RetrospectiveStorm,
    key: string,
    label: string,
    type: 'date' | 'number'
  ) => {
    const nestedKeys = key.split('.');
    let value: any = storm;

    for (const nestedKey of nestedKeys) {
      value = value?.[nestedKey];
      if (!value) {
        break;
      }
    }

    const newRange = { min: value, max: value, type };

    if (type === 'date') {
      newRange.min = newRange.min.getTime();
      newRange.max = newRange.max.getTime();
    }

    const existingRange = attributes[label];

    if (existingRange) {
      newRange.min = Math.min(existingRange.min, newRange.min);
      newRange.max = Math.max(existingRange.max, newRange.max);
    }

    return newRange;
  };

  function formatValue(value: Attribute) {
    if (value.min === value.max) {
      if (value.type === 'number') {
        return formatNumbers(value.min);
      } else if (value.type === 'dateTime') {
        return formatDuration(value.min);
      } else {
        return formatDate(value.min);
      }
    }

    if (value.type === 'number') {
      return `${formatRange(value.min, value.max)}`;
    } else if (value.type === 'dateTime') {
      return `${formatDuration(value.min)} - ${formatDuration(value.max)}`;
    } else {
      return `${formatDate(value.min)} - ${formatDate(value.max)}`;
    }
  }

  function generateStormAttributes(storms: RetrospectiveStorm[] | null) {
    const attributes: Record<string, Attribute> = {};

    if (!storms?.length) {
      return attributes;
    }

    storms.forEach((storm) => {
      attributes['Event Dates'] = formatAttributes(
        attributes,
        storm,
        'stormStartDate',
        'Event Dates',
        'date'
      );

      if (storm.stormStartDate && storm.stormEndDate) {
        if (!attributes['Duration']) {
          attributes['Duration'] = {
            min: storm.stormEndDate.getTime() - storm.stormStartDate.getTime(),
            max: storm.stormEndDate.getTime() - storm.stormStartDate.getTime(),
            type: 'dateTime'
          };
        } else {
          const min = Math.min(
            attributes['Duration'].min,
            storm.stormEndDate.getTime() - storm.stormStartDate.getTime()
          );
          const max = Math.max(
            attributes['Duration'].max,
            storm.stormEndDate.getTime() - storm.stormStartDate.getTime()
          );
          attributes['Duration'] = { min, max, type: 'dateTime' };
        }
      }

      attributes['Average Wind Speed (mph)'] = formatAttributes(
        attributes,
        storm,
        'averageWindSpeed',
        'Average Wind Speed (mph)',
        'number'
      );

      attributes['Wind Gust Count - 20 mph'] = formatAttributes(
        attributes,
        storm,
        'windGustThresholds.over20mph',
        'Wind Gust Count - 20 mph',
        'number'
      );

      attributes['Wind Gust Count - 30 mph'] = formatAttributes(
        attributes,
        storm,
        'windGustThresholds.over30mph',
        'Wind Gust Count - 30 mph',
        'number'
      );

      attributes['Wind Gust Count - 40 mph'] = formatAttributes(
        attributes,
        storm,
        'windGustThresholds.over40mph',
        'Wind Gust Count - 40 mph',
        'number'
      );
    });

    return attributes;
  }
</script>

<svelte:head>
  <title>{store.etrLabel} Retrospective</title>
  <meta name={`Storm ${store.etrLabel} Retrospective`} content="Retrospective" />
</svelte:head>

<div class="etr-retrospective flex flex-col h-full gap-sm">
  {#if errorMessage}
    <Alert.Root variant="error">
      <Alert.Title>Error:</Alert.Title>
      <Alert.Description>{errorMessage}</Alert.Description>
    </Alert.Root>
  {/if}

  <div class="flex gap-sm flex-col items-center lg:flex-row">
    <ButtonGroup
      class="w-[143px] self-start mr-2 my-1"
      size="icon"
      bind:value={pageView}
      onClick={handleSelect}
    >
      <Button tooltip={capitalize(PageView.SPATIAL)} value={PageView.SPATIAL}>
        <span class="material-icons">map</span>
      </Button>
      <Button tooltip={capitalize(PageView.TABULAR)} value={PageView.TABULAR}>
        <span class="material-icons">table_rows</span>
      </Button>
      <Button tooltip={capitalize(PageView.ANALOG)} value={PageView.ANALOG}>
        <span class="material-icons">hive</span>
      </Button>
    </ButtonGroup>

    {#if pageView === PageView.TABULAR}
      <div class="flex gap-sm flex-wrap w-full items-end justify-between">
        <h2>Storm Retrospectives</h2>
        {#if selectedStorms.length}
          <ButtonGroup bind:value={selectedIndex}>
            <Button value={0}>Customers</Button>
            <Button value={1}>
              {store.capitalizedOutagesLabel}
            </Button>
          </ButtonGroup>
        {/if}
      </div>
    {:else}
      <div class="flex gap-sm w-full items-center justify-between">
        {#if isLoading}
          <Skeleton class="ml-2 w-[400px]" />
        {:else if selectedStorm}
          <h2 class="max-w-[500px] truncate">
            {selectedStorm?.displayName}
          </h2>
          {#if pageView === PageView.SPATIAL && showAmiMap}
            <Button
              size="icon"
              variant="ghost"
              tooltip={showDualView ? 'Show Single' : 'Show Dual'}
              onclick={() => (showDualView = !showDualView)}
            >
              <span class="material-icons">
                {showDualView ? 'splitscreen_left' : 'fullscreen_portrait'}
              </span>
            </Button>
          {/if}
          {#if pageView === PageView.ANALOG && selectedStorm.similarStorms?.length}
            <ButtonGroup bind:value={selectedIndex}>
              <Button value={0}>Customers</Button>
              <Button value={1}>
                {store.capitalizedOutagesLabel}
              </Button>
            </ButtonGroup>
          {/if}
        {/if}
      </div>
    {/if}
  </div>
  {#if pageView === PageView.TABULAR}
    {#if selectedStorms.length}
      <Carousel.Root class="flex" opts={{ dragFree: true, align: 'start' }}>
        <Carousel.Content class="m-0 gap-sm">
          {#each selectedStorms as storm}
            <Carousel.Item class="max-w-[360px] p-0">
              <AnalogCard {storm} {selectedIndex} />
            </Carousel.Item>
          {/each}
        </Carousel.Content>
      </Carousel.Root>
    {/if}

    <Card class="min-h-[860px] w-full flex-1">
      <div class="flex flex-col gap-sm h-full">
        <div class="table-top flex w-full items-center justify-between flex-wrap">
          <div>
            <p class="text-lg">Historical Events</p>
            <div class="flex items-center gap-sm mt-xs text-accentText">
              {#if selectedStorms.length}
                <span>
                  {selectedStorms.length}
                  {selectedStorms.length === 1 ? 'event' : 'events'} selected
                </span>
              {:else}
                <span>Select events to compare</span>
              {/if}
            </div>
          </div>
          <Toggle
            checked={showRemoved}
            label="Show Removed"
            onToggle={(isToggled) => {
              showRemoved = isToggled;
              selectedStorms = [];
            }}
          />
        </div>
        <Table
          class="events-table"
          columnDefs={getHistoricalColumns(
            showRemoved,
            onStormModeUpdate,
            onSelectSimilarStorm,
            onStormSelect,
            true
          )}
          csvTitle="ETRRetrospectiveEvents"
          gridOptions={{
            ...gridOptions,
            rowSelection: 'multiple',
            suppressRowClickSelection: true,
            onSelectionChanged: ({ api }) => {
              const selectedNodes = api.getSelectedNodes();
              selectedStorms = selectedNodes.map((node) => node.data);

              selectedNodes.forEach((node) => {
                if (!node.isSelected()) {
                  node.setSelected(true);
                }
              });
            }
          }}
          {isLoading}
          rowData={tableData}
          onSetGridData={onSetSelectedStorms}
        />
      </div>
    </Card>
  {:else}
    {#if pageView === PageView.SPATIAL}
      <div class="flex w-full gap-sm flex-col lg:flex-row">
        <div
          class="normal-view flex gap-sm flex-row w-full lg:min-w-[400px] lg:max-w-[400px] lg:flex-col flex-wrap"
        >
          <Card class="w-[calc(50%-0.25rem)] lg:w-full chart !pb-0 flex flex-col min-h-[192px]">
            <p class="text-center truncate">
              Total Customers {store.customersAffectedLabel}
            </p>
            <AgChart
              data={currentRecords}
              loading={isLoading}
              options={{
                height: 150,
                legend: { enabled: false },
                series: [
                  {
                    sectorSpacing: 2,
                    angleKey: 'cumulativeAffectedCustomers',
                    calloutLabelKey: 'region',
                    innerRadiusRatio: 0.8,
                    tooltip: {
                      renderer: pieChartTooltipRenderer
                    },
                    type: 'donut',
                    calloutLabel: {
                      enabled: false
                    },
                    innerLabels: [
                      {
                        text: formatNumbers(systemRecord?.cumulativeAffectedCustomers),
                        fontWeight: 'bold',
                        fontSize: 18
                      }
                    ]
                  }
                ]
              }}
              onMouseleave={() => (store.activeMapRegion = null)}
            />
          </Card>
          <Card class="w-[calc(50%-0.25rem)] lg:w-full chart !pb-0 flex flex-col min-h-[192px]">
            <p class="text-center truncate">
              Total {store.capitalizedOutagesLabel}
            </p>
            <AgChart
              data={currentRecords}
              loading={isLoading}
              options={{
                height: 150,
                legend: { enabled: false },
                series: [
                  {
                    sectorSpacing: 2,
                    angleKey: 'cumulativeIncidents',
                    calloutLabelKey: 'region',
                    innerRadiusRatio: 0.8,
                    tooltip: {
                      renderer: pieChartTooltipRenderer
                    },
                    type: 'donut',
                    calloutLabel: {
                      enabled: false
                    },
                    innerLabels: [
                      {
                        text: formatNumbers(systemRecord?.cumulativeIncidents),
                        fontWeight: 'bold',
                        fontSize: 18
                      }
                    ]
                  }
                ]
              }}
              onMouseleave={() => (store.activeMapRegion = null)}
            />
          </Card>
          {#if showMapWeatherComponents}
            <Card class="w-full !h-[162px] min-h-[162px]" loading={isLoading}>
              <p class="text-center mb-sm truncate">Wind</p>
              {#if systemRecord?.windGustThresholds}
                <div class="flex flex-wrap justify-between gap-x-md">
                  <span class="text-accentText">Avg. Sustained Speed:</span>
                  <span class="text-right">
                    {formatNumbers(systemRecord?.averageWindSpeed, 2)} mph
                  </span>
                </div>
                <div class="flex flex-wrap justify-between gap-x-md">
                  <span class="text-accentText">Hourly Gusts > 20 mph:</span>
                  <span class="text-right">
                    {formatNumbers(systemRecord?.windGustThresholds?.over20mph)}
                  </span>
                </div>
                <div class="flex flex-wrap justify-between gap-x-md">
                  <span class="text-accentText">Hourly Gusts > 30 mph:</span>
                  <span class="text-right">
                    {formatNumbers(systemRecord?.windGustThresholds?.over30mph)}
                  </span>
                </div>
                <div class="flex flex-wrap justify-between gap-x-md">
                  <span class="text-accentText">Hourly Gusts > 40 mph:</span>
                  <span class="text-right">
                    {formatNumbers(systemRecord?.windGustThresholds?.over40mph)}
                  </span>
                </div>
              {:else}
                <p class="text-center text-xs mt-10">No data to display</p>
              {/if}
            </Card>
          {/if}
        </div>
        <!-- Mobile View -->
        <Card class="mobile-view flex min-h-[232px] !pb-0">
          <div class="w-6/12 flex flex-col">
            <p class="text-center h-12">
              Total Customers {store.customersAffectedLabel}
            </p>
            <AgChart
              data={currentRecords}
              loading={isLoading}
              options={{
                height: 150,
                legend: { enabled: false },
                series: [
                  {
                    sectorSpacing: 2,
                    angleKey: 'cumulativeAffectedCustomers',
                    calloutLabelKey: 'region',
                    innerRadiusRatio: 0.8,
                    tooltip: {
                      renderer: pieChartTooltipRenderer
                    },
                    type: 'donut',
                    calloutLabel: {
                      enabled: false
                    },
                    innerLabels: [
                      {
                        text: formatNumbers(systemRecord?.cumulativeAffectedCustomers),
                        fontWeight: 'bold',
                        fontSize: 16
                      }
                    ]
                  }
                ]
              }}
              onMouseleave={() => (store.activeMapRegion = null)}
            />
          </div>

          <div class="w-6/12 flex flex-col">
            <p class="text-center h-12">
              Total {store.capitalizedOutagesLabel}
            </p>
            <AgChart
              data={currentRecords}
              loading={isLoading}
              options={{
                height: 150,
                legend: { enabled: false },
                series: [
                  {
                    sectorSpacing: 2,
                    angleKey: 'cumulativeIncidents',
                    calloutLabelKey: 'region',
                    innerRadiusRatio: 0.8,
                    tooltip: {
                      renderer: pieChartTooltipRenderer
                    },
                    type: 'donut',
                    calloutLabel: {
                      enabled: false
                    },
                    innerLabels: [
                      {
                        text: formatNumbers(systemRecord?.cumulativeIncidents),
                        fontWeight: 'bold',
                        fontSize: 16
                      }
                    ]
                  }
                ]
              }}
              onMouseleave={() => (store.activeMapRegion = null)}
            />
          </div>
        </Card>

        {#if showDualView}
          <div class="flex gap-sm flex-1 flex-col 2xl:flex-row">
            <EtrMap
              class="map-card w-full"
              records={currentRecords}
              config={store.componentsConfig?.[Components.RETROSPECTIVE_MAP]?.items}
              loading={isLoading}
              showWindGusts
            >
              <PlayPause
                {onForward}
                {onRewind}
                currentFrameTime={parseInt(
                  historicalPlaybackData?.[currentIndex]?.[0].timestamp,
                  10
                )}
                maxStep={historicalPlaybackData.length - 1}
                index={currentIndex}
                onChange={(e) => {
                  currentIndex = e;
                }}
              />
            </EtrMap>
            <AmiMap class="map-card w-full" loading={isLoading} {selectedStorm} />
          </div>
        {:else}
          <EtrMap
            class="map-card w-full"
            records={currentRecords}
            config={store.componentsConfig?.[Components.RETROSPECTIVE_MAP]?.items}
            loading={isLoading}
            showWindGusts
          >
            <PlayPause
              {onForward}
              {onRewind}
              currentFrameTime={parseInt(historicalPlaybackData?.[currentIndex]?.[0].timestamp, 10)}
              maxStep={historicalPlaybackData.length - 1}
              index={currentIndex}
              onChange={(e) => {
                currentIndex = e;
              }}
            />
          </EtrMap>
        {/if}
      </div>
    {/if}
    {#if pageView === PageView.ANALOG}
      {#if isLoading}
        <div class="flex flex-col gap-xs">
          <!--eslint-disable @typescript-eslint/no-unused-vars -->
          {#each Array(SKELETON_COUNT) as _}
            <Skeleton class="w-full" />
          {/each}
        </div>
      {:else}
        {#if !selectedStorm}
          <Alert.Root variant="info">
            <Alert.Title>Select an event to view its similar storms.</Alert.Title>
          </Alert.Root>
        {/if}
        {#if selectedStorm}
          {#if !selectedStorm.similarStorms?.length}
            <Alert.Root variant="warning">
              <Alert.Title>No Similar Storms:</Alert.Title>
              <Alert.Description>Select another event.</Alert.Description>
            </Alert.Root>
          {:else}
            <div class="flex items-center">
              <Accordion.Root type="single" class="w-full">
                <Accordion.Item>
                  <Accordion.Trigger>
                    <div class="flex items-center gap-2">
                      <Tooltip className="relative top-[3px]">
                        {#snippet trigger()}
                          <span class="material-icons">info</span>
                        {/snippet}

                        {#snippet content()}
                          <span
                            >Similar storms are matched based on having a duration, wind gust
                            counts, and average sustained wind speeds within 70% of the reference
                            storm, while starting within ±2 months</span
                          >
                        {/snippet}
                      </Tooltip>
                      <span>
                        {selectedStorm?.similarStorms?.length}
                        similar
                        {selectedStorm?.similarStorms?.length === 1 ? 'storm' : 'storms'} found:
                      </span>
                    </div>
                  </Accordion.Trigger>
                  <Accordion.Content>
                    <div class="flex flex-wrap gap-sm">
                      {#each Object.entries(similarStormAttributes) as [key, value]}
                        <span
                          class="p-xs text-xs border-solid border !border-primary bg-primary rounded-md shadow-sm text-white"
                        >
                          {key}:
                          <span>{formatValue(value)}</span>
                        </span>
                      {/each}
                    </div>
                  </Accordion.Content>
                </Accordion.Item>
              </Accordion.Root>
            </div>

            <div class="flex gap-sm flex-col md:flex-row items-center">
              <AnalogCard storm={selectedStorm} isPrimary onClick={onRemoveStorm} {selectedIndex} />
              <Carousel.Root opts={{ align: 'start' }}>
                <Carousel.Content class="m-0 gap-sm w-[375px] md:w-full">
                  {#each selectedStorm?.similarStorms as storm}
                    <Carousel.Item class="max-w-[360px] p-0">
                      <AnalogCard {storm} {selectedIndex} />
                    </Carousel.Item>
                  {/each}
                </Carousel.Content>
              </Carousel.Root>
            </div>
          {/if}
        {/if}
      {/if}
    {/if}
    <Card class="min-h-[260px] w-full flex-1">
      <div class="flex flex-col gap-sm h-full">
        <div class="table-top flex gap-sm w-full items-center justify-between flex-wrap">
          <p class="text-lg">Historical Events</p>
          <Toggle checked={showRemoved} label="Show Removed" {onToggle} />
        </div>
        <Table
          class="events-table w-full"
          columnDefs={getHistoricalColumns(
            showRemoved,
            onStormModeUpdate,
            onSelectSimilarStorm,
            onStormSelect
          )}
          csvTitle="ETRHistoricalEvents"
          gridOptions={{
            ...gridOptions,
            rowSelection: 'single',
            suppressRowClickSelection: false,
            onSelectionChanged: ({ api }) => {
              onSelectStorm(api.getSelectedRows()[0]);
            }
          }}
          {isLoading}
          rowData={getRowData(selectedStorm, tableData, pageView)}
          {onSetGridData}
        />
      </div>
    </Card>
  {/if}
</div>

<Dialog.Root
  open={!!selectedStormToUpdate}
  onOpenChange={(isOpening) => {
    if (!isOpening) selectedStormToUpdate = null; // Handles ESC Keypress
  }}
>
  <Dialog.Content class="w-[350px] md:w-[600px]">
    <Dialog.Header>
      <Dialog.Title>
        Confirm {showRemoved ? 'Restore' : 'Remove'}?
      </Dialog.Title>
      <Dialog.Description>
        Are you sure you want to {showRemoved ? 'restore' : 'remove'}
        <strong>[{selectedStormToUpdate?.displayName}]</strong>?
      </Dialog.Description>
    </Dialog.Header>

    <Dialog.Footer>
      <Button variant="tertiary" onclick={() => (selectedStormToUpdate = null)}>Cancel</Button>
      <Button
        disabled={isUpdating}
        onclick={async () => {
          await updateStormMode();
          selectedStormToUpdate = null;
          fetchStorms();
        }}
      >
        Confirm
      </Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>

<style>
  :global(.events-table .material-icons) {
    cursor: pointer;
    position: relative;
  }

  :global(.events-table .settings_backup_restore) {
    color: var(--color-green);
    font-size: 28px;
    top: 2px;
  }

  :global(.events-table .close) {
    color: var(--color-danger);
    font-size: 32px;
    top: 2px;
  }

  :global(.etr-retrospective .mobile-view) {
    display: none;
  }

  @media (max-width: 1024px) {
    :global(.etr-retrospective .map-card) {
      height: 450px !important;
    }
  }

  @media (max-width: 767px) {
    :global(.etr-retrospective .normal-view .chart) {
      display: none;
    }

    :global(.etr-retrospective .normal-view) {
      order: 1;
    }

    :global(.etr-retrospective .mobile-view) {
      display: flex;
      order: 0;
    }

    :global(.etr-retrospective .map-card) {
      max-height: 450px;
      order: 2;
    }
  }
</style>
