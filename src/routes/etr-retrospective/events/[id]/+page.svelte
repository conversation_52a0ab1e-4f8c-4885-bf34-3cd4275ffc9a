<script lang="ts">
  import { goto } from '$app/navigation';
  import { base } from '$app/paths';
  import { page } from '$app/stores';
  import {
    getStormMetricData,
    getStorm,
    getStormAggregate,
    getStormRetrospective,
    getResourceVisualizationHistory
  } from '$lib/api';
  import { AgChart, ButtonGroup } from '$lib/components';
  import { Card } from '$lib/components/ui/card';
  import * as Alert from '$lib/components/ui/alert';
  import { Button } from '$lib/components/ui/button';
  import * as Select from '$lib/components/ui/select';
  import {
    getPastOutagesOverviewChartSeriesItems,
    HOURS_TO_RESTORATION_LINE_SERIES_COLORS,
    CROSS_AXIS_COLOUR,
    CHART_LINE_DASH_LENGTH,
    CHART_LINE_DASH_BREAK,
    DEFAULT_HISTORICAL_WORKER_ITEMS,
    getHistoricalWorkerChartSeriesItems,
    customFormatter,
    LINECHART_X_AXIS_FORMAT_HOURLY
  } from '$lib/config/chart';
  import store from '$lib/stores/app.svelte';
  import { showEta, showMapWeatherComponents } from '$lib/stores/constants';
  import {
    Components,
    type AggregateMetricRecord,
    type Metric,
    type SelectItem,
    type StormData,
    type RegionSummary,
    EtrChartKeys
  } from '$lib/types';
  import { onMount } from 'svelte';
  import { ErrorStatusCodes } from '$lib/config/main';
  import { formatOverviewData } from '$lib/utils';
  import { formatNumbers, formatDuration } from '$lib/utils';
  import { formatDateTime } from '$lib/config/utils';
  import {
    STORM_WINDOW_HOURS,
    formatWorkerRecords,
    MS_IN_HOUR,
    OutagesChartScope
  } from '$lib/config/etr';
  import { DateTime } from 'luxon';
  import type { AgAxisLabelFormatterParams, AgNodeClickEvent } from 'ag-charts-enterprise';

  type ChartDataCache = {
    [OutagesChartScope.SYSTEM]: Record<string, any>[];
    [OutagesChartScope.REGION]: Record<string, any>;
  };

  const OUTAGE_CHART_ITEMS: SelectItem[] =
    store.componentsConfig?.[Components.ETR_HISTORICAL_OUTAGES_CHART]?.items ?? [];
  const OUTAGE_CHART_ITEMS_MAP = Object.fromEntries(
    OUTAGE_CHART_ITEMS.map(({ id, text }) => [id, text])
  );
  const DEFAULT_HISTORICAL_WORKER_ITEMS_MAP = Object.fromEntries(
    DEFAULT_HISTORICAL_WORKER_ITEMS.map(({ id, text }) => [id, text])
  );

  let storm = $state<StormData | null>(null);

  let chartXAxisMax = $state<Date | null>(null);
  let chartXAxisMin = $state<Date | null>(null);
  let comboChartData = $state<Record<string, any>[]>([]);
  let isLoading = $state<boolean>(true);
  let metricData = $state<Record<string, any>[]>([]);
  let lineChartData = $state<Record<string, any>[]>([]);
  let selectedOutageChartItems = $state<SelectItem[]>(OUTAGE_CHART_ITEMS.filter((i) => i.checked));
  let selectedOutageChartKeys = $state<string[]>(
    OUTAGE_CHART_ITEMS.filter((i) => i.checked).map((i) => i.id)
  );
  let selectedWorkerChartItems = $state<SelectItem[]>(
    DEFAULT_HISTORICAL_WORKER_ITEMS.filter((i) => i.checked)
  );
  let selectedWorkerChartKeys = $state<string[]>(
    DEFAULT_HISTORICAL_WORKER_ITEMS.filter((i) => i.checked).map((i) => i.id)
  );
  let showError = $state<boolean>(false);
  let showNoData = $state<boolean>(false);
  let stormCrossLines = $state<Record<string, any>[]>([]);
  let systemOverviewData = $state<AggregateMetricRecord[]>([]);
  let retrospectiveRecord = $state<RegionSummary | null>(null);
  let loadingRetrospectiveRecord = $state<boolean>(true);
  let showSystemView = $state<boolean>(true);
  let selectedView = $state<number>(0);
  let groupedRegionData: Record<
    string,
    { outages: Metric[]; restorations: Metric[]; 'cumulative-outages': Metric[] }
  > = {};
  let selectedRegion = $state<string>(store.regions[0]?.displayName ?? '');

  let chartDataCache: ChartDataCache = {
    [OutagesChartScope.SYSTEM]: [],
    [OutagesChartScope.REGION]: {}
  };

  let resourcesData = $state<
    {
      timestamp: string;
      totalWorkers: number;
      region: string;
      date: Date;
      resources: Record<string, number>;
    }[]
  >([]);

  let outageChartItems = $derived(getPastOutagesOverviewChartSeriesItems(selectedOutageChartItems));
  let workerChartItems = $derived(getHistoricalWorkerChartSeriesItems(selectedWorkerChartItems));

  $effect(() => {
    lineChartData = [...metricData, ...systemOverviewData, ...resourcesData];
  });

  onMount(async () => {
    await getStormData();

    isLoading = true;

    try {
      await Promise.allSettled([
        fetchStormOutageData(showSystemView),
        fetchStormOverviewData(),
        fetchStormRetrospective(),
        fetchResourceHistory()
      ]);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      isLoading = false;
    }

    setStormCrossLines();
  });

  async function getStormData() {
    const stormId = $page.params.id;
    if (stormId) {
      try {
        storm = await getStorm(stormId);
      } catch (e: any) {
        if (e?.response?.status === ErrorStatusCodes.NOT_FOUND) {
          showNoData = true;
        } else {
          showError = true;
        }
      }
    }
  }

  function setStormCrossLines() {
    if (storm) {
      const props = {
        lineDash: [CHART_LINE_DASH_LENGTH, CHART_LINE_DASH_BREAK],
        stroke: '#b30900',
        strokeWidth: 2,
        type: 'line'
      };
      stormCrossLines = [
        { ...props, value: storm.stormStartDate, label: { text: 'Storm Start' } },
        { ...props, value: storm.stormEndDate, label: { text: 'Storm End' } }
      ];
    }
  }

  function setOutageCountData(metrics: Metric[]) {
    comboChartData = metrics.map((m) => ({
      date: new Date(m.timestamp),
      outageCount: m.records[0] ? m.records[0]?.activeIncidents : null
    }));
  }

  function setRestorationCountData(metrics: Metric[]) {
    const data = metrics.map((m) => ({
      date: new Date(m.timestamp),
      restorationCount: m.records[0] ? m.records[0].restoredIncidents : null
    }));
    comboChartData = [...comboChartData, ...data];
  }

  function setCumulativeOutageCountData(metrics: Metric[]) {
    const data = metrics.map((m) => ({
      date: new Date(m.timestamp),
      cumulativeIncidents: m.records[0] ? m.records[0].cumulativeIncidents : null,
      cumulativeRestorations: m.records[0] ? m.records[0].cumulativeRestoredIncidents : null,
      cumulativeAffectedCustomers: m.records[0] ? m.records[0].cumulativeCustomerOutages : null,
      cumulativeCustomersRestored: m.records[0]
        ? m.records[0].cumulativeRestoredCustomerOutages
        : null
    }));
    comboChartData = [...comboChartData, ...data];
  }

  function setHoursToRestorationData(metrics: Metric[]) {
    metricData = metrics.map((m) => ({
      date: new Date(m.timestamp),
      hours: m.records[0] ? m.records[0].hoursToRestoration : null,
      sessionId: m.sessionId,
      stormId: storm?.id
    }));
  }

  function setWorkerCountData(metrics: Metric[]) {
    const data = metrics.map((m) => ({
      date: new Date(m.timestamp),
      resources: m.records[0] ? m.records[0].resources : null,
      workers: m.records[0] ? m.records[0].totalWorkers : null
    }));
    metricData = [...metricData, ...data];
  }

  function setChartXAxisDomainRange() {
    const sortedData = [...comboChartData, ...lineChartData].sort(
      (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
    );
    if (sortedData.length < 2) return;
    chartXAxisMin = sortedData[0].date;
    chartXAxisMax = sortedData.at(-1)?.date;
  }

  function groupByRegionWithTimeSeries(
    key: 'outages' | 'cumulative-outages' | 'restorations',
    metrics: Metric[]
  ) {
    metrics.forEach((entry) => {
      const timestamp = entry.timestamp;
      entry.records.forEach((record) => {
        const { region } = record;

        if (!groupedRegionData[region]) {
          groupedRegionData[region] = {
            outages: [],
            restorations: [],
            'cumulative-outages': []
          };
        }

        groupedRegionData[region][key].push({
          timestamp,
          records: [record]
        });
      });
    });
  }

  async function fetchStormOutageData(isSystemView: boolean) {
    if (!storm) return;

    if (isSystemView && chartDataCache[OutagesChartScope.SYSTEM].length) {
      comboChartData = chartDataCache[OutagesChartScope.SYSTEM];
      setChartXAxisDomainRange();
      return;
    }

    if (!isSystemView && Object.keys(chartDataCache[OutagesChartScope.REGION]).length) {
      groupedRegionData = chartDataCache[OutagesChartScope.REGION];
      onRegionChange(store.regions[0]?.displayName);
      setChartXAxisDomainRange();
      return;
    }

    try {
      const [r1, r2, r3, r4, r5] = await Promise.all([
        getStormMetricData('outages', storm.id, isSystemView),
        getStormMetricData('restorations', storm.id, isSystemView),
        getStormMetricData('medium-predicted-restorations', storm.id, isSystemView),
        getStormMetricData('resources', storm.id, isSystemView),
        getStormMetricData('cumulative-outages', storm.id, isSystemView)
      ]);

      if (isSystemView) {
        if (r1?.metrics?.length) setOutageCountData(r1.metrics);
        if (r2?.metrics?.length) setRestorationCountData(r2.metrics);
        if (r3?.metrics?.length) setHoursToRestorationData(r3.metrics);
        if (r4?.metrics?.length) setWorkerCountData(r4.metrics);
        if (r5?.metrics?.length) setCumulativeOutageCountData(r5.metrics);
        chartDataCache['system'] = comboChartData;
      } else {
        groupByRegionWithTimeSeries('outages', r1.metrics);
        groupByRegionWithTimeSeries('restorations', r2.metrics);
        groupByRegionWithTimeSeries('cumulative-outages', r5.metrics);
        chartDataCache['region'] = groupedRegionData;
        onRegionChange(store.regions[0]?.displayName);
      }

      setChartXAxisDomainRange();
    } catch (e: any) {
      if (e?.response?.status === ErrorStatusCodes.NOT_FOUND) {
        showNoData = true;
      } else {
        showError = true;
      }
    }
  }

  function onRegionChange(region: string) {
    setOutageCountData(groupedRegionData[region]?.outages || []);
    setRestorationCountData(groupedRegionData[region]?.restorations);
    setCumulativeOutageCountData(groupedRegionData[region]?.['cumulative-outages']);
  }

  async function fetchStormOverviewData() {
    if (storm?.id) {
      const stormOverviewData = await getStormAggregate(storm.id);
      systemOverviewData = formatOverviewData(stormOverviewData?.regionalMetrics?.System);
    }
  }

  async function fetchStormRetrospective() {
    if (storm) {
      loadingRetrospectiveRecord = true;
      const data = await getStormRetrospective(storm.id);
      retrospectiveRecord = data.stormEndSummary?.System;
      loadingRetrospectiveRecord = false;
    }
  }

  async function fetchResourceHistory() {
    if (!storm?.stormStartDate || !storm?.stormEndDate) return;

    const startDate = DateTime.fromISO(storm.stormStartDate.toISOString()).minus({
      hours: STORM_WINDOW_HOURS
    });
    const endDate = DateTime.fromISO(storm.stormEndDate.toISOString()).plus({
      hours: STORM_WINDOW_HOURS
    });

    try {
      const data = await getResourceVisualizationHistory(startDate.toISO()!, endDate.toISO()!);

      const durationInHours = endDate.diff(startDate, 'hours').hours;

      resourcesData = formatWorkerRecords(
        data.historicalSystemStates,
        durationInHours,
        true,
        endDate,
        MS_IN_HOUR
      ).filter((record) => record.region === 'System');
    } catch (error) {
      console.error(error);
    }
  }
</script>

{#if showNoData}
  <Alert.Root variant="info">
    <Alert.Title>No Data Available</Alert.Title>
  </Alert.Root>
{:else if showError}
  <Alert.Root variant="error">
    <Alert.Title>Error:</Alert.Title>
    <Alert.Description>An internal server error occurred.</Alert.Description>
  </Alert.Root>
{:else}
  <div class="event historical-storm flex flex-col gap-sm">
    {#if storm}
      <div class="flex flex-col">
        <div class="flex items-center mb-2 gap-md">
          <Button
            class="rounded-full"
            size="icon"
            onclick={() => {
              goto(
                `${base}/etr-retrospective?stormId=${
                  storm?.id
                }&pageView=${$page.url.searchParams.get('pageView') ?? 'spatial'}`
              );
            }}
            tooltip="Back to Retrospective page"
          >
            <span class="material-icons">arrow_back</span>
          </Button>
          <h2>Storm Overview - {storm.displayName}</h2>
        </div>
        <div class="flex gap-sm flex-col lg:flex-row">
          <Card class="flex flex-col p-4 w-full lg:w-[450px]">
            <div class="flex flex-col gap-1">
              <div class="flex flex-wrap justify-between gap-x-md">
                <span class="text-accentText">Storm Start:</span>
                <span class="flex-1 text-right">
                  {formatDateTime(storm.stormStartDate, true)}
                </span>
              </div>
              <div class="flex flex-wrap justify-between gap-x-md">
                <span class="text-accentText">Storm End:</span>
                <span class="flex-1 text-right">
                  {formatDateTime(storm.stormEndDate, true)}
                </span>
              </div>
              <div class="flex flex-wrap justify-between gap-x-md">
                <span class="text-accentText">Duration:</span>
                <span class="flex-1 text-right">
                  {formatDuration(
                    new Date(storm.stormEndDate).getTime() -
                      new Date(storm.stormStartDate).getTime()
                  )}
                </span>
              </div>
            </div>
          </Card>
          <Card class="flex flex-col p-4 w-full lg:w-[450px]" loading={loadingRetrospectiveRecord}>
            <div class="flex flex-col">
              <div class="flex flex-wrap justify-between gap-x-md">
                <span class="text-accentText">
                  Total Customers {store.customersAffectedLabel}:
                </span>
                <span class="text-right">
                  {formatNumbers(retrospectiveRecord?.totalCumulativeCustomersAffected)}
                </span>
              </div>
              <div class="flex flex-wrap justify-between gap-x-md">
                <span class="text-accentText">
                  Total {store.capitalizedOutagesLabel}:
                </span>
                <span class="text-right">
                  {formatNumbers(retrospectiveRecord?.totalCumulativeIncidents)}
                </span>
              </div>
            </div>
          </Card>
          {#if showMapWeatherComponents}
            <Card
              class="flex flex-col p-4 w-full lg:w-[450px]"
              loading={loadingRetrospectiveRecord}
            >
              <div class="flex flex-col">
                <div class="flex flex-wrap justify-between gap-x-md">
                  <span class="text-accentText">Avg. Sustained Wind Speed:</span>
                  <span class="text-right">
                    {formatNumbers(retrospectiveRecord?.averageWindSpeed, 2)} mph
                  </span>
                </div>
                <div class="flex flex-wrap justify-between gap-x-md">
                  <span class="text-accentText">Hourly Gusts > 20 mph:</span>
                  <span class="text-right">
                    {formatNumbers(retrospectiveRecord?.windGustThresholds?.over20mph)}
                  </span>
                </div>
                <div class="flex flex-wrap justify-between gap-x-md">
                  <span class="text-accentText">Hourly Gusts > 30 mph:</span>
                  <span class="text-right">
                    {formatNumbers(retrospectiveRecord?.windGustThresholds?.over30mph)}
                  </span>
                </div>
                <div class="flex flex-wrap justify-between gap-x-md">
                  <span class="text-accentText">Hourly Gusts > 40 mph:</span>
                  <span class="text-right">
                    {formatNumbers(retrospectiveRecord?.windGustThresholds?.over40mph)}
                  </span>
                </div>
              </div>
            </Card>
          {/if}
        </div>
      </div>
      <Card class="flex flex-col !pb-0 !h-[325px] gap-sm">
        <div
          class="flex items-center flex-wrap gap-4 {showEta ? 'justify-between' : 'justify-end'}"
        >
          <div class="flex gap-md flex-1 flex-wrap">
            <Select.Root
              type="multiple"
              value={selectedOutageChartKeys}
              onValueChange={(value) => {
                selectedOutageChartKeys = value;
                selectedOutageChartItems = value.map((v) => {
                  return { id: v, text: OUTAGE_CHART_ITEMS_MAP[v] };
                });
              }}
            >
              <Select.Trigger class="w-[280px]">
                <div class="flex items-center">
                  <Select.SelectionPill
                    count={selectedOutageChartItems.length}
                    onClear={() => {
                      selectedOutageChartKeys = [];
                      selectedOutageChartItems = [];
                    }}
                  />
                  Chart Settings
                </div>
              </Select.Trigger>
              <Select.Content>
                {#each OUTAGE_CHART_ITEMS as { id, text }}
                  <Select.Item value={id}>{text}</Select.Item>
                {/each}
              </Select.Content>
            </Select.Root>

            {#if showEta && !showSystemView && store.regions.length}
              <Select.Root type="single" bind:value={selectedRegion} onValueChange={onRegionChange}>
                <Select.Trigger class="w-[155px]">
                  {selectedRegion}
                </Select.Trigger>
                <Select.Content>
                  {#each store.regions as region}
                    <Select.Item value={region.displayName}>
                      {region.displayName}
                    </Select.Item>
                  {/each}
                </Select.Content>
              </Select.Root>
            {/if}
          </div>

          {#if showEta}
            <ButtonGroup
              bind:value={selectedView}
              onClick={() => {
                showSystemView = selectedView === 0;
                fetchStormOutageData(showSystemView);
              }}
            >
              <Button value={0}>System</Button>
              <Button value={1}>Region</Button>
            </ButtonGroup>
          {/if}
        </div>
        <AgChart
          className="combo-chart"
          loading={isLoading}
          data={comboChartData}
          options={{
            background: { fill: 'transparent' },
            axes: [
              {
                crossLines: stormCrossLines,
                label: {
                  formatter: (value: AgAxisLabelFormatterParams) =>
                    customFormatter(value, LINECHART_X_AXIS_FORMAT_HOURLY)
                },
                max: chartXAxisMax,
                min: chartXAxisMin,
                position: 'bottom',
                type: 'time'
              },
              ...(selectedOutageChartKeys.includes(EtrChartKeys.cumulativeAffectedCustomers) ||
              selectedOutageChartKeys.includes(EtrChartKeys.cumulativeCustomersRestored)
                ? [
                    {
                      keys: [
                        EtrChartKeys.cumulativeAffectedCustomers,
                        EtrChartKeys.cumulativeCustomersRestored
                      ],
                      position: 'right',
                      title: {
                        text: 'Customers',
                        color: CROSS_AXIS_COLOUR
                      },
                      type: 'number',
                      label: {
                        color: CROSS_AXIS_COLOUR
                      },
                      tick: {
                        stroke: CROSS_AXIS_COLOUR
                      }
                    }
                  ]
                : []),
              {
                position: 'left',
                title: { text: store.capitalizedOutagesLabel },
                type: 'number'
              }
            ],
            series: outageChartItems,
            legend: { item: { showSeriesStroke: true } },
            title: {
              text: `Past ${store.capitalizedOutagesLabel} Overview`
            },
            padding: { right: 50, left: 50 },
            navigator: {
              enabled: true
            }
          }}
        />
      </Card>

      <Card class="flex flex-col !pb-0 !h-[325px] gap-sm">
        <Select.Root
          type="multiple"
          value={selectedWorkerChartItems.map((i) => i.id)}
          onValueChange={(value) => {
            selectedWorkerChartKeys = value;
            selectedWorkerChartItems = value.map((v) => {
              return { id: v, text: DEFAULT_HISTORICAL_WORKER_ITEMS_MAP[v] };
            });
          }}
        >
          <Select.Trigger class="w-[280px]">
            <div class="flex items-center">
              <Select.SelectionPill
                count={selectedWorkerChartItems.length}
                onClear={() => {
                  selectedWorkerChartKeys = [];
                  selectedWorkerChartItems = [];
                }}
              />
              Chart Settings
            </div>
          </Select.Trigger>
          <Select.Content>
            {#each DEFAULT_HISTORICAL_WORKER_ITEMS as { id, text }}
              <Select.Item value={id}>{text}</Select.Item>
            {/each}
          </Select.Content>
        </Select.Root>

        <AgChart
          className="line-chart"
          loading={isLoading}
          data={lineChartData}
          options={{
            background: { fill: 'transparent' },
            axes: [
              {
                crossLines: stormCrossLines,
                label: {
                  formatter: (value: AgAxisLabelFormatterParams) =>
                    customFormatter(value, LINECHART_X_AXIS_FORMAT_HOURLY)
                },
                max: chartXAxisMax,
                min: chartXAxisMin,
                position: 'bottom',
                type: 'time'
              },
              ...(selectedWorkerChartKeys.includes(EtrChartKeys.hours)
                ? [
                    {
                      keys: [EtrChartKeys.hours],
                      position: 'left',
                      title: {
                        text: 'Hours to Restoration',
                        color: HOURS_TO_RESTORATION_LINE_SERIES_COLORS.hours
                      },
                      type: 'number',
                      label: {
                        color: HOURS_TO_RESTORATION_LINE_SERIES_COLORS.hours
                      },
                      tick: {
                        stroke: HOURS_TO_RESTORATION_LINE_SERIES_COLORS.hours
                      }
                    }
                  ]
                : []),
              {
                keys: [EtrChartKeys.totalWorkers],
                position: 'right',
                title: {
                  text: 'Workers',
                  color: HOURS_TO_RESTORATION_LINE_SERIES_COLORS.workers
                },
                type: 'number',
                label: {
                  color: HOURS_TO_RESTORATION_LINE_SERIES_COLORS.workers
                },
                tick: {
                  stroke: HOURS_TO_RESTORATION_LINE_SERIES_COLORS.workers
                }
              }
            ],
            series: workerChartItems,
            legend: { item: { showSeriesStroke: true } },
            listeners: {
              seriesNodeClick: ({
                datum,
                yKey
              }: AgNodeClickEvent<string, { sessionId: string; yKey: string }>) => {
                if (yKey === 'hours') {
                  goto(`${base}/etr/dashboard?sessionId=${datum.sessionId}&stormId=${storm?.id}`);
                }
              }
            },
            title: { text: `${store.etrLabel} Prediction Overview - Total Hours to Restoration` },
            padding: { right: 50, left: 50 },
            navigator: {
              enabled: true
            }
          }}
        />
      </Card>
    {/if}
  </div>
{/if}

<style>
  :global(.event .combo-chart) {
    margin-bottom: 1rem;
  }

  :global(.ag-chart-tooltip-content .worker-count) {
    margin: 4px 0;
  }
</style>
