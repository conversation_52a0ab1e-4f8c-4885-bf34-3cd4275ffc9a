<script lang="ts">
  import { onMount } from 'svelte';
  import { base } from '$app/paths';
  import { goto } from '$app/navigation';
  import { type MapMouseEvent } from 'mapbox-gl';
  import { Skeleton } from '$lib/components/ui/skeleton';
  import {
    AgChart,
    ButtonGroup,
    HourlyMap,
    HourlyHeader,
    HourlyHeaderLoading,
    HourlyHeaderMobile,
    HourlyHeaderMobileLoading,
    Timestamp
  } from '$lib/components';
  import { Card } from '$lib/components/ui/card';
  import { Button } from '$lib/components/ui/button';
  import * as Select from '$lib/components/ui/select';
  import { findMinEventLevelThreshold, formatAsOfTimestamp } from '$lib/config/utils';
  import {
    Components,
    FeatureFlag,
    Visibility,
    type HourlyForecast,
    type LineChartTooltipParams,
    type MapItem,
    type HourlyChartSelectItem,
    type Region,
    type PieChartTooltipParams
  } from '$lib/types';
  import { getHourlyPredictions } from '$lib/api';
  import store from '$lib/stores/app.svelte';
  import { isSystemic } from '$lib/stores/constants';
  import {
    customFormatter,
    LINECHART_X_AXIS_FORMAT_HOURLY,
    pieChartTooltipRenderer,
    tooltipRenderer
  } from '$lib/config/chart';
  import { formatNumbers, isDataTimestampOutdated } from '$lib/utils';
  import { formatRegionId } from '$lib/config/map';
  import * as Tabs from '$lib/components/ui/tabs';
  import type { AgAxisLabelFormatterParams } from 'ag-charts-enterprise';

  const TIME_RANGES = ['24', '36', '48'];
  const SYSTEM_REGION = {
    name: 'system',
    displayName: 'System',
    centroid: store.appConfig?.center?.value ?? [0, 0],
    type: 'main-territory'
  };

  let hourlyMapItems: MapItem[] = store.componentsConfig?.[Components.HOURLY_MAP]?.items ?? [];
  let options: HourlyChartSelectItem[] =
    store.componentsConfig?.[Components.HOURLY_CHART]?.items ?? [];

  let asOfTimestamp = $state<string>('');
  let customersChartSelectedKeys = $state<number[]>([]);
  let forcastedLineChartData = $state<Record<string, number | string>[]>([]);
  let hourlyForecastCache = $state<Record<string, HourlyForecast>>({});
  let hourlyForecastData = $state<HourlyForecast | null>(null);
  let isLoading = $state<boolean>(true);
  let lastUpdatedDate = $state<string>('');
  let loadingLineChartData = $state<boolean>(true);
  let maxOutageValue = $state<number>(0);
  let outagesChartSelectedKeys = $state<number[]>([]);
  let selectedConfig = $state<HourlyChartSelectItem>(options[0]);
  let selectedRegion = $state<Region>(SYSTEM_REGION);
  let selectedTime = $state<string>(TIME_RANGES[0]);

  let predictionOptions = $derived(options.filter((option) => option.category === 'prediction'));
  let weatherOptions = $derived(options.filter((option) => option.category === 'weather'));

  let isDataOutdated = $derived(isDataTimestampOutdated(lastUpdatedDate, 2));
  let lineChartSeriesItems = $derived(setLineChartSeriesItems(hourlyForecastData, selectedConfig));
  let yAxisMax = $derived(
    findMinEventLevelThreshold(
      selectedRegion.name === 'system' ? 'system' : 'pd-area',
      hourlyForecastData?.zoneSummaryAttributes?.length,
      selectedRegion.name
    )
  );

  $effect(() => {
    forcastedLineChartData = formatLineChartData(hourlyForecastData);
  });

  onMount(async () => {
    if (!validateRouteEntry()) {
      goto(`${base}/`);
      return;
    }

    isLoading = true;
    await fetchHourlyPredictions(TIME_RANGES[0]);
    isLoading = false;
    await Promise.allSettled(
      TIME_RANGES.slice(1).map((hour) => fetchHourlyPredictions(hour, false))
    );
  });

  async function fetchHourlyPredictions(endHour: string, setData = true) {
    const cacheKey = `${selectedRegion.name}-${endHour}`;

    if (!hourlyForecastCache[cacheKey]) {
      const data = await getHourlyPredictions('0', endHour, [selectedRegion.name]);
      hourlyForecastCache[cacheKey] = data;

      if (setData) {
        updateForecastData(data);
      }
    } else if (setData) {
      updateForecastData(hourlyForecastCache[cacheKey]);
    }
  }

  function updateForecastData(data: HourlyForecast) {
    hourlyForecastData = {
      ...data,
      zoneSummaryAttributes: data.zoneSummaryAttributes.map((item) => ({
        ...item,
        threatLevel: store.eventLevels.find(
          ({ range, view, name }) =>
            range?.length &&
            Math.round(item.cumulativeOutages) >= range[0] &&
            Math.round(item.cumulativeOutages) <= range[1] &&
            name === formatRegionId(item.region) &&
            view === (selectedRegion.name === 'system' ? 'pd-area' : 'workgroup')
        )?.category
      }))
    };
    lastUpdatedDate = data.timestamp;
    asOfTimestamp = formatAsOfTimestamp(data.timestamp);
  }

  function validateRouteEntry() {
    return store.featureFlags?.[FeatureFlag.hourly];
  }

  function formatLineChartData(data: HourlyForecast | null): Record<string, any>[] | [] {
    if (!data) return [];
    loadingLineChartData = true;
    const formattedData: Record<string, any> = {};
    let scopedMaxOutageValue = 0;
    data.zonePredictions
      .sort((a, b) => a.zone.localeCompare(b.zone))
      .forEach((zone) => {
        zone.hourlyZonePredictions.forEach((prediction) => {
          const targetTime = prediction.targetTime;
          if (!formattedData[targetTime]) {
            formattedData[targetTime] = { targetTime: new Date(targetTime) };
          }

          const { outageAttributes, weatherAttributes } = prediction.hourlyPredictionAttributes;
          Object.entries(outageAttributes).forEach(([attr, value]) => {
            formattedData[targetTime][`${zone.zone}-${attr}`] = value;
          });

          scopedMaxOutageValue = Math.max(
            scopedMaxOutageValue,
            outageAttributes.pointOutagePrediction
          );

          Object.entries(weatherAttributes).forEach(([attr, value]) => {
            formattedData[targetTime][`${zone.zone}-${attr}`] = value;
          });
        });
      });
    maxOutageValue = scopedMaxOutageValue;
    loadingLineChartData = false;
    return Object.values(formattedData);
  }

  function setLineChartSeriesItems(data: HourlyForecast | null, config: HourlyChartSelectItem) {
    if (!data) return [];

    const zones = data?.zonePredictions?.map((prediction) => prediction.zone);

    return zones.map((i) => ({
      tooltip: {
        renderer: (data: LineChartTooltipParams) => {
          return tooltipRenderer(
            data,
            config.precision,
            data.yKey.includes('threatLevel')
              ? `(${data.datum[`${i}-threatLevelCategory`] ?? 'Unknown'})`
              : config.unit
          );
        }
      },
      type: 'line',
      xKey: 'targetTime',
      yKey: `${i}-${config.value}`,
      yName: i
    }));
  }

  function handleOnRegionClick(e: MapMouseEvent) {
    try {
      if (isSystemic || !selectedRegion || !e?.features?.[0]) return;

      const { sourceLayer = '', properties = {} } = e.features[0];

      if (sourceLayer === 'larger-regions') {
        selectedRegion = store.territories.find(
          (i) => i.name === formatRegionId(properties?.name as string)
        ) as Region;
      }
      if (sourceLayer === 'regions') {
        if (properties?.pdArea === selectedRegion.displayName) {
          selectedRegion = SYSTEM_REGION as Region;
        } else {
          selectedRegion = store.territories.find(
            (i) => i.name === formatRegionId(properties?.pdArea as string)
          ) as Region;
        }
      }

      fetchHourlyPredictions(selectedTime);
    } catch (err) {
      console.error('Error handling region click:', err);
    }
  }
</script>

<svelte:head>
  <title>Hourly</title>
  <meta name="Hourly" content="Hourly" />
</svelte:head>

<div class="hourly flex flex-col gap-sm h-full">
  <div class="flex gap-x-lg gap-y-sm justify-between flex-wrap">
    {#if asOfTimestamp}
      <Timestamp {isDataOutdated} lastUpdatedDate={asOfTimestamp} />
    {:else}
      <Skeleton class="mt-sm w-[200px]" />
    {/if}
    <ButtonGroup
      bind:value={selectedTime}
      onClick={() => {
        fetchHourlyPredictions(selectedTime);
      }}
    >
      {#each TIME_RANGES as time}
        <Button value={time}>
          {time} Hours
        </Button>
      {/each}
    </ButtonGroup>
  </div>
  <div class="normal-view">
    {#if isLoading}
      <HourlyHeaderLoading />
    {:else}
      <HourlyHeader
        outageData={hourlyForecastCache[`system-${selectedTime}`]?.summaryAttributes
          ?.outageAttributes}
        weatherData={hourlyForecastCache[`system-${selectedTime}`]?.summaryAttributes
          ?.weatherAttributes}
      />
    {/if}
  </div>
  <div class="mobile-view">
    {#if isLoading}
      <HourlyHeaderMobileLoading />
    {:else}
      <HourlyHeaderMobile
        outageData={hourlyForecastCache[`system-${selectedTime}`]?.summaryAttributes
          ?.outageAttributes}
        weatherData={hourlyForecastCache[`system-${selectedTime}`]?.summaryAttributes
          ?.weatherAttributes}
      />
    {/if}
  </div>
  <div class="flex gap-sm flex-col lg:flex-row">
    <!-- Desktop View -->
    <div class="normal-view flex flex-col gap-sm !max-h-[464px]">
      {#if !isSystemic}
        {#if isLoading}
          <Skeleton class="h-[32px] w-full" />
        {:else}
          <Select.Root
            type="single"
            value={selectedRegion.name}
            onValueChange={(value) => {
              selectedRegion =
                value === 'system'
                  ? SYSTEM_REGION
                  : (store.territories.find((i) => i.name === value) as Region);
              fetchHourlyPredictions(selectedTime);
            }}
          >
            <Select.Trigger>
              {selectedRegion.displayName}
            </Select.Trigger>
            <Select.Content>
              <Select.Item value="system">System</Select.Item>
              <Select.Group>
                <Select.Label class="ml-2 text-xs my-1 flex">
                  {store.regionsLabel}
                </Select.Label>
                {#each store.territories as territory}
                  <Select.Item value={territory.name}>
                    {territory.displayName}
                  </Select.Item>
                {/each}
              </Select.Group>
            </Select.Content>
          </Select.Root>
        {/if}
      {/if}
      <div class="normal-view flex lg:flex-col gap-sm w-full lg:w-[430px] h-full">
        {#if hourlyMapItems?.some((i) => i.attributeName === Visibility.cumulativeCustomersAffected)}
          <Card
            class={`flex flex-col !h-full ${hourlyMapItems.length === 2 ? 'w-[calc(50%-0.25rem)] lg:w-full' : 'w-full'} !pb-0 `}
            loading={isLoading}
          >
            <p class="text-center overflow-hidden whitespace-nowrap text-ellipsis">
              Forecasted Customers {store.customersAffectedLabel}
            </p>
            <AgChart
              data={hourlyForecastData?.zoneSummaryAttributes || []}
              options={{
                minHeight: 140,
                legend: {
                  enabled: hourlyMapItems.length === 1,
                  listeners: {
                    legendItemClick: ({ itemId }: { itemId: number }) => {
                      const set = new Set(customersChartSelectedKeys);
                      set.has(itemId) ? set.delete(itemId) : set.add(itemId);
                      customersChartSelectedKeys = [...set];
                    }
                  }
                },
                series: [
                  {
                    sectorSpacing: 2,
                    angleKey: 'cumulativeCustomersAffected',
                    calloutLabelKey: 'region',
                    innerRadiusRatio: 0.8,
                    tooltip: { renderer: pieChartTooltipRenderer },
                    type: 'donut',
                    calloutLabel: {
                      enabled: false
                    },
                    innerLabels: [
                      {
                        text: formatNumbers(
                          hourlyForecastData?.zoneSummaryAttributes.reduce(
                            (sum, item, index) =>
                              customersChartSelectedKeys.includes(index)
                                ? sum
                                : sum + item.cumulativeCustomersAffected,
                            0
                          )
                        ),
                        fontWeight: 'bold',
                        fontSize: 16
                      }
                    ]
                  }
                ]
              }}
              onMouseleave={() => (store.activeMapRegion = null)}
            />
          </Card>
        {/if}
        <Card
          class={`flex flex-col !h-full ${hourlyMapItems?.length === 2 ? 'w-[calc(50%-0.25rem)] lg:w-full' : 'w-full'} !pb-0 `}
          loading={isLoading}
        >
          <p class="text-center overflow-hidden whitespace-nowrap text-ellipsis">
            Forecasted {store.capitalizedOutagesLabel}
          </p>
          <AgChart
            data={hourlyForecastData?.zoneSummaryAttributes || []}
            options={{
              minHeight: 140,
              legend: {
                enabled: hourlyMapItems?.length === 1,
                listeners: {
                  legendItemClick: ({ itemId }: { itemId: number }) => {
                    const set = new Set(outagesChartSelectedKeys);
                    set.has(itemId) ? set.delete(itemId) : set.add(itemId);
                    outagesChartSelectedKeys = [...set];
                  }
                }
              },
              series: [
                {
                  sectorSpacing: 2,
                  angleKey: 'cumulativeOutages',
                  calloutLabelKey: 'region',
                  innerRadiusRatio: 0.8,
                  tooltip: {
                    renderer: (data: PieChartTooltipParams) =>
                      pieChartTooltipRenderer(data, true, hourlyMapItems[0].attributeName)
                  },
                  type: 'donut',
                  calloutLabel: {
                    enabled: false
                  },
                  innerLabels: [
                    {
                      text: formatNumbers(
                        hourlyForecastData?.zoneSummaryAttributes.reduce(
                          (sum, item, index) =>
                            outagesChartSelectedKeys.includes(index)
                              ? sum
                              : sum + item.cumulativeOutages,
                          0
                        )
                      ),
                      fontWeight: 'bold',
                      fontSize: 16
                    }
                  ]
                }
              ]
            }}
            onMouseleave={() => (store.activeMapRegion = null)}
          />
        </Card>
      </div>
    </div>
    <!-- Mobile View -->
    <Card class="mobile-view flex !pb-0 justify-center" loading={isLoading}>
      {#if hourlyMapItems?.some((i) => i.attributeName === Visibility.cumulativeCustomersAffected)}
        <div class="w-6/12 flex flex-col">
          <p class="text-center">
            Forecasted <br /> Customers {store.customersAffectedLabel}
          </p>
          <AgChart
            data={hourlyForecastData?.zoneSummaryAttributes || []}
            options={{
              height: 140,
              legend: { enabled: false },
              series: [
                {
                  sectorSpacing: 2,
                  angleKey: 'cumulativeCustomersAffected',
                  calloutLabelKey: 'region',
                  innerRadiusRatio: 0.8,
                  tooltip: { renderer: pieChartTooltipRenderer },
                  type: 'donut',
                  calloutLabel: {
                    enabled: false
                  },
                  innerLabels: [
                    {
                      text: formatNumbers(
                        hourlyForecastData?.zoneSummaryAttributes?.reduce(
                          (acc, d) => acc + d.cumulativeCustomersAffected,
                          0
                        )
                      ),
                      fontWeight: 'bold',
                      fontSize: 16
                    }
                  ]
                }
              ]
            }}
            onMouseleave={() => (store.activeMapRegion = null)}
          />
        </div>
      {/if}
      <div class="w-6/12 flex flex-col">
        <p class="text-center">
          Forecasted <br />{store.capitalizedOutagesLabel}
        </p>
        <AgChart
          data={hourlyForecastData?.zoneSummaryAttributes || []}
          options={{
            height: 140,
            legend: { enabled: false },
            series: [
              {
                sectorSpacing: 2,
                angleKey: 'cumulativeOutages',
                calloutLabelKey: 'region',
                innerRadiusRatio: 0.8,
                tooltip: {
                  renderer: (data: PieChartTooltipParams) =>
                    pieChartTooltipRenderer(data, true, hourlyMapItems[0].attributeName)
                },
                type: 'donut',
                calloutLabel: {
                  enabled: false
                },
                innerLabels: [
                  {
                    text: formatNumbers(
                      hourlyForecastData?.zoneSummaryAttributes?.reduce(
                        (acc, d) => acc + d.cumulativeOutages,
                        0
                      )
                    ),
                    fontWeight: 'bold',
                    fontSize: 16
                  }
                ]
              }
            ]
          }}
          onMouseleave={() => (store.activeMapRegion = null)}
        />
      </div>
    </Card>

    <div
      class="min-h-[342px] !h-full w-full rounded-md overflow-hidden border-solid border !border-borderColor shadow-md"
    >
      <HourlyMap
        records={hourlyForecastData?.zoneSummaryAttributes}
        systemConfig={store.componentsConfig?.[Components.HOURLY_MAP]?.items}
        regionConfig={store.componentsConfig?.[Components.HOURLY_MAP_REGIONS]?.items}
        {selectedRegion}
        loading={isLoading}
        onRegionClick={handleOnRegionClick}
      />
    </div>
  </div>
  <Card class="flex flex-col xl:flex-row flex-1 !h-[20vh] !min-h-[280px] w-full">
    <div class="xl:hidden">
      <Select.Root
        type="single"
        value={selectedConfig.value}
        onValueChange={(value) => {
          selectedConfig = options.find((i) => i.value === value) as HourlyChartSelectItem;
        }}
      >
        <Select.Trigger class="max-w-[280px] mb-1">
          {selectedConfig.text}
        </Select.Trigger>
        <Select.Content>
          <Select.Group>
            <Select.Label>Prediction</Select.Label>
            {#each predictionOptions as { value, text }}
              <Select.Item {value}>{text}</Select.Item>
            {/each}
          </Select.Group>
          <Select.Group>
            <Select.Label>Weather</Select.Label>
            {#each weatherOptions as { value, text }}
              <Select.Item {value}>{text}</Select.Item>
            {/each}
          </Select.Group>
        </Select.Content>
      </Select.Root>
    </div>

    <div class="hidden xl:flex flex-col w-72 pr-4 border-r border-borderColor">
      <Tabs.Root
        value="0"
        variant="solid"
        onValueChange={(val) => {
          if (val === '0') {
            selectedConfig = predictionOptions[0];
          } else {
            selectedConfig = weatherOptions[0];
          }
        }}
      >
        <Tabs.List class="justify-self-start">
          <Tabs.Trigger value="0">Prediction</Tabs.Trigger>
          <Tabs.Trigger value="1">Weather</Tabs.Trigger>
        </Tabs.List>

        <Tabs.Content value="0">
          <div class="flex flex-col overflow-auto">
            {#each predictionOptions as { value, text }}
              <Button
                variant="ghost"
                class="flex justify-start items-center"
                onclick={() =>
                  (selectedConfig = options.find(
                    (i) => i.value === value
                  ) as HourlyChartSelectItem)}
              >
                <span class={value === selectedConfig.value ? 'text-primary' : 'text-transparent'}
                  >●</span
                >
                {text}
              </Button>
            {/each}
          </div>
        </Tabs.Content>
        <Tabs.Content value="1">
          <div class="flex flex-col overflow-auto">
            {#each weatherOptions as { value, text }}
              <Button
                variant="ghost"
                class="flex justify-start items-center"
                onclick={() =>
                  (selectedConfig = options.find(
                    (i) => i.value === value
                  ) as HourlyChartSelectItem)}
              >
                <span class={value === selectedConfig.value ? 'text-primary' : 'text-transparent'}
                  >●</span
                >
                {text}
              </Button>
            {/each}
          </div>
        </Tabs.Content>
      </Tabs.Root>
    </div>

    <AgChart
      className="flex-1"
      loading={loadingLineChartData}
      data={forcastedLineChartData}
      options={{
        axes: [
          {
            position: 'bottom',
            type: 'time',
            label: {
              formatter: (value: AgAxisLabelFormatterParams) =>
                customFormatter(value, LINECHART_X_AXIS_FORMAT_HOURLY)
            }
          },
          {
            position: 'left',
            type: 'number',
            title: {
              text: selectedConfig.yAxis
            },
            ...(selectedConfig.value.toLowerCase().includes('outage') &&
              forcastedLineChartData?.length &&
              maxOutageValue < yAxisMax && {
                max: yAxisMax
              }),
            label: {
              formatter: function ({ value }: AgAxisLabelFormatterParams) {
                if (value % 1 === 0 || selectedConfig.precision) {
                  return value;
                }
                return '';
              }
            },
            crosshair: {
              label: {
                format: `#{0>2.${selectedConfig.precision}f}`
              }
            }
          }
        ],
        padding: { right: 50 },
        series: lineChartSeriesItems
      }}
    />
  </Card>
</div>

<style>
  :global(.hourly .hourly-card) {
    width: 33%;
  }

  :global(.hourly .mobile-view) {
    display: none;
  }

  .hourly .normal-view {
    display: flex;
  }

  @media (max-width: 1200px) {
    :global(.hourly .row .hourly-card) {
      width: calc(50% - 0.25rem);
    }
  }

  @media (max-width: 767px) {
    :global(.hourly .mobile-view) {
      display: flex;
    }

    .hourly .normal-view {
      display: none;
    }

    :global(.hourly .first-row) {
      display: none !important;
    }
  }
</style>
