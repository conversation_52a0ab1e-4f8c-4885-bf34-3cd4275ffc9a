<script lang="ts">
  import { goto } from '$app/navigation';
  import { base } from '$app/paths';
  import * as Alert from '$lib/components/ui/alert';
  import store from '$lib/stores/app.svelte';
  import { Components } from '$lib/types';
  import { onMount } from 'svelte';

  let showError = $state<boolean>(false);

  let navigationData = $derived(store.componentsConfig?.[Components.NAVIGATION_ITEMS]?.items ?? []);

  onMount(() => {
    const splashPage = navigationData.flatMap((i) => i.items).find((d) => d.isSplash);

    if (splashPage) {
      goto(`${base}${splashPage.route}`);
    } else {
      showError = true;
    }
  });
</script>

{#if showError}
  <Alert.Root variant="error">
    <Alert.Title>Error:</Alert.Title>
    <Alert.Description>Failed to retrieve navigation configuration.</Alert.Description>
  </Alert.Root>
{/if}
