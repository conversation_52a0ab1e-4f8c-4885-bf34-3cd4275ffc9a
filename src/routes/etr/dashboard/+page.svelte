<script lang="ts">
  import { DateTime } from 'luxon';
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { base } from '$app/paths';
  import { page } from '$app/stores';
  import {
    getActiveStorm,
    getEtrResults,
    getLatestEtrSessionId,
    getHistoricalEtrResults,
    getHistoricalSessionEtrs,
    getOutagesOverview,
    getSessionEtrs,
    getSessionEtrSummary,
    getStormMetricData,
    getStormAggregateOverview,
    updateStormModel,
    getSimulatedRegions,
    getMaxEstimatedSimulationTimes,
    getCurrentResourcesV2,
    getFutureResourcesV2,
    getResourceVisualizationHistory
  } from '$lib/api';
  import * as Alert from '$lib/components/ui/alert';
  import { Button } from '$lib/components/ui/button';
  import * as Tabs from '$lib/components/ui/tabs';
  import { Skeleton } from '$lib/components/ui/skeleton';
  import store from '$lib/stores/app.svelte';
  import { isForecast, isSystemic, showEta } from '$lib/stores/constants';
  import { FeatureFlag, StormMode } from '$lib/types';
  import type {
    ChartDataPoint,
    EtrSimulation,
    EtrSummaryStats,
    RegionalEtr,
    TerritorySimChartData,
    Resource,
    ResourceData,
    SessionEtrResponse,
    StormData,
    EtrOutageOverview,
    AggregateMetricRecord,
    SimulatedRegion,
    SimulationTime,
    FutureResourcesData,
    UnifiedEtrSimData
  } from '$lib/types';
  import { SimulationOutageScale, RegionalEtrLevel } from '$lib/types';
  import { formatOverviewData, isDataTimestampOutdated, toCapitalizedWords } from '$lib/utils';
  import {
    getDataRequestStartTime,
    OutagesChartScope,
    OutageScales,
    formatWorkerRecords,
    MS_IN_HOUR
  } from '$lib/config/etr';
  import { EditableField, Timestamp } from '$lib/components';
  import { ErrorStatusCodes } from '$lib/config/main';
  import { formatAsOfTimestamp, formatDateTime } from '$lib/config/utils';
  import ETRMain from '$lib/components/Etr/TabsContent/ETRMain.svelte';
  import StormOverview from '$lib/components/Etr/TabsContent/StormOverview.svelte';
  import ETRSimulations from '$lib/components/Etr/TabsContent/ETRSimulations.svelte';
  import UpdateEtr from '$lib/components/Etr/UpdateETR.svelte';

  let activeStorm = $state<StormData | null>(null);
  let errorMessage = $state<string>('');
  let systemOverviewData = $state<AggregateMetricRecord[]>([]);
  let isHistoricalSession = $state<boolean>(true);
  let lastUpdatedDate = $state<string>('');
  let regionalEtrs = $state<RegionalEtr[]>([]);
  let resources: Resource[] = [];
  let selectedEtrLevel = $state<OutageScales>(OutageScales.MEDIUM);
  let sessionId: string = '';
  let showSystemView = $state<boolean>(true);

  let lineChartDataCache: Record<OutagesChartScope, Record<string, any>[]> = {
    system: [],
    region: []
  };
  let summariesBarChartData = $state<Record<string, any>[]>([]);
  let barChartData = $state<Record<string, any>[]>([]);

  let chartData = $state<ChartDataPoint[]>([]);
  let isLoadingChartData = $state<boolean>(true);
  let isLoadingLineChartData = $state<boolean>(false);

  let isLoadingActiveStorm = $state<boolean>(true);
  let isLoadingSimulatedRegions = $state(<boolean>true);
  let isLoadingSessionETRs = $state<boolean>(true);
  let isLoadingEtrSimulationResults = $state<boolean>(true);
  let isLoadingHistoricEtrResults = $state<boolean>(true);
  let isDataOutdated = $state<boolean>(false);
  let resourcesData = $state<
    {
      timestamp: string;
      totalWorkers: number;
      region: string;
      date: Date;
      resources: Record<string, number>;
    }[]
  >([]);

  // ETR
  let datePickerMinDate = $state<string | null>(null);
  let datePickerMaxDate = $state<string | null>(null);
  let etrResults = $state<UnifiedEtrSimData[]>([]);
  let systemRestoration = $state<{ low: string; medium: string; high: string } | null>(null);
  let systemGenerationTime: string | null = null;
  let targetedETR = $state<string | null>(null);
  let targetedETRPreset = $state<string | null>(null);
  let tooltipData = $state<Record<string, string[]>>({});

  // Resource Planning
  let currentResourcesRowData = $state<ResourceData[]>([]);
  let futureResourcesRowData: FutureResourcesData[] = [];
  let isLoadingCurrentResources = $state<boolean>(true);
  let isLoadingFutureResources = $state<boolean>(false);

  let isLoadingSimulationTimes = $state<boolean>(true);
  let simulationTimes = $state<Record<SimulationOutageScale, SimulationTime | null>>({
    [SimulationOutageScale.low]: null,
    [SimulationOutageScale.medium]: null,
    [SimulationOutageScale.high]: null
  });

  let simulationRegions: Record<string, SimulatedRegion[]> = {};
  let currentSimulationResults = $state<SimulatedRegion[]>([]);
  let sessionDate = $state<Date>(new Date());

  let isActiveSession = $derived(!isHistoricalSession);
  let chartScope = $derived(showSystemView ? OutagesChartScope.SYSTEM : OutagesChartScope.REGION);
  let filteredEtrs = $derived(regionalEtrs.filter((e) => e.outageScale === selectedEtrLevel));
  let historicalSessionId = $derived($page.url.searchParams.get('sessionId'));
  let lineChartData = $derived(lineChartDataCache[chartScope]);
  let stormId = $derived($page.url.searchParams.get('stormId'));
  let territories = $derived(
    currentResourcesRowData.map((d) => ({
      territoryId: d.territoryId,
      territoryName: d.territoryName
    }))
  );

  $effect(() => {
    barChartData = [...summariesBarChartData, ...systemOverviewData];
  });

  $effect(() => {
    if (!showSystemView) {
      fetchRegionData();
    }
  });

  $effect(() => {
    if (!isSystemic && chartData.length) {
      const data = chartData
        .map((d) => d.date)
        .filter((val) => !!val)
        .sort((a, b) => a.localeCompare(b));
      targetedETRPreset = data[0];
      datePickerMinDate = DateTime.fromISO(data[0]).toFormat('MM/dd/yyyy');
      datePickerMaxDate = DateTime.fromISO(data[data.length - 1]).toFormat('MM/dd/yyyy');
    }
  });

  onMount(async () => {
    if (validateRouteEntry()) {
      isHistoricalSession = !!$page.url.searchParams.get('sessionId');
      targetedETR = $page.url.searchParams.get('targetedETR');

      if (isHistoricalSession) {
        getHistoricalData();
      } else {
        refreshPage();
      }
    } else {
      goto(`${base}/`);
    }
  });

  async function fetchStormMetricData(historicalSessionId: string, stormId: string) {
    try {
      isLoadingCurrentResources = true;
      const response = await getStormMetricData('resources', stormId, isSystemic);

      if (response?.metrics?.length) {
        const sessionMetric = response.metrics.find((m) => m.sessionId === historicalSessionId);

        if (sessionMetric) {
          const sessionResources = sessionMetric.records.map((r) => ({
            arrivalTime: null,
            territoryId: '',
            territoryName: r.region,
            totalResources: { ...r.resources }
          }));
          setResources(sessionResources);
        }
      }
    } catch (e) {
      console.error(e);
    }

    isLoadingCurrentResources = false;
  }

  async function getHistoricalData() {
    if (!historicalSessionId) {
      goto(`${base}/etr`);
      return;
    }

    await fetchSessionEtrs(historicalSessionId);

    if (stormId) {
      Promise.all([
        fetchStormMetricData(historicalSessionId, stormId),
        getHistoricalResultsChartData(historicalSessionId, stormId)
      ]); // Need fetchSessionEtrs to resolve first for systemGenerationTime
    }
  }

  function validateRouteEntry() {
    return store.featureFlags?.[FeatureFlag.etr];
  }

  async function refreshPage() {
    if (!activeStorm) {
      isLoadingActiveStorm = true;

      try {
        activeStorm = await getActiveStorm();
        isLoadingActiveStorm = false;
        if (!activeStorm) {
          goto(`${base}/etr`);
        }
      } catch (e) {
        console.error(e);
      }
    }

    if (activeStorm) {
      if (!sessionId) {
        try {
          const id = await getLatestEtrSessionId(activeStorm.id);
          sessionId = id;
        } catch (err) {
          errorMessage = `Could not load ${store.etrLabel} session`;
          console.error(err);
        }
      }

      fetchSessionEtrs(sessionId);

      if (showEta) {
        Promise.allSettled([
          fetchSimulatedRegions(SimulationOutageScale.medium, RegionalEtrLevel.current),
          fetchMaxSimulationTimes(SimulationOutageScale.medium)
        ]).catch((err) => console.error(err));
      }

      isLoadingChartData = true;
      Promise.allSettled([
        fetchOutagesData(sessionId, OutagesChartScope.SYSTEM),
        fetchStormOverviewData(),
        fetchOutagesOverview(sessionId, OutagesChartScope.SYSTEM)
      ])
        .catch((err) => console.error(err))
        .finally(() => {
          isLoadingChartData = false;
        });

      try {
        await fetchFutureResources();
        await fetchResources();
        await getResultsChartData();
        await fetchResourceHistory();

        if (targetedETR) interpolateProjectedData();
      } catch (err) {
        console.error(err);
      }
    } else {
      isLoadingSimulatedRegions = false;
      errorMessage = 'No active storm';
    }
  }

  function setResources(rs: Resource[]) {
    resources = rs;
    currentResourcesRowData = rs
      .sort((a, b) => a.territoryName.localeCompare(b.territoryName))
      .map((r, i) => {
        const total = Object.keys(r.totalResources).reduce(
          (acc, key) => acc + r.totalResources[key],
          0
        );
        return {
          ...r.totalResources,
          id: i + 1,
          futureTotal: calcFutureTerritoryCount(r.territoryId),
          territoryId: r.territoryId,
          territoryName: r.territoryName,
          total
        };
      });
  }

  function setRegionalEtrs({ regionalETRs, stormStartDate }: SessionEtrResponse) {
    regionalEtrs = regionalETRs
      .filter((e) => isSystemic || e.type === 'current')
      .map((e, i) => {
        const rowData: RegionalEtr & { targetedETR?: string } = { ...e, id: i + 1 };
        // Account for "targetedETR" query param state
        if (targetedETR) rowData.targetedETR = targetedETR;
        if (DateTime.fromISO(stormStartDate).diffNow('days').days > 1) {
          rowData.activeCustOutages = '-';
          rowData.activeOutages = '-';
        }

        return rowData;
      })
      .sort((a, b) => a.location.localeCompare(b.location));
  }

  async function fetchSessionEtrs(id: string) {
    isLoadingSessionETRs = true;

    try {
      const response = isActiveSession
        ? await getSessionEtrs(id)
        : await getHistoricalSessionEtrs(id);

      if (Object.keys(response).length) {
        sessionDate = new Date(response.generationTime);
        lastUpdatedDate = formatAsOfTimestamp(response.generationTime);
        isDataOutdated = isDataTimestampOutdated(response.generationTime);
        setRegionalEtrs(response);
        systemRestoration = response.systemRestoration;
        systemGenerationTime = response.generationTime;

        tooltipData = formatTooltipData(response?.forecastMetadata?.tooltip_vals);
      }
    } catch (e: any) {
      console.error(e);
      const isManualStorm = activeStorm?.generationType === 'manual';
      const is404 = e?.response?.status === ErrorStatusCodes.NOT_FOUND;
      if (isManualStorm && is404) {
        console.error('Manual active storm ETRs are not available yet');
        goto(`${base}/etr`);
      }
    } finally {
      isLoadingSessionETRs = false;
    }
  }

  function onSelectTargetedEtr() {
    if (!targetedETR) targetedETR = targetedETRPreset;
  }

  function setLineChartData(summaries: EtrOutageOverview[], chartDataScope: OutagesChartScope) {
    lineChartDataCache[chartDataScope].push(
      ...summaries.map((s) => {
        const data: {
          date: Date;
          pastOutageCount?: number;
          outageCount?: number;
          [key: string]: any;
        } = { date: new Date(s.forecastDateTime) };

        if (chartDataScope === OutagesChartScope.SYSTEM) {
          if (s.historical) {
            // Outages
            data.pastOutageCount = s.activeOutage;
          } else {
            data.outageCount = s.activeOutage;
          }
        } else if (s.scope) {
          data[s.scope] = s.activeOutage;
        }
        return data;
      })
    );
  }

  function setForecastBarChartData(summaries: EtrSummaryStats[]) {
    if (!summaries) return;
    const newOutageData = summaries.map((s) => ({
      date: new Date(s.forecastDateTime),
      newOutageCount: s.totalNewOutages
    }));
    const assessedOutageData = summaries.map((s) => ({
      date: new Date(s.forecastDateTime),
      assessedOutageCount: s.totalAssessedOutages
    }));

    const restoredOutageData = summaries.map((s) => {
      const isFuture = new Date(s.forecastDateTime).getTime() > Date.now();
      const attr = isFuture ? 'projRestOutageCount' : 'restOutageCount';

      return {
        date: new Date(s.forecastDateTime),
        [attr]: s.totalRestoredOutages
      };
    });

    summariesBarChartData = [...newOutageData, ...restoredOutageData, ...assessedOutageData].filter(
      (d: {
        date: Date;
        newOutageCount?: number;
        projRestOutageCount?: number;
        restOutageCount?: number;
        assessedOutageCount?: number;
      }) => {
        return (
          Number(d.newOutageCount) > 0 ||
          Number(d.projRestOutageCount) > 0 ||
          Number(d.restOutageCount) > 0 ||
          Number(d.assessedOutageCount) > 0
        );
      }
    );
  }

  async function fetchOutagesOverview(id: string, chartDataScope: OutagesChartScope) {
    if (!activeStorm) {
      return;
    }

    const startTime = getDataRequestStartTime(activeStorm);

    try {
      const response = await getOutagesOverview(chartDataScope, isForecast, id, startTime);
      const outages = response.filter(
        ({ scope }) => chartDataScope === OutagesChartScope.SYSTEM || scope !== 'System'
      );
      if (outages.length) setLineChartData(outages, chartDataScope);
    } catch (err) {
      console.error(err);
    }
  }

  async function fetchRegionData() {
    if (lineChartDataCache[OutagesChartScope.REGION].length) {
      return;
    }

    isLoadingLineChartData = true;
    await fetchOutagesOverview(sessionId, OutagesChartScope.REGION);
    lineChartData = lineChartDataCache[OutagesChartScope.REGION];
    isLoadingLineChartData = false;
  }

  async function fetchRestoredOutagesData(id: string, chartDataScope: OutagesChartScope) {
    if (activeStorm) {
      const startTime = getDataRequestStartTime(activeStorm);

      try {
        const overview = await getOutagesOverview(chartDataScope, isForecast, id, startTime);

        const outageData = overview.map((s) => ({
          date: new Date(s.forecastDateTime),
          activeOutageCount: s.activeOutage,
          restOutageCount: s.restoredOutage,
          cumulativeRestoredOutage: s.cumulativeRestoredOutage
        }));

        summariesBarChartData = outageData.filter(
          (d) => d.activeOutageCount > 0 || d.restOutageCount > 0
        );
        lineChartDataCache[OutagesChartScope.SYSTEM].push(...outageData);
      } catch (err) {
        console.error(err);
      }
    }
  }

  async function fetchForecastOutagesData(id: string, chartDataScope: OutagesChartScope) {
    try {
      const summaryResponse = await getSessionEtrSummary(id, chartDataScope);
      setForecastBarChartData(summaryResponse.summaries);
    } catch (err) {
      console.error(err);
    }
  }

  function fetchOutagesData(id: string, chartDataScope: OutagesChartScope) {
    return isForecast
      ? fetchForecastOutagesData(id, chartDataScope)
      : fetchRestoredOutagesData(id, chartDataScope);
  }

  async function fetchStormOverviewData() {
    if (!activeStorm) return;

    const endTime = new Date().toISOString();
    const startTime = getDataRequestStartTime(activeStorm);

    try {
      const stormOverviewData = await getStormAggregateOverview(startTime, endTime);

      systemOverviewData = formatOverviewData(stormOverviewData?.regionalMetrics?.System);
      lineChartDataCache[OutagesChartScope.SYSTEM].push(
        ...formatOverviewData(stormOverviewData?.regionalMetrics?.System)
      );
    } catch (err) {
      console.error(err);
    }
  }

  function calcFutureTerritoryCount(id: string) {
    if (futureResourcesRowData.length) {
      return futureResourcesRowData
        .filter((d) => d.toTerritoryId === id)
        .reduce((acc, d) => acc + d.total, 0);
    } else {
      return 0;
    }
  }

  async function fetchResources() {
    if (!activeStorm) return;
    isLoadingCurrentResources = true;

    try {
      const res = await getCurrentResourcesV2(activeStorm.id);
      currentResourcesRowData = res.regionalResourceStates
        .sort((a, b) => a.territoryName.localeCompare(b.territoryName))
        .map((r, i) => {
          const total = Object.keys(r.resources).reduce((acc, key) => acc + r.resources[key], 0);

          return {
            ...r.resources,
            id: i + 1,
            futureTotal: calcFutureTerritoryCount(r.territoryId),
            territoryId: r.territoryId,
            territoryName: r.territoryName,
            total
          };
        });
    } catch {
      errorMessage = 'Could not load current resources';
    } finally {
      isLoadingCurrentResources = false;
    }
  }

  async function fetchFutureResources() {
    if (!activeStorm) return;
    isLoadingFutureResources = true;

    try {
      const res = await getFutureResourcesV2(activeStorm.id);
      futureResourcesRowData = res.map((r) => {
        const total = Object.keys(r.resources).reduce((acc, key) => acc + r.resources[key], 0);
        return {
          ...r,
          total,
          resourceType: toCapitalizedWords(Object.keys(r.resources)[0]),
          status: DateTime.now().toISO() < r.executionTime ? 'Enroute' : 'Completed'
        };
      });
    } catch (e) {
      errorMessage = 'Could not load future resources';
    }

    isLoadingFutureResources = false;
  }

  function getHoursToRestoration(restorationDate: string) {
    const forecastStartPoint = activeStorm?.stormStartDate
      ? DateTime.fromJSDate(activeStorm.stormStartDate)
      : DateTime.now();
    const startPoint = systemGenerationTime
      ? DateTime.fromISO(systemGenerationTime)
      : DateTime.now();

    return DateTime.fromISO(restorationDate).diff(
      activeStorm?.stormMode === StormMode.FORECAST ? forecastStartPoint : startPoint,
      'hours'
    ).hours;
  }

  function setForecastChartData({ high, low, medium }: EtrSimulation['systemRestoration']) {
    // Revert this fix once we find a library for a vertical and line graph integrated together
    const hi = high
      .map((s) => ({
        date: s.projectedETR,
        group: '90th Percentile',
        hoursToRestoration: getHoursToRestoration(s.projectedETR),
        isFilled: !s.simulation,
        value: s.totalResources
      }))
      .sort((a, b) => a.date?.localeCompare(b.date));
    const md = medium
      .map((s) => ({
        date: s.projectedETR,
        group: '50th Percentile',
        hoursToRestoration: getHoursToRestoration(s.projectedETR),
        isFilled: !s.simulation,
        value: s.totalResources
      }))
      .sort((a, b) => a.date?.localeCompare(b.date));
    const lo = low
      .map((s) => ({
        date: s.projectedETR,
        group: '10th Percentile',
        hoursToRestoration: getHoursToRestoration(s.projectedETR),
        isFilled: !s.simulation,
        value: s.totalResources
      }))
      .sort((a, b) => a.date?.localeCompare(b.date));
    chartData = [...hi, ...lo, ...md].sort((a, b) => {
      if (a.value === b.value) {
        return Number(a.isFilled) - Number(b.isFilled);
      }
      return a.value - b.value;
    });
  }

  function setTerritoryChartData(data: TerritorySimChartData[]) {
    chartData = data
      .map((d) => ({
        date: d.estimatedRestoration,
        group: d.territoryName,
        hoursToRestoration: getHoursToRestoration(d.estimatedRestoration),
        isFilled: !d.isSimulation,
        isHighlighted: d.userSimulation,
        value: d.totalResources
      }))
      .filter((d) => d.hoursToRestoration >= 0)
      .sort((a, b) => {
        return a.group === b.group
          ? a.value === b.value
            ? Number(a.isFilled) - Number(b.isFilled) ||
              Number(b.isHighlighted) - Number(a.isHighlighted)
            : a.value - b.value
          : a.group.localeCompare(b.group);
      });
  }

  async function getHistoricalResultsChartData(historicalSessionId: string, stormId: string) {
    isLoadingHistoricEtrResults = true;
    try {
      const response = await getHistoricalEtrResults(stormId, isSystemic, historicalSessionId);

      if (isSystemic) {
        setForecastChartData((response as EtrSimulation).systemRestoration);
      } else {
        setTerritoryChartData(response as TerritorySimChartData[]);
      }
    } catch (e) {
      console.error(e);
    } finally {
      isLoadingHistoricEtrResults = false;
    }
  }

  function unifyEtrResults(response: EtrSimulation | TerritorySimChartData[]) {
    if (isSystemic) {
      const { low, medium, high } = (response as EtrSimulation).systemRestoration;
      etrResults = [
        low.map((d) => ({
          id: '10th Percentile',
          isSimulation: d.simulation,
          projectedETR: d.projectedETR,
          totalResources: d.totalResources
        })),
        medium.map((d) => ({
          id: '50th Percentile',
          isSimulation: d.simulation,
          projectedETR: d.projectedETR,
          totalResources: d.totalResources
        })),
        high.map((d) => ({
          id: '90th Percentile',
          isSimulation: d.simulation,
          projectedETR: d.projectedETR,
          totalResources: d.totalResources
        }))
      ].flat();
    } else {
      etrResults = (response as TerritorySimChartData[]).map((d) => ({
        id: d.territoryName,
        isSimulation: d.isSimulation,
        projectedETR: d.estimatedRestoration,
        totalResources: d.totalResources
      }));
    }
  }

  async function getResultsChartData() {
    if (activeStorm) {
      isLoadingEtrSimulationResults = true;
      try {
        const response = await getEtrResults(activeStorm.id, isSystemic);
        unifyEtrResults(response);

        if (isSystemic) {
          setForecastChartData((response as EtrSimulation).systemRestoration);
        } else {
          setTerritoryChartData(response as TerritorySimChartData[]);
        }
      } catch (e) {
        console.error(e);
      } finally {
        isLoadingEtrSimulationResults = false;
      }
    }
  }

  function calculateResource(
    xMinRes: number,
    xMaxRes: number,
    yMinDiff: number,
    yMaxDiff: number,
    target: number
  ): number {
    if (yMinDiff === yMaxDiff) {
      throw new Error('yMinDiff and yMaxDiff cannot be the same to avoid division by zero.');
    }

    const val = xMinRes + (target - yMaxDiff) * ((xMaxRes - xMinRes) / (yMinDiff - yMaxDiff));
    return val;
  }

  function interpolateProjectedData() {
    if (chartData.length && targetedETR) {
      const targetedLuxonDate = DateTime.fromISO(targetedETR);
      const territoryNames = territories.map((t) => t.territoryName);
      territoryNames.forEach((territory) => {
        let resource = 0;
        const etrData = filteredEtrs.find((e) => e.location === territory);
        const isMoreResourcesNeeded =
          targetedLuxonDate < DateTime.fromISO(String(etrData?.projectedETR));

        if (isMoreResourcesNeeded) {
          const fData = chartData
            .filter((d) => d.group === territory)
            .sort((a, b) => a.date.localeCompare(b.date));
          if (fData.length < 2) return;
          let rPoint = fData.find((d) => {
            const luxonDate = DateTime.fromISO(d.date);
            return targetedLuxonDate < luxonDate;
          });
          let lPoint = fData.reverse().find((d) => {
            const luxonDate = DateTime.fromISO(d.date);
            return luxonDate < targetedLuxonDate;
          });

          if (lPoint && rPoint) {
            const start = DateTime.fromISO(lPoint.date);
            const end = DateTime.fromISO(rPoint.date);
            const diffToTarget = targetedLuxonDate.diff(start, 'minutes');
            const minDiffToTarget = diffToTarget.toObject().minutes;
            const diffToEnd = end.diff(start, 'minutes');
            const minDiffToEnd = diffToEnd.toObject().minutes;
            resource = calculateResource(
              Number(lPoint.value),
              Number(rPoint.value),
              0,
              Number(minDiffToEnd),
              Number(minDiffToTarget)
            );
          } else if (rPoint) {
            // No left side reference (not available) - take right side value
            resource = Number(rPoint.value);
          }
        }

        const rowData = currentResourcesRowData.find((d) => d.territoryName === territory)!;
        rowData.resourcesNeeded = resource;
        rowData.delta = rowData.total - resource;
      });
      currentResourcesRowData = currentResourcesRowData;
    }
  }

  function onTargetDateChanged(date: DateTime) {
    targetedETR = date.toISO();
    if (targetedETR) $page.url.searchParams.set('targetedETR', targetedETR);
    goto(`?${$page.url.searchParams.toString()}`);
    interpolateProjectedData();
    // Add targetedETR to all rows to apply cellStyle comparison
    regionalEtrs = regionalEtrs.map((e) => ({ ...e, targetedETR }));
  }

  function onResetTargetedETR() {
    targetedETR = null;
    $page.url.searchParams.delete('targetedETR');
    goto(`?${$page.url.searchParams.toString()}`);
    // Reset "-" under "Needed To Reach Target" column
    setResources(resources);
    // Add targetedETR to all rows to apply cellStyle comparison
    regionalEtrs = regionalEtrs.map((e) => ({ ...e, targetedETR }));
  }

  async function updateStormName(id: string | undefined, name: string = '') {
    if (id) {
      await updateStormModel(id, 'displayName', name);
      getActiveStorm();
    }
  }

  async function fetchSimulatedRegions(
    outageScale: SimulationOutageScale,
    regionEtrType: RegionalEtrLevel
  ) {
    const id = isHistoricalSession ? historicalSessionId : sessionId;

    if (!id) return;

    if (simulationRegions[`${outageScale}-${regionEtrType}`]) {
      currentSimulationResults = simulationRegions[`${outageScale}-${regionEtrType}`];
      return;
    }

    try {
      isLoadingSimulatedRegions = true;
      const data = await getSimulatedRegions(id, outageScale, regionEtrType);
      simulationRegions[`${outageScale}-${regionEtrType}`] = data;
      currentSimulationResults = data;
    } catch (e) {
      console.error(e);
    } finally {
      isLoadingSimulatedRegions = false;
    }
  }

  async function fetchMaxSimulationTimes(outageScale: SimulationOutageScale) {
    const id = isHistoricalSession ? historicalSessionId : sessionId;

    if (!id) return;

    if (simulationTimes[outageScale]) {
      return;
    }
    try {
      isLoadingSimulationTimes = true;
      const times = await getMaxEstimatedSimulationTimes(
        outageScale,
        Object.values(RegionalEtrLevel),
        id
      );
      simulationTimes[outageScale] = times;
    } catch (e) {
      console.error(e);
    } finally {
      isLoadingSimulationTimes = false;
    }
  }
  function formatTooltipData(tooltipData: Record<string, any>[] = []): Record<string, string[]> {
    return tooltipData.reduce((acc, item) => {
      acc[item['result_id']] = item['result_str'];
      return acc;
    }, {});
  }

  async function fetchResourceHistory() {
    if (!activeStorm?.stormStartDate || !activeStorm?.stormEndDate) return;

    const startTimeISO = getDataRequestStartTime(activeStorm);
    const endDate = DateTime.utc();

    try {
      const data = await getResourceVisualizationHistory(startTimeISO, endDate.toISO());

      const durationInHours = endDate.diff(DateTime.fromISO(startTimeISO), 'hours').hours;

      resourcesData = formatWorkerRecords(
        data.historicalSystemStates,
        durationInHours,
        true,
        endDate,
        MS_IN_HOUR
      );
    } catch (error) {
      console.error(error);
    }
  }
</script>

<svelte:head>
  <title>Storm Insights</title>
  <meta name="Storm Insights" content={store.etrLabel} />
</svelte:head>

{#if errorMessage}
  <Alert.Root variant="error">
    <Alert.Title>Error:</Alert.Title>
    <Alert.Description>{errorMessage}</Alert.Description>
  </Alert.Root>
{/if}

<section class="etr flex flex-col">
  <div class="flex items-center w-full gap-md mb-sm">
    <Button
      class="rounded-full"
      size="icon"
      tooltip="Back to Active Summary page"
      onclick={() => {
        goto(isHistoricalSession ? `${base}/etr-retrospective/events/${stormId}` : `${base}/etr`);
      }}
    >
      <span class="material-icons">arrow_back</span>
    </Button>

    {#if isHistoricalSession}
      {#if isLoadingSessionETRs}
        <Skeleton class="h-[19px] mt-[2px] w-[200px]" />
      {:else if lastUpdatedDate}
        <span class="p-xs text-accentText">As of {lastUpdatedDate}</span>
      {:else}
        <span class="p-xs text-accentText"> No simulations found for historical session </span>
      {/if}
    {:else if isLoadingActiveStorm}
      <div class="flex flex-col w-full gap-sm">
        <Skeleton class="h-[36px] w-1/2" />
        <Skeleton class="h-[32px] w-[200px]" />
      </div>
    {:else if activeStorm}
      <div class="flex justify-between gap-sm flex-wrap w-full">
        <div class="flex flex-col gap-xs">
          <EditableField
            class="text-xl w-fit"
            value={activeStorm?.displayName}
            onChange={(value) => updateStormName(activeStorm?.id, value)}
          />
          {#if isLoadingSessionETRs}
            <Skeleton class="w-[200px]" />
          {:else if lastUpdatedDate}
            <Timestamp {isDataOutdated} {lastUpdatedDate} />
          {:else}
            <span class="p-xs text-accentText">
              No simulations found as of {formatDateTime(new Date(), true)}
            </span>
          {/if}
        </div>
      </div>
    {/if}
  </div>

  <Tabs.Root value="0">
    <div class="flex justify-between gap-sm flex-wrap">
      <Tabs.List>
        <Tabs.Trigger value="0">
          {showEta ? 'Restoration Summary' : store.etrLabel}
        </Tabs.Trigger>
        {#if isActiveSession}
          <Tabs.Trigger value="1">Storm Overview</Tabs.Trigger>
          {#if !showEta}
            <Tabs.Trigger value="2">Simulation</Tabs.Trigger>
          {/if}
        {/if}
      </Tabs.List>

      {#if activeStorm?.stormMode === StormMode.ACTIVE}
        <UpdateEtr {activeStorm} {refreshPage} />
      {/if}
    </div>

    <Tabs.Content value="0">
      <ETRMain
        {isActiveSession}
        {isLoadingSimulatedRegions}
        isLoadingResources={isLoadingCurrentResources || isLoadingFutureResources}
        {regionalEtrs}
        {filteredEtrs}
        stormMode={activeStorm?.stormMode || null}
        {targetedETR}
        {selectedEtrLevel}
        {systemRestoration}
        {currentResourcesRowData}
        {chartData}
        {onSelectTargetedEtr}
        onSelectEtrLevel={(level) => (selectedEtrLevel = level)}
        {simulationTimes}
        {isLoadingSimulationTimes}
        {currentSimulationResults}
        onFetchSimulatedRegions={fetchSimulatedRegions}
        onFetchMaxSimulationTimes={fetchMaxSimulationTimes}
        {tooltipData}
        stormStartDate={activeStorm?.stormStartDate}
        {sessionDate}
        {isLoadingSessionETRs}
        {isLoadingEtrSimulationResults}
        {isLoadingHistoricEtrResults}
        {datePickerMaxDate}
        {datePickerMinDate}
        {onTargetDateChanged}
        {onResetTargetedETR}
      />
    </Tabs.Content>
    {#if isActiveSession}
      <Tabs.Content value="1">
        <StormOverview
          bind:showSystemView
          {activeStorm}
          {barChartData}
          {lineChartData}
          {isLoadingChartData}
          {isLoadingLineChartData}
          {resourcesData}
        />
      </Tabs.Content>
      {#if !showEta}
        <Tabs.Content value="2">
          <ETRSimulations {chartData} {etrResults} />
        </Tabs.Content>
      {/if}
    {/if}
  </Tabs.Root>
</section>
