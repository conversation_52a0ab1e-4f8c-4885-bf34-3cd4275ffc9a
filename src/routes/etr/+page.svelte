<script lang="ts">
  import { onMount } from 'svelte';
  import { DateTime } from 'luxon';
  import { goto } from '$app/navigation';
  import { base } from '$app/paths';
  import {
    getActiveStorm,
    getActiveOutages,
    getLatestEtrSessionId,
    postEtrEvent,
    getStormAggregateOverview,
    getResourceVisualizationHistory
  } from '$lib/api';
  import { getUserData } from '$lib/authentication';
  import { AgChart, EtrMap, Timestamp, Tooltip } from '$lib/components';
  import { Card } from '$lib/components/ui/card';
  import * as Alert from '$lib/components/ui/alert';
  import { Button } from '$lib/components/ui/button';
  import { ErrorStatusCodes } from '$lib/config/main';
  import { formatNumbers, isDataTimestampOutdated } from '$lib/utils';
  import { pieChartTooltipRenderer } from '$lib/config/chart';
  import store from '$lib/stores/app.svelte';
  import { showEta, showUnconfirmedOutages } from '$lib/stores/constants';
  import { Components, FeatureFlag, OutageSource, StormMode, Visibility } from '$lib/types';
  import {
    groupByTimestamp,
    groupSystemStatesByTimestamp,
    formatWorkerRecords,
    MS_IN_HOUR,
    mergeResourceTotals
  } from '$lib/config/etr';
  import type {
    StormData,
    OutageRecord,
    OutagesData,
    AggregateMetricRecord,
    AggregateMetricRecordWithRegion,
    EventBody
  } from '$lib/types';
  import { Skeleton } from '$lib/components/ui/skeleton';
  import { formatAsOfTimestamp, formatDateTime } from '$lib/config/utils';

  const HOURS_OFFSET = 2.5;
  const GRANULARITY = 10;
  const STORM_MODES: Record<any, { label: string; icon: string; color: string }> = {
    [StormMode.ACTIVE]: { label: 'Active Storm', icon: 'warning', color: 'bg-danger' },
    [StormMode.FORECAST]: { label: 'Storm Forecasted', icon: 'schedule', color: 'bg-warning' },
    [StormMode.POST_RESTORATION]: {
      label: 'Restoration Complete',
      icon: 'check_circle',
      color: 'bg-success'
    }
  };

  let activeOutages = $state<OutagesData | null>(null);
  let activeStorm = $state<StormData | null>(null);
  let activeSystemOutageRecord = $state<OutageRecord | null>(null);
  let defaultErrorMessage: string = 'An internal server error occurred. Please refresh the page.';
  let errorMessage = $state<string>('');
  let isDataOutdated = $state<boolean>(false);
  let lastUpdatedDate = $state<string | null>(null);
  let outageRecords = $state<AggregateMetricRecord[]>([]);
  let isLoading = $state<boolean>(true);
  let isSaving = $state<boolean>(false);
  let showDashboardLink = $state<boolean>(true);
  let source = $state<OutageSource>(OutageSource.refined);
  let systemOutageRecord = $state<AggregateMetricRecordWithRegion | null>(null);
  let systemOverviewData = $state<Record<string, AggregateMetricRecordWithRegion[]>>({});
  let systemStateRecords = $state<
    Record<string, { timestamp: string; totalWorkers: number; region: string; date: Date }[]>
  >({});

  let formattedSystemOverviewData = $derived(
    mergeResourceTotals(systemStateRecords, systemOverviewData)
  );
  let isStormActive = $derived<boolean>(activeStorm?.stormMode === StormMode.ACTIVE);

  onMount(async () => {
    if (validateRouteEntry()) {
      try {
        await Promise.allSettled([
          getActiveStormData(),
          getActiveOutagesData(),
          fetchStormOverviewData(),
          fetchResourceHistory()
        ]);
      } catch (e) {
        errorMessage = 'Could not load storm data';
      }

      await validateDashboardEntry();
      isLoading = false;
    } else {
      goto(`${base}/`);
    }
  });

  function validateRouteEntry() {
    return store.featureFlags?.[FeatureFlag.etr];
  }

  function getEventBody(event: string): EventBody {
    const stormDisplayName =
      event === 'startStorm'
        ? `Storm Event: ${DateTime.now().toFormat('yyyy-MM-dd HH:mm:ss ZZZZ')}`
        : null;
    return {
      data: {
        stormDisplayName,
        stormId: event === 'endStorm' ? activeStorm!.id : null,
        user: getUserData()
      },
      event
    };
  }

  async function getActiveStormData() {
    activeStorm = await getActiveStorm();
  }

  async function getActiveOutagesData() {
    activeOutages = await getActiveOutages();

    if (activeOutages) {
      activeSystemOutageRecord =
        activeOutages.outageRecords.find(({ region }) => region === 'System') ?? null;
      const { lastUpdated } = activeOutages;
      lastUpdatedDate = formatAsOfTimestamp(lastUpdated);
      isDataOutdated = isDataTimestampOutdated(lastUpdated);
    }
  }

  async function postStormEvent(event: 'startStorm' | 'endStorm') {
    isSaving = true;

    try {
      await postEtrEvent(getEventBody(event));
      getActiveStormData();
      showDashboardLink = event === 'endStorm';
    } catch (e) {
      console.error(e);
      errorMessage = defaultErrorMessage;
    }

    isSaving = false;
  }

  async function validateDashboardEntry() {
    if (activeStorm?.generationType === 'manual') {
      try {
        await getLatestEtrSessionId(activeStorm.id);
        showDashboardLink = true;
      } catch (e: any) {
        showDashboardLink = false;
        console.error(e);
        if (e?.response?.status === ErrorStatusCodes.NOT_FOUND) {
          console.error('Manual active storm ETRs are not available yet');
        }
      }
    }
  }

  async function fetchStormOverviewData() {
    const startTime = new Date(Date.now() - HOURS_OFFSET * MS_IN_HOUR).toISOString();
    const endTime = new Date().toISOString();
    const data = await getStormAggregateOverview(startTime, endTime, GRANULARITY);

    if (data.regionalMetrics) {
      systemOverviewData = groupByTimestamp(data.regionalMetrics);
      const lastTimestamp = Object.keys(systemOverviewData)?.at(-1);
      if (lastTimestamp) {
        const mostRecentOutageRecord: AggregateMetricRecordWithRegion[] =
          systemOverviewData[lastTimestamp];
        systemOutageRecord =
          mostRecentOutageRecord.find(({ region }) => region === 'System') || null;
        outageRecords = mostRecentOutageRecord.filter(({ region }) => region !== 'System');
      }
    }
  }

  async function fetchResourceHistory() {
    const startDate = new Date(Date.now() - HOURS_OFFSET * MS_IN_HOUR).toISOString();
    const endDate = new Date().toISOString();

    try {
      const data = await getResourceVisualizationHistory(startDate, endDate);
      systemStateRecords = groupSystemStatesByTimestamp(
        formatWorkerRecords(data.historicalSystemStates)
      );
    } catch (e) {
      console.error(e);
    }
  }
</script>

<svelte:head>
  <title>Active Summary</title>
  <meta name="Storm Active Summary" content={store.etrLabel} />
</svelte:head>

{#if validateRouteEntry()}
  <div class="etr flex flex-col gap-sm h-full">
    {#if errorMessage}
      <Alert.Root variant="error">
        <Alert.Title>Error:</Alert.Title>
        <Alert.Description>{errorMessage}</Alert.Description>
      </Alert.Root>
    {/if}

    {#if isLoading}
      <Skeleton class="w-[300px] h-5 mt-sm" />
    {:else}
      <div>
        {#if lastUpdatedDate}
          <Timestamp {lastUpdatedDate} {isDataOutdated} />
        {:else}
          <Skeleton class="w-[200px] mt-sm" />
        {/if}
      </div>
      {#if !isStormActive}
        <div class="flex gap-md flex-wrap">
          <h2 class="p-xs">Current {store.capitalizedOutagesLabel}</h2>
          <Button disabled={isSaving} onclick={() => postStormEvent('startStorm')}>
            Create Event
          </Button>
        </div>
      {/if}
    {/if}

    <div class="flex gap-sm flex-col xl:flex-row etr h-full">
      <div class="flex flex-col gap-sm w-full xl:min-w-[400px] xl:max-w-[400px]">
        {#if isLoading}
          <Card loading class="h-[205px]" />
        {:else if activeStorm}
          <div>
            <div
              class="flex gap-sm items-center text-white {isStormActive
                ? activeStorm?.generationType
                : STORM_MODES[activeStorm.stormMode].color} rounded-t-md mb-[-1px] p-md"
            >
              <span class="material-icons">
                {STORM_MODES[activeStorm.stormMode].icon}
              </span>
              <h2 class="text-left text-lg">
                {STORM_MODES[activeStorm.stormMode].label}
              </h2>
            </div>
            <Card class="flex flex-col w-full xl:w-[400px] gap-md !rounded-t-none">
              <div class="flex flex-col gap-sm">
                <div class="flex flex-wrap justify-between gap-x-md">
                  <span class="text-accentText w-[125px]">Storm Start:</span>
                  <span class="text-right">
                    {formatDateTime(activeStorm.stormStartDate, true)}
                  </span>
                </div>

                {#if showEta}
                  <div class="flex flex-wrap justify-between gap-x-md">
                    <span class="text-accentText w-[125px]"> Projected ETA: </span>
                    {#if activeStorm.projectedETA}
                      <span class="text-right">
                        {formatDateTime(activeStorm.projectedETA, true)}
                      </span>
                    {:else}
                      <span class="text-right">-</span>
                    {/if}
                  </div>
                {/if}

                <div class="flex flex-wrap justify-between gap-x-md">
                  <span class="text-accentText w-[125px]">
                    Projected {store.etrLabel}:
                  </span>
                  {#if activeStorm.projectedETR}
                    <span class="text-right">
                      {formatDateTime(activeStorm.projectedETR, true)}
                    </span>
                  {:else}
                    <span class="text-right">-</span>
                  {/if}
                </div>
              </div>
              <div class="flex justify-between items-center">
                {#if isStormActive}
                  <Button disabled={isSaving} onclick={() => postStormEvent('endStorm')}>
                    End Event
                  </Button>
                {/if}

                {#if showDashboardLink}
                  <Button
                    href={`${base}/etr/dashboard`}
                    variant="link"
                    class="!no-underline ml-auto"
                  >
                    <div class="flex items-center ml-auto gap-1">
                      <span class="material-icons !text-sm">open_in_new</span>
                      <span class="hover:underline"
                        >{showEta ? 'Restoration' : store.etrLabel} Summary</span
                      >
                    </div>
                  </Button>
                {:else}
                  <Tooltip>
                    {#snippet content()}
                      {`${store.etrLabel} Unavailable`}
                    {/snippet}
                    {#snippet trigger()}
                      <span class="material-icons warning">warning</span>
                    {/snippet}
                  </Tooltip>
                {/if}
              </div>
            </Card>
          </div>
        {/if}

        <div class="normal-view flex flex-row gap-sm w-full xl:flex-col chart-wrapper">
          <Card
            class="w-[calc(50%-0.25rem)] xl:w-full card !pb-0 flex flex-col"
            loading={isLoading}
          >
            <p class="text-center overflow-hidden whitespace-nowrap text-ellipsis">
              Customers {store.customersAffectedLabel}
            </p>
            <AgChart
              data={outageRecords}
              options={{
                height: 150,
                legend: { enabled: false },
                series: [
                  {
                    sectorSpacing: 2,
                    angleKey:
                      source === OutageSource.refined
                        ? Visibility.customers
                        : Visibility.rawCustomers,
                    calloutLabelKey: 'region',
                    innerRadiusRatio: 0.8,
                    tooltip: { renderer: pieChartTooltipRenderer },
                    type: 'donut',
                    calloutLabel: {
                      enabled: false
                    },
                    innerLabels: [
                      {
                        text: formatNumbers(
                          systemOutageRecord?.[
                            source === OutageSource.refined
                              ? Visibility.customers
                              : Visibility.rawCustomers
                          ]
                        ),
                        fontWeight: 'bold',
                        fontSize: 18
                      }
                    ]
                  }
                ]
              }}
              onMouseleave={() => (store.activeMapRegion = null)}
            />
            {#if showUnconfirmedOutages}
              <span class="mb-sm block text-accentText text-right">
                {formatNumbers(activeSystemOutageRecord?.potentialMomentaryIncidents ?? 0)}
                Unconfirmed
              </span>
            {/if}
          </Card>
          <Card
            class="w-[calc(50%-0.25rem)] xl:w-full card !pb-0 flex flex-col"
            loading={isLoading}
          >
            <p class="text-center overflow-hidden whitespace-nowrap text-ellipsis">
              Active {store.capitalizedOutagesLabel}
            </p>
            <AgChart
              data={outageRecords}
              options={{
                height: 150,
                legend: { enabled: false },
                series: [
                  {
                    sectorSpacing: 2,
                    angleKey:
                      source === OutageSource.refined ? Visibility.outages : Visibility.rawOutages,
                    calloutLabelKey: 'region',
                    innerRadiusRatio: 0.8,
                    tooltip: { renderer: pieChartTooltipRenderer },
                    type: 'donut',
                    calloutLabel: {
                      enabled: false
                    },
                    innerLabels: [
                      {
                        text: formatNumbers(
                          systemOutageRecord?.[
                            source === OutageSource.refined
                              ? Visibility.outages
                              : Visibility.rawOutages
                          ]
                        ),
                        fontWeight: 'bold',
                        fontSize: 18
                      }
                    ]
                  }
                ]
              }}
              onMouseleave={() => (store.activeMapRegion = null)}
            />
            {#if showUnconfirmedOutages}
              <span class="mb-sm block text-accentText text-right">
                {formatNumbers(activeSystemOutageRecord?.potentialMomentaryIncidents ?? 0)}
                Unconfirmed
              </span>
            {/if}
          </Card>
        </div>
      </div>

      <!-- Mobile View -->
      <Card class="card flex mobile-view !pb-0" loading={isLoading}>
        <div class="w-6/12 flex flex-col">
          <p class="text-center text-sm h-12">
            Customers {store.customersAffectedLabel}
          </p>
          <AgChart
            data={outageRecords}
            options={{
              height: 150,
              legend: { enabled: false },
              series: [
                {
                  sectorSpacing: 2,
                  angleKey:
                    source === OutageSource.refined
                      ? Visibility.customers
                      : Visibility.rawCustomers,
                  calloutLabelKey: 'region',
                  innerRadiusRatio: 0.8,
                  tooltip: { renderer: pieChartTooltipRenderer },
                  type: 'donut',
                  calloutLabel: {
                    enabled: false
                  },
                  innerLabels: [
                    {
                      text: formatNumbers(
                        systemOutageRecord?.[
                          OutageSource.refined ? Visibility.customers : Visibility.rawCustomers
                        ]
                      ),
                      fontWeight: 'bold',
                      fontSize: 16
                    }
                  ]
                }
              ]
            }}
            onMouseleave={() => (store.activeMapRegion = null)}
          />
          {#if showUnconfirmedOutages}
            <span class="mb-sm block text-accentText text-right">
              {formatNumbers(activeSystemOutageRecord?.potentialMomentaryIncidents ?? 0)}
              Unconfirmed
            </span>
          {/if}
        </div>

        <div class="w-6/12 flex flex-col">
          <p class="text-center text-sm h-12">
            Active {store.capitalizedOutagesLabel}
          </p>
          <AgChart
            data={outageRecords}
            options={{
              height: 150,
              legend: { enabled: false },
              series: [
                {
                  sectorSpacing: 2,
                  angleKey:
                    source === OutageSource.refined ? Visibility.outages : Visibility.rawOutages,
                  calloutLabelKey: 'region',
                  innerRadiusRatio: 0.8,
                  tooltip: { renderer: pieChartTooltipRenderer },
                  type: 'donut',
                  calloutLabel: {
                    enabled: false
                  },
                  innerLabels: [
                    {
                      text: formatNumbers(
                        systemOutageRecord?.[
                          source === OutageSource.refined
                            ? Visibility.outages
                            : Visibility.rawOutages
                        ]
                      ),
                      fontWeight: 'bold',
                      fontSize: 16
                    }
                  ]
                }
              ]
            }}
            onMouseleave={() => (store.activeMapRegion = null)}
          />
          {#if showUnconfirmedOutages}
            <span class="mb-sm block text-accentText text-right">
              {formatNumbers(activeSystemOutageRecord?.potentialMomentaryIncidents ?? 0)}
              Unconfirmed
            </span>
          {/if}
        </div>
      </Card>

      <EtrMap
        class="map-card w-full min-h-[450px] xl:min-h-full"
        config={store.componentsConfig?.[Components.ETR_MAP]?.items}
        loading={isLoading}
        records={outageRecords}
        showPlayback
        showWindGusts={false}
        systemOverviewData={formattedSystemOverviewData}
        showWindAnimation
        outagesSource={store.componentsConfig?.[Components.ETR_MAP_OUTAGES_SOURCE]?.items}
        bind:source
        showResources
      />
    </div>
  </div>
{/if}

<style>
  .system {
    background-color: var(--color-danger);
  }

  .manual {
    background-color: var(--color-secondary);
  }

  :global(.etr .mobile-view) {
    display: none;
  }

  :global(.etr .material-icons.warning) {
    color: var(--color-warning);
  }

  @media (max-width: 767px) {
    :global(.etr .normal-view) {
      display: none;
    }

    :global(.etr .mobile-view) {
      display: flex;
    }
  }

  @media (max-width: 1280px) {
    :global(.etr .map-card) {
      max-height: 450px;
    }
  }

  @media (max-width: 400px) {
    :global(.etr .chart-wrapper) {
      flex-direction: column;
      gap: 1rem;
    }

    :global(.etr .card) {
      width: 100% !important;
    }
  }
</style>
