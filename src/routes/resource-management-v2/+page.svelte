<script lang="ts">
  import { onDestroy, onMount } from 'svelte';
  import type { GridApi, CellValueChangedEvent } from 'ag-grid-enterprise';
  import type { EventSourcePolyfill } from 'event-source-polyfill';
  import { DateTime } from 'luxon';
  import { goto } from '$app/navigation';
  import { base } from '$app/paths';
  import {
    getActiveStorm,
    getDefaultResourcesV2,
    getLatestEtrSessionId,
    getCurrentResourcesV2,
    getFutureResourcesV2,
    getSessionEtrs,
    getStormAggregateOverview,
    postDefaultResourcesV2,
    updateStormModel,
    getResourceVisualization,
    getToggledEvents,
    postEtrEvent,
    getActiveCrews,
    getResourceVisualizationHistory
  } from '$lib/api';
  import { EditableField, ResourcePlanningV2, Table } from '$lib/components';
  import { Card } from '$lib/components/ui/card';
  import * as Alert from '$lib/components/ui/alert';
  import * as Tabs from '$lib/components/ui/tabs';
  import {
    formatWorkerRecords,
    groupByTimestamp,
    groupSystemStatesByTimestamp,
    mergeResourceTotals,
    MS_IN_HOUR
  } from '$lib/config/etr';
  import { ErrorStatusCodes } from '$lib/config/main';
  import { getDefaultResourcesColumns } from '$lib/config/table';
  import store from '$lib/stores/app.svelte';
  import { Components } from '$lib/types';
  import type {
    RegionalResourceState,
    ResourceData,
    FutureResourcesData,
    StormData,
    ResourceVisualization,
    AggregateMetricResponse,
    ActiveCrew
  } from '$lib/types';
  import { FeatureFlag } from '$lib/types';
  import { formatAsOfTimestamp } from '$lib/config/utils';
  import { capitalize, isDataTimestampOutdated, toCapitalizedWords } from '$lib/utils';
  import { listenToResourceOperationUpdates } from '$lib/api/sse';
  import Timestamp from '$src/lib/components/Timestamp/Timestamp.svelte';

  const HOURS_OFFSET = 2.5;
  const GRANULARITY = 10;
  const NEXT_PENDING_TIMESTAMP = 60000; // 1 minute;
  const resourceFields: string[] =
    store.componentsConfig?.[Components.RESOURCE_VALIDATION]?.validations || [];

  let eventSource: EventSourcePolyfill | null = null;

  // General
  let activeStorm = $state<StormData | undefined>();
  let errorMessage = $state<string>('');
  let isLoadingStorm = $state<boolean>(true);
  let territoryMap = $state<Map<string, string>>(new Map());

  // Resource Defaults
  let defaultResourcesRowData = $state<ResourceData[]>([]);
  let defaultResourcesV2 = $state<RegionalResourceState>({
    regionalResourceStates: []
  });
  let isLoadingDefaultResources = $state<boolean>(true);

  // Resource Planning
  let currentResourcesRowData = $state<ResourceData[]>([]);
  let futureResourcesRowData = $state<FutureResourcesData[]>([]);
  let isDataOutdated = $state<boolean>(false);
  let isLoadingCurrentResources = $state<boolean>(false);
  let isLoadingFutureResources = $state<boolean>(false);
  let lastUpdatedDate = $state<string>('');
  let sessionId: string = '';
  let showWarningMessage = $state<boolean>(false);

  let allowResourceDefaults = $state<boolean>(true);
  let crews = $state<ActiveCrew[]>([]);
  let isLoadingAggregateResources = $state<boolean>(true);
  let isLoadingCrews = $state<boolean>(false);
  let isLoadingResourceVisualization = $state<boolean>(true);
  let isLoadingToggledResources = $state<boolean>(false);
  let resourceChartData = $state<{ date: Date; resources?: number; pending?: number }[]>([]);
  let resourceVisualizations = $state<ResourceVisualization | null>(null);
  let stormAggregateOverview = $state<AggregateMetricResponse | null>(null);

  let systemStateRecords = $state<
    Record<string, { timestamp: string; totalWorkers: number; region: string; date: Date }[]>
  >({});

  let systemOverviewData = $derived(
    groupByTimestamp(stormAggregateOverview?.regionalMetrics ?? {})
  );
  let formattedSystemOverviewData = $derived(
    mergeResourceTotals(systemStateRecords, systemOverviewData)
  );
  let showResourcePlanning = $derived(
    ['active', 'forecast'].includes(activeStorm?.stormMode ?? '')
  );
  let territories = $derived(
    currentResourcesRowData.map((d) => ({
      territoryId: d.territoryId?.toString() || '',
      territoryName: d.territoryName
    }))
  );

  $effect(() => {
    if (resourceVisualizations) formatResourceData(resourceVisualizations);
  });

  // Generate the map from defaults
  $effect(() => {
    if (defaultResourcesV2?.regionalResourceStates) {
      territoryMap = new Map(
        defaultResourcesV2.regionalResourceStates.map(({ territoryId, territoryName }) => [
          territoryId,
          territoryName
        ])
      );
    }
  });

  onMount(async () => {
    if (validateRouteEntry()) {
      refreshPage();
    } else {
      goto(`${base}/`);
    }
  });

  // Cleanup on unmount
  onDestroy(() => {
    if (eventSource) {
      eventSource.close();
    }
  });

  // Example function to retrieve the territory name
  function getTerritoryNameById(id: string): string | undefined {
    return territoryMap.get(id);
  }

  function validateRouteEntry() {
    return store.featureFlags?.[FeatureFlag.etr];
  }

  async function fetchActiveStorm() {
    try {
      const data = await getActiveStorm();
      if (data) activeStorm = data;
    } catch {
      errorMessage = 'Could not load active storm data';
    } finally {
      isLoadingStorm = false;
    }
  }

  async function fetchDefaultResourcesV2() {
    try {
      const resources = await getDefaultResourcesV2();
      setDefaultResourcesV2(resources);
    } catch (err) {
      errorMessage = 'Could not load default resources';
    }

    isLoadingDefaultResources = false;
  }

  function setDefaultResourcesV2(rs: RegionalResourceState) {
    defaultResourcesV2 = rs;
    defaultResourcesRowData = rs.regionalResourceStates
      .sort((a, b) => a.territoryName.localeCompare(b.territoryName))
      .map((r, i) => {
        const total = Object.keys(r.resources).reduce((acc, key) => acc + r.resources[key], 0);
        return {
          ...r.resources,
          id: i + 1,
          territoryId: r.territoryId,
          territoryName: r.territoryName,
          total
        };
      });
  }

  async function updateDefaultResourcesV2(event: CellValueChangedEvent<Record<string, any>>) {
    if (!event.colDef.field || !resourceFields.includes(event.colDef.field)) return;

    const value = Number(event.value);
    if (isNaN(value) || value < 0) {
      errorMessage = 'Enter non-negative numeric values.';
      return;
    }

    errorMessage = '';

    const body: RegionalResourceState = {
      regionalResourceStates: []
    };

    body.regionalResourceStates = defaultResourcesRowData.map((d) => {
      const resources: Record<string, number> = {};
      resourceFields.forEach((field) => (resources[field] = Number(d[field])));
      return {
        territoryId: d.territoryId?.toString() || '',
        territoryName: d.territoryName,
        resources
      };
    });

    try {
      await postDefaultResourcesV2(body);
      const region = defaultResourcesV2.regionalResourceStates.find(
        (r) => r.territoryId === event.data.territoryId
      );
      if (region) region.resources = getRowResources(event.data);
      setDefaultResourcesV2(defaultResourcesV2);
    } catch (e) {
      errorMessage = 'Could not update default resources';
    }
  }

  function getRowResources(d: CellValueChangedEvent<Record<string, any>>['data']) {
    const rowResources: Record<string, any> = {};
    resourceFields.forEach((field) => (rowResources[field] = d[field]));
    return rowResources;
  }

  function pinResourcesSummaryRow(api: GridApi) {
    if (defaultResourcesRowData.length <= 1) return;
    const cols = [...resourceFields, 'futureTotal', 'total'];
    const result: Record<string, any>[] = [{ territoryName: 'Summary' }];
    cols.forEach((field) => (result[0][field] = 0));
    cols.forEach((field) => {
      api.forEachNode((n) => (result[0][field] += n.data[field]));
    });
    api.setGridOption('pinnedBottomRowData', result);
  }

  async function updateStormName(id: string, name: string = '') {
    try {
      await updateStormModel(id, 'displayName', name);
    } catch (e) {
      errorMessage = 'Could not update storm name';
    }
  }

  async function fetchLatestEtrSessionId() {
    try {
      const id = await getLatestEtrSessionId(activeStorm!.id);
      sessionId = id;
    } catch (e: any) {
      const isManualStorm = activeStorm!.generationType === 'manual';
      const is404 = e?.response?.status === ErrorStatusCodes.NOT_FOUND;

      if (isManualStorm && is404) {
        showWarningMessage = true;
      } else {
        errorMessage = `Could not load ${store.etrLabel} session`;
      }
    }
  }

  async function setupResourcePlanning() {
    await Promise.all([fetchLatestEtrSessionId(), fetchFutureResources(), fetchActiveCrews()]);
    if (sessionId) fetchSessionEtrs(sessionId);
    fetchCurrentResources(); // Resources Enroute needs future resources data
  }

  async function fetchSessionEtrs(id: string) {
    try {
      const response = await getSessionEtrs(id);

      if (Object.keys(response).length) {
        lastUpdatedDate = formatAsOfTimestamp(response.generationTime);
        isDataOutdated = isDataTimestampOutdated(response.generationTime);
      }
    } catch (e: any) {
      const isManualStorm = activeStorm!.generationType === 'manual';
      const is404 = e?.response?.status === ErrorStatusCodes.NOT_FOUND;

      if (isManualStorm && is404) {
        showWarningMessage = true;
      } else {
        errorMessage = 'Could not load session ETRs';
      }
    }
  }

  async function fetchFutureResources() {
    if (!activeStorm) return;
    isLoadingFutureResources = true;

    try {
      const res = await getFutureResourcesV2(activeStorm.id);
      futureResourcesRowData = res.map((r) => {
        const total = Object.keys(r.resources).reduce((acc, key) => acc + r.resources[key], 0);
        return {
          ...r,
          total,
          resourceType: toCapitalizedWords(Object.keys(r.resources)[0]),
          toTerritoryId: getTerritoryNameById(r.toTerritoryId) ?? 'Unknown',
          fromTerritoryId: getTerritoryNameById(r.fromTerritoryId) ?? r.fromTerritoryId,
          status: DateTime.now().toISO() < r.executionTime ? 'Enroute' : 'Completed'
        };
      });
    } catch (e) {
      errorMessage = 'Could not load future resources';
    }

    isLoadingFutureResources = false;
  }

  async function fetchCurrentResources() {
    if (!activeStorm) return;
    isLoadingCurrentResources = true;

    try {
      const res = await getCurrentResourcesV2(activeStorm.id);
      currentResourcesRowData = res.regionalResourceStates
        .sort((a, b) => a.territoryName.localeCompare(b.territoryName))
        .map((r, i) => {
          const total = Object.keys(r.resources).reduce((acc, key) => acc + r.resources[key], 0);
          return {
            ...r.resources,
            id: i + 1,
            futureTotal: calcFutureTerritoryCount(r.territoryName),
            territoryId: r.territoryId,
            territoryName: r.territoryName,
            total
          };
        });
    } catch {
      errorMessage = 'Could not load current resources';
    } finally {
      isLoadingCurrentResources = false;
    }
  }

  function calcFutureTerritoryCount(id: string) {
    if (futureResourcesRowData.length) {
      return futureResourcesRowData
        .filter((d) => d.toTerritoryId === id)
        .reduce((acc, d) => acc + d.total, 0);
    } else {
      return 0;
    }
  }

  async function refetchResources() {
    await fetchFutureResources();
    await Promise.allSettled([
      fetchCurrentResources(),
      fetchActiveCrews(),
      fetchResourceVisualization(),
      fetchToggledEvents(),
      fetchResourceHistory()
    ]);
  }

  async function fetchResourceData() {
    if (!activeStorm) return;

    try {
      isLoadingAggregateResources = true;
      const endTime = new Date().toISOString();
      const startTime = DateTime.fromISO(endTime).minus({ hours: 3 }).toISO()!;

      stormAggregateOverview = await getStormAggregateOverview(startTime, endTime, GRANULARITY);
    } catch (error) {
      console.error('Error fetching resource data:', error);
    } finally {
      isLoadingAggregateResources = false;
    }
  }

  async function fetchResourceVisualization() {
    if (!activeStorm?.id) return;
    isLoadingResourceVisualization = true;
    try {
      resourceVisualizations = await getResourceVisualization(activeStorm.id);
    } catch (error) {
      console.error('Error fetching resource visualization:', error);
    } finally {
      isLoadingResourceVisualization = false;
    }
  }

  function formatResourceData(resourceVisualization: ResourceVisualization) {
    const now = new Date();

    const sortedResources = [...resourceVisualization.resourceCounts].sort(
      (a, b) =>
        new Date(a.resourceActivationTime).getTime() - new Date(b.resourceActivationTime).getTime()
    );

    const pastResources = sortedResources.filter(
      (record) => new Date(record.resourceActivationTime) < now
    );
    const pendingResources = sortedResources.filter(
      (record) => new Date(record.resourceActivationTime) >= now
    );

    const lastDispatchedResource = pastResources.at(-1);

    const lastFutureResource = sortedResources.findLast(
      (record) => new Date(record.resourceActivationTime) < activeStorm!.stormEndDate
    );

    const chartData = [
      ...pastResources.map((record) => ({
        date: new Date(record.resourceActivationTime),
        resources: record.regionalResourceCount.System
      })),
      ...(lastDispatchedResource
        ? [
            {
              date: now,
              resources: lastDispatchedResource.regionalResourceCount.System
            },
            {
              date: new Date(now.getTime() + NEXT_PENDING_TIMESTAMP),
              pending: lastDispatchedResource.regionalResourceCount.System
            }
          ]
        : []),
      ...pendingResources.map((record) => ({
        date: new Date(record.resourceActivationTime),
        pending: record.regionalResourceCount.System
      })),
      ...(activeStorm?.stormEndDate && lastFutureResource
        ? [
            {
              date: activeStorm.stormEndDate,
              pending: lastFutureResource.regionalResourceCount.System
            }
          ]
        : [])
    ];
    resourceChartData = chartData.sort((a, b) => a.date.getTime() - b.date.getTime());
  }

  async function onToggleResourceDefaults(toggled: boolean) {
    if (!activeStorm) return;
    allowResourceDefaults = toggled;
    isLoadingToggledResources = true;
    try {
      await postEtrEvent({
        aggregateId: activeStorm.id,
        event: 'resourceDefaultsToggled',
        eventDate: activeStorm.stormStartDate.toISOString(),
        data: {
          status: allowResourceDefaults ? 'ON' : 'OFF'
        }
      });

      await Promise.all([refetchResources(), fetchDefaultResourcesV2()]);
    } catch (e) {
      errorMessage = 'Could not toggle resource simulation';
    } finally {
      isLoadingToggledResources = false;
    }
  }

  async function fetchToggledEvents() {
    if (!activeStorm) return;
    try {
      const d = await getToggledEvents(activeStorm.id);
      if (d.length) {
        allowResourceDefaults = d[0].data?.status === 'ON';
      }
    } catch (e) {
      allowResourceDefaults = false;
    }
  }

  async function fetchActiveCrews() {
    if (!activeStorm?.id) return;

    try {
      isLoadingCrews = true;
      const d = await getActiveCrews(activeStorm.id);
      crews = d.map((crew) => ({
        notes: 'No notes',
        ...crew,
        toTerritoryId: getTerritoryNameById(crew.assignedTerritoryId) ?? 'Unknown',
        resourceType: toCapitalizedWords(crew?.resources?.type),
        status: capitalize(crew.status),
        ...crew.resources.roleAllocations.reduce<Record<string, number>>((acc, role) => {
          acc[role.role] = role.quantity;
          return acc;
        }, {})
      }));
    } catch (e) {
      console.error('Error fetching active crews', e);
    } finally {
      isLoadingCrews = false;
    }
  }

  async function fetchResourceHistory() {
    const startDate = new Date(Date.now() - HOURS_OFFSET * MS_IN_HOUR).toISOString();
    const endDate = new Date().toISOString();

    try {
      const data = await getResourceVisualizationHistory(startDate, endDate);
      systemStateRecords = groupSystemStatesByTimestamp(
        formatWorkerRecords(data.historicalSystemStates)
      );
    } catch (e) {
      console.error(e);
    }
  }

  const refreshPage = async () => {
    await fetchDefaultResourcesV2();
    await fetchActiveStorm();

    if (activeStorm) {
      eventSource = await listenToResourceOperationUpdates(refetchResources, territoryMap);
      await fetchToggledEvents();
    }

    if (showResourcePlanning) {
      setupResourcePlanning();
    }

    Promise.allSettled([fetchResourceData(), fetchResourceVisualization(), fetchResourceHistory()]);
  };
</script>

<svelte:head>
  <title>Resource Management</title>
  <meta name="Storm Resource Management" content="Resource Management" />
</svelte:head>

{#if validateRouteEntry()}
  <div class="resource-management flex flex-col gap-sm md:h-full">
    {#if errorMessage}
      <Alert.Root variant="error">
        <Alert.Title>Error:</Alert.Title>
        <Alert.Description>{errorMessage}</Alert.Description>
      </Alert.Root>
    {/if}

    {#if showWarningMessage}
      <Alert.Root variant="warning">
        <Alert.Title>{store.etrLabel} Unavailable:</Alert.Title>
        <Alert.Description>This manual storm's ETRs are not yet available.</Alert.Description>
      </Alert.Root>
    {/if}

    <Tabs.Root value="0">
      <Tabs.List>
        <Tabs.Trigger value="0">Resource Defaults</Tabs.Trigger>
        {#if showResourcePlanning}
          <Tabs.Trigger value="1">Resource Planning</Tabs.Trigger>
        {/if}
      </Tabs.List>

      <Tabs.Content value="0">
        <Card>
          <Table
            columnDefs={getDefaultResourcesColumns()}
            csvTitle="DefaultResources"
            domLayout="autoHeight"
            isLoading={isLoadingDefaultResources || isLoadingStorm}
            rowData={defaultResourcesRowData}
            onCellValueChanged={updateDefaultResourcesV2}
            onSetGridData={(gridApi) => pinResourcesSummaryRow(gridApi)}
          />
        </Card>
      </Tabs.Content>
      {#if showResourcePlanning}
        <Tabs.Content value="1">
          <EditableField
            class="text-xl"
            value={activeStorm!.displayName}
            onChange={(value) => updateStormName(activeStorm!.id, value)}
          />

          {#if lastUpdatedDate}
            <Timestamp {isDataOutdated} {lastUpdatedDate} />
          {:else}
            <div class="text-accentText mb-md">ETRs not available</div>
          {/if}
          <ResourcePlanningV2
            activeStorm={activeStorm!}
            {refreshPage}
            {allowResourceDefaults}
            {crews}
            {currentResourcesRowData}
            {futureResourcesRowData}
            {isLoadingAggregateResources}
            {isLoadingCrews}
            {isLoadingCurrentResources}
            {isLoadingFutureResources}
            {isLoadingResourceVisualization}
            {isLoadingToggledResources}
            isReadOnly={true}
            {resourceChartData}
            stormId={activeStorm!.id}
            systemOverviewData={formattedSystemOverviewData}
            {territories}
            onResourcesMoved={refetchResources}
            {onToggleResourceDefaults}
            {pinResourcesSummaryRow}
          />
        </Tabs.Content>
      {/if}
    </Tabs.Root>
  </div>
{/if}
