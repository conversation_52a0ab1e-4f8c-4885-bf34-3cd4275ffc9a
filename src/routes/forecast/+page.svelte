<script lang="ts">
  import { onMount } from 'svelte';
  import { DateTime } from 'luxon';
  import { type MapMouseEvent } from 'mapbox-gl';
  import { goto } from '$app/navigation';
  import { base } from '$app/paths';
  import {
    getDailyEvents,
    getDailyHourlyPredictionSummary,
    getDailyPredictionMap,
    getMapConfigs,
    getPSSessionId,
    getRegionalWeatherData,
    getWSSessionId,
    getSessionPredictions,
    getForecastAnalogs,
    getWeatherForecasts
  } from '$lib/api';
  import {
    AgChart,
    ButtonGroup,
    EmptyForecastCard,
    ForecastCard,
    ForecastCardMobile,
    ForecastCardLoading,
    Loader,
    DailyMap,
    TogglingInfoModal,
    EventsChart,
    EventsChartMobile,
    EmptyPointForecastCard,
    PointForecastCard,
    PointForecastCardMobile,
    PointForecastCardLoading,
    MobileView,
    DesktopView,
    Timestamp
  } from '$lib/components';
  import { Card } from '$lib/components/ui/card';
  import { Button } from '$lib/components/ui/button';
  import * as Select from '$lib/components/ui/select';
  import { Skeleton } from '$lib/components/ui/skeleton';
  import * as Tabs from '$lib/components/ui/tabs';
  import { isDarkMode } from '$lib/stores/theme';
  import store from '$lib/stores/app.svelte';
  import {
    isSystemic,
    showDailyMapWindAnimation,
    showForecastAnalogs,
    showForecastingEvents,
    showPointForecast,
    showProbabilisticForecast,
    timezoneAbbr
  } from '$lib/stores/constants';
  import { Components, FeatureFlag, PageModes } from '$lib/types';
  import type {
    DailyEvent,
    DailyPredictionMap,
    MapConfig,
    RegionalWeather,
    DailyHourlyValue,
    SessionPrediction,
    SpatialData,
    DailyPredictedSessionData,
    ViewLevel,
    Region,
    StormAnalog,
    WeatherStation,
    LineChartTooltipParams,
    CustomTooltipRendererParams
  } from '$lib/types';
  import {
    CURRENT_TIME_CROSS_LINE_SERIES_ITEM,
    customFormatter,
    forecastTooltipRenderer,
    tooltipRenderer,
    mobileViewTooltipRenderer,
    OUTAGES_STROKE_COLOR,
    ACTUALS_STROKE_COLOR,
    CHART_LINE_DASH_LENGTH,
    CHART_LINE_DASH_BREAK,
    LINECHART_X_AXIS_FORMAT_DAILY
  } from '$lib/config/chart';
  import { convertArrayToObject, getSortedKeysSubset, isDataTimestampOutdated } from '$lib/utils';
  import {
    findMinEventLevelThreshold,
    formatAsOfTimestamp,
    formatDate,
    getThreatLevelCrossLines
  } from '$lib/config/utils';
  import { formatRegionId } from '$lib/config/map';
  import type { AgAxisLabelFormatterParams } from 'ag-charts-enterprise';

  const SYSTEM_REGION: Region = {
    name: 'system',
    displayName: 'System',
    centroid: store.appConfig?.center?.value ?? [0, 0],
    type: 'main-territory'
  };
  const DAYS_TO_SHOW = 5;
  const rangeAreaSeriesItems = [
    {
      interpolation: { type: 'smooth' },
      tooltip: { renderer: forecastTooltipRenderer },
      type: 'range-area',
      xKey: 'hour',
      yLowKey: 'min',
      yHighKey: 'max'
    }
  ];

  let isChartLoading = $state<boolean>(true);
  let isForecastLoading = $state<boolean>(true);
  let isEventsLoading = $state<boolean>(true);
  let isLoadingSpatialData = $state<boolean>(true);
  let isLoadingForecastCards = $state<boolean>(true);
  let isLoadingHourlyForecastData = $state<boolean>(true);
  let isLoadingWeatherForecasts = $state<boolean>(true);
  let isLoadingAnalogs = $state<boolean>(false);

  let asOfTimestamp = $state<string>('');
  let lastUpdatedDate = $state<string>('');
  let selectedDay = $state<string>(formatDate(new Date()));
  let sessionPredictions = $state<SessionPrediction[]>([]);

  let activePageMode = $state<PageModes>(PageModes.weather);

  let mapConfigs = $state<MapConfig[]>([]);
  let weatherData = $state<RegionalWeather[]>([]);
  let weatherDataCache: Record<string, RegionalWeather[]> = {};
  let systemWeatherData = $state<RegionalWeather[]>([]);
  let systemWeatherDataCache: Record<string, RegionalWeather[]> = {};
  let dailyEvents = $state<DailyEvent[]>([]);
  let dailySystemEvents = $state<DailyEvent[]>([]);
  let dailySession: DailyPredictedSessionData;
  let dailyHourlyPredictions = $state<DailyHourlyValue[]>([]);

  let chartData = $state<Record<string, any>[]>([]);
  let dailyPredictionMap: DailyPredictionMap = {};
  let forecastAnalogs = $state<StormAnalog[]>([]);
  let weatherStations = $state<WeatherStation[][]>([]);

  let showDualView = $state<boolean>(false);
  let midnightOutagesSum = $state<number>(0);
  let midnightCustomersAffectedSum = $state<number>(0);

  let hourlyZoneSessionId: string;
  let dailyZoneSessionId: string;
  let dailyParentZoneSessionId: string;
  let dailySessionId: string;
  let dailyZoneWeatherSessionId: string;
  let dailyWeatherSessionId: string;
  let dailyParentZoneWeatherSessionId: string;

  let selectedRegion = $state<Region>(SYSTEM_REGION);
  let viewLevel = $state<ViewLevel>('system');

  let groupedSystemWeatherData = $state<Record<string, RegionalWeather>>({});
  let groupedSystemDailyEvents = $state<Record<string, DailyEvent>>({});
  let spatialData = $state<SpatialData[]>([]);
  let selectedIndex = $state<number>(0);
  let selectedView = $state<number>(0);
  let selectedDayIndex = $state<number>(0);
  let isPointForecastView = $state<boolean>(showPointForecast && !showProbabilisticForecast);

  // Mobile View
  let chartViewIndex: number = $state<number>(0);
  let isMapView = $state<boolean>(false);

  let filteredDailyEvents = $derived(dailyEvents.filter(({ date }) => date === selectedDay));
  let filteredWeatherData = $derived(weatherData.filter(({ day }) => day === selectedDay));
  let isCurrentDay = $derived(formatDate(new Date()) === selectedDay);
  let isDataOutdated = $derived(isDataTimestampOutdated(lastUpdatedDate, 2));
  let isSystemView = $derived(selectedRegion.name === 'system');
  let nextDayDate = $derived(
    DateTime.fromFormat(selectedDay, 'yyyy-MM-dd').plus({ days: 1 }).toJSDate()
  );
  let selectedDayDate = $derived(DateTime.fromFormat(selectedDay, 'yyyy-MM-dd').toJSDate());
  let threatLevelCrossLines = $derived(
    getThreatLevelCrossLines(
      selectedRegion.name,
      selectedRegion.name === 'system' ? 'system' : 'pd-area',
      $isDarkMode
    )
  );
  let yAxisMax = $derived(
    findMinEventLevelThreshold(
      selectedRegion.name === 'system' ? 'system' : 'pd-area',
      spatialData.length,
      selectedRegion.name
    )
  );

  $effect(() => {
    configureChartData(selectedDay);
  });

  $effect(() => {
    groupedSystemWeatherData = getSortedKeysSubset(
      convertArrayToObject(systemWeatherData, 'day'),
      DAYS_TO_SHOW
    );
  });

  $effect(() => {
    groupedSystemDailyEvents = getSortedKeysSubset(
      convertArrayToObject(dailySystemEvents, 'date'),
      DAYS_TO_SHOW
    );
  });

  $effect(() => {
    spatialData = formatSpatialData(filteredDailyEvents, filteredWeatherData);
  });

  onMount(async () => {
    if (!validateRouteEntry()) {
      return goto(`${base}/`);
    }

    isLoadingSpatialData = true;

    try {
      const [
        psSessionDaily,
        psSessionDailyZone,
        psSessionHourlyZone,
        wsSessionDailyZone,
        wsSessionDaily
      ] = await Promise.all([
        getPSSessionId('daily'),
        getPSSessionId('daily_zone'),
        getPSSessionId('hourly_zone'),
        getWSSessionId('daily_zone'),
        getWSSessionId('daily')
      ]);

      lastUpdatedDate = psSessionDaily.latestDate;
      asOfTimestamp = formatAsOfTimestamp(psSessionDaily.latestDate);
      dailySessionId = psSessionDaily.session;
      dailyZoneSessionId = psSessionDailyZone.session;
      hourlyZoneSessionId = psSessionHourlyZone.session;
      dailyZoneWeatherSessionId = wsSessionDailyZone;
      dailyWeatherSessionId = wsSessionDaily;

      if (!isSystemic) {
        const [psSessionDailyParentZone, wsSessionDailyParentZone] = await Promise.all([
          getPSSessionId('daily_parent_zone'),
          getWSSessionId('daily_parent_zone')
        ]);

        dailyParentZoneSessionId = psSessionDailyParentZone.session;
        dailyParentZoneWeatherSessionId = wsSessionDailyParentZone;
      }

      await Promise.allSettled([
        fetchPredictionData(dailySessionId),
        fetchSpatialData(),
        fetchChartData(),
        fetchDailyEvents(),
        fetchDailyHourlyPredictions(hourlyZoneSessionId),
        fetchMapConfigs(),
        fetchForecastAnalogs(),
        fetchWeatherForecasts()
      ]);
    } catch (err) {
      console.error('Error loading session data.', err);
    } finally {
      isLoadingForecastCards = false;
      isLoadingSpatialData = false;
    }
  });

  function validateRouteEntry() {
    return store.featureFlags?.[FeatureFlag.daily];
  }

  function configureChartData(date: string) {
    if (dailyPredictionMap[date]) {
      chartData = dailyPredictionMap[date].hourlyPredictions.map((p) => ({
        hour: Number(p.hour),
        max: p.max,
        min: p.min
      }));
    }
  }

  function formatSpatialData(events: DailyEvent[], weather: RegionalWeather[]) {
    const eventsObj = events.reduce((acc: Record<string, DailyEvent>, event) => {
      acc[event.territory] = event;
      return acc;
    }, {});

    const weatherObj = weather.reduce((acc: Record<string, RegionalWeather>, event) => {
      acc[event.displayName] = event;
      return acc;
    }, {});

    return Object.entries(weatherObj).map(([key, value]) => {
      const outages = eventsObj[key]?.outages ?? 0;
      return {
        name: value.name,
        displayName: key,
        attributes: {
          ...value.weatherAttributes,
          outages,
          customersAffected: eventsObj[key]?.customersAffected ?? 0,
          threatLevel:
            store.eventLevels.find(
              ({ range, view, name }) =>
                range?.length &&
                Math.round(outages) >= range[0] &&
                Math.round(outages) <= range[1] &&
                name === value.name &&
                view === (selectedRegion.name === 'system' ? 'pd-area' : 'workgroup')
            )?.category ?? ''
        }
      };
    });
  }

  async function fetchPredictionData(session: string, parentZone?: string) {
    try {
      sessionPredictions = await getSessionPredictions(session, parentZone);
    } catch (err) {
      console.error(err);
    } finally {
      isForecastLoading = false;
    }
  }

  async function fetchSpatialData(parentZone?: string) {
    const key = parentZone ?? 'system';

    const id =
      !isSystemic && selectedRegion.name === 'system'
        ? dailyParentZoneWeatherSessionId
        : dailyZoneWeatherSessionId;

    if (!id) return;

    try {
      isLoadingForecastCards = true;
      if (weatherDataCache[key]) {
        weatherData = weatherDataCache[key];
      } else {
        weatherData = await getRegionalWeatherData(
          id,
          selectedRegion.name === 'system' && !isSystemic ? 'daily_parent_zone' : 'daily_zone',
          parentZone
        );
        weatherDataCache[key] = weatherData;
      }

      if (isPointForecastView) {
        if (systemWeatherDataCache[key]) {
          systemWeatherData = systemWeatherDataCache[key];
        } else {
          systemWeatherData = parentZone
            ? await getRegionalWeatherData(dailyZoneWeatherSessionId, 'daily', parentZone)
            : await getRegionalWeatherData(dailyWeatherSessionId, 'daily');
          systemWeatherDataCache[key] = systemWeatherData;
        }
      }
    } catch (err) {
      console.error(err);
    } finally {
      isLoadingForecastCards = false;
    }
  }

  async function fetchMapConfigs() {
    try {
      mapConfigs = await getMapConfigs();
    } catch (err) {
      console.error(err);
    }
  }

  async function fetchChartData(parentZone?: string) {
    isChartLoading = true;
    try {
      dailyPredictionMap = await getDailyPredictionMap(parentZone);
      configureChartData(selectedDay);
    } catch (err) {
      console.error(err);
    } finally {
      isChartLoading = false;
    }
  }

  async function fetchWeatherForecasts() {
    if (!showDailyMapWindAnimation) return;

    try {
      const data = await getWeatherForecasts();
      weatherStations = data.map((i) =>
        Object.entries(i.weatherStations).map(([id, station]) => ({
          ...station,
          id,
          windBearing: station.weatherAttributes.windBearingDeg,
          windSpeed: station.weatherAttributes.windSpeedMph
        }))
      );
    } catch (err) {
      console.error(err);
    } finally {
      isLoadingWeatherForecasts = false;
    }
  }

  async function fetchDailyEvents(parentZone?: string) {
    const id =
      !isSystemic && selectedRegion.name === 'system'
        ? dailyParentZoneSessionId
        : dailyZoneSessionId;

    if (!id) return;

    try {
      // Needs daily_zone session ID for system=false
      const dailyRegionalEvents = await getDailyEvents(id, false, parentZone);
      // Needs daily session ID for system=true when there is no parentZone
      dailySystemEvents = await getDailyEvents(
        parentZone ? dailyZoneSessionId : dailySessionId,
        true,
        parentZone
      );
      dailyEvents = [...(dailyRegionalEvents ?? []), ...(dailySystemEvents ?? [])];
    } catch (err) {
      console.error(err);
    } finally {
      isEventsLoading = false;
    }
  }

  async function fetchDailyHourlyPredictions(sessionId: string, parentZone?: string) {
    isLoadingHourlyForecastData = true;
    try {
      dailySession = await getDailyHourlyPredictionSummary(sessionId, parentZone);

      dailySession.dailyHourlyValues = dailySession.dailyHourlyValues.map((d) => ({
        ...d,
        values: d.values.map((i) => {
          const isPast = new Date(i.targetDate) < new Date();
          return {
            ...i,
            totalOutagesSum: null,
            [isPast ? 'midnightOutages' : 'totalOutagesSum']: i.totalOutagesSum ?? 0,
            [isPast ? 'midnightCustomersAffected' : 'totalCustomersAffectedSum']:
              i.totalCustomersAffectedSum ?? 0,
            date: new Date(i.targetDate),
            targetDate: formatDate(i.targetDate),
            totalOutages: i.totalOutagesSum ?? 0
          };
        })
      }));

      dailyHourlyPredictions = dailySession?.dailyHourlyValues
        .slice(0, DAYS_TO_SHOW)
        .flatMap((d) => d.values);

      midnightOutagesSum = dailyHourlyPredictions.reduce(
        (acc, curr) => acc + (curr.midnightOutages ?? 0),
        0
      );
      midnightCustomersAffectedSum = dailyHourlyPredictions.reduce(
        (acc, curr) => acc + (curr.midnightCustomersAffected ?? 0),
        0
      );
    } catch (err) {
      console.error(err);
    } finally {
      isLoadingHourlyForecastData = false;
    }
  }

  async function onHandleViewChange() {
    if (!selectedRegion) return;

    try {
      isLoadingForecastCards = true;
      const parentZone = selectedRegion.name === 'system' ? '' : selectedRegion.name.toUpperCase();

      await Promise.allSettled([
        fetchPredictionData(dailyZoneSessionId, parentZone),
        fetchDailyEvents(parentZone)
      ]);

      viewLevel = selectedRegion.name === 'system' ? 'system' : 'pd-area';

      await Promise.allSettled([
        fetchSpatialData(parentZone),
        fetchChartData(parentZone),
        fetchDailyHourlyPredictions(hourlyZoneSessionId, parentZone)
      ]);
    } catch (err) {
      console.error('Error changing view:', err);
    } finally {
      isLoadingForecastCards = false;
    }
  }

  function handleOnRegionClick(e: MapMouseEvent) {
    try {
      if (isSystemic || !selectedRegion || !e?.features?.[0]) return;

      const { sourceLayer = '', properties = {} } = e.features[0];

      // pd-area view
      if (sourceLayer === 'larger-regions') {
        selectedRegion = store.territories.find(
          (i) => i.name === formatRegionId(properties?.name as string)
        ) as Region;
      }
      // workgroup view
      if (sourceLayer === 'regions') {
        if (properties?.pdArea === selectedRegion.displayName) {
          selectedRegion = SYSTEM_REGION;
        } else {
          selectedRegion = store.territories.find(
            (i) => i.name === formatRegionId(properties?.pdArea as string)
          ) as Region;
        }
      }

      onHandleViewChange();
    } catch (err) {
      console.error('Error handling region click:', err);
    }
  }

  async function fetchForecastAnalogs() {
    if (!showForecastAnalogs) return;

    try {
      isLoadingAnalogs = true;
      const data = await getForecastAnalogs(selectedRegion.name);
      forecastAnalogs = data.stormAnalogs;
    } catch (err) {
      console.error(err);
      forecastAnalogs = [];
    } finally {
      isLoadingAnalogs = false;
    }
  }

  function getDefaultAnalogCrosslines(analog: StormAnalog) {
    return {
      type: 'range',
      fill: 'red',
      lineDash: [CHART_LINE_DASH_LENGTH, CHART_LINE_DASH_BREAK],
      stroke: 'red',
      label: {
        text: `⚠ ${analog.analogEvents.length} Similar ${
          analog.analogEvents.length === 1 ? 'Event' : 'Events'
        }`,
        color: 'red'
      }
    };
  }
</script>

<svelte:head>
  <title>Forecast</title>
  <meta name="Storm Forecast" content="Daily" />
</svelte:head>

<section class="daily md:h-full flex flex-col gap-sm">
  {#if showPointForecast && showProbabilisticForecast}
    <div class="flex justify-between flex-wrap gap-x-lg gap-y-sm">
      {#if asOfTimestamp}
        <Timestamp {isDataOutdated} lastUpdatedDate={asOfTimestamp} />
      {:else}
        <Skeleton class="w-[200px] h-[26px]" />
      {/if}

      <ButtonGroup
        bind:value={selectedView}
        onClick={() => {
          isPointForecastView = selectedView === 1;
          fetchSpatialData();
        }}
      >
        <Button value={0}>Probabilistic</Button>
        <Button value={1}>Point Forecast</Button>
      </ButtonGroup>
    </div>
  {:else if asOfTimestamp}
    <Timestamp {isDataOutdated} lastUpdatedDate={asOfTimestamp} />
  {:else}
    <Skeleton class="w-[200px] h-[26px]" />
  {/if}
  {#if !isSystemic && showPointForecast && !showProbabilisticForecast}
    <div class="flex gap-md flex-wrap md:justify-start mb-sm">
      <Tabs.Root
        value={isSystemView ? '0' : '1'}
        onValueChange={(value) => {
          selectedRegion = value === '0' ? SYSTEM_REGION : store.territories[0];
          onHandleViewChange();
        }}
      >
        <Tabs.List>
          <Tabs.Trigger value="0">System</Tabs.Trigger>
          <Tabs.Trigger value="1">
            {store.regionsLabel}
          </Tabs.Trigger>
        </Tabs.List>
      </Tabs.Root>

      {#if !isSystemView}
        <Select.Root
          type="single"
          value={selectedRegion.name}
          onValueChange={(value) => {
            selectedRegion = store.territories.find((i) => i.name === value) as Region;
            onHandleViewChange();
          }}
        >
          <Select.Trigger class="w-[200px]">
            {selectedRegion.displayName}
          </Select.Trigger>
          <Select.Content>
            {#each store.territories as territory}
              <Select.Item value={territory.name}>
                {territory.displayName}
              </Select.Item>
            {/each}
          </Select.Content>
        </Select.Root>
      {/if}
    </div>
  {/if}

  <MobileView class="flex-col gap-sm">
    <div class="mobile-first-row">
      {#if isPointForecastView}
        {#if isLoadingForecastCards}
          {#if isMapView}
            <Card class="h-[318px]" loading />
          {:else}
            <PointForecastCardLoading class="w-full" />
          {/if}
        {:else if Object.keys(groupedSystemWeatherData).length}
          <PointForecastCardMobile
            {groupedSystemDailyEvents}
            {groupedSystemWeatherData}
            {isCurrentDay}
            bind:isMapView
            {midnightCustomersAffectedSum}
            {midnightOutagesSum}
            regionName={selectedRegion.name}
            {selectedDay}
            {viewLevel}
            onDayChange={(date, index) => {
              selectedDay = date;
              selectedDayIndex = index;
            }}
          >
            <DailyMap
              {mapConfigs}
              {spatialData}
              bind:selectedIndex
              loading={isLoadingSpatialData}
              {selectedRegion}
              onRegionClick={handleOnRegionClick}
              isLoadingWeather={isLoadingWeatherForecasts}
              weatherStations={weatherStations[selectedDayIndex]}
              {isCurrentDay}
            />
          </PointForecastCardMobile>
        {:else}
          <EmptyPointForecastCard />
        {/if}
      {:else if isForecastLoading}
        <ForecastCardLoading class="w-full" />
      {:else if sessionPredictions.length}
        <ForecastCardMobile
          bind:isMapView
          {selectedDay}
          {sessionPredictions}
          onDayChange={(date, index) => {
            selectedDay = date;
            selectedDayIndex = index;
          }}
        >
          <DailyMap
            {mapConfigs}
            {spatialData}
            bind:selectedIndex
            loading={isLoadingSpatialData}
            {selectedRegion}
            onRegionClick={handleOnRegionClick}
            isLoadingWeather={isLoadingWeatherForecasts}
            weatherStations={weatherStations[selectedDayIndex]}
            {isCurrentDay}
          />
        </ForecastCardMobile>
      {:else}
        <EmptyForecastCard class="w-full" />
      {/if}
    </div>

    {#if showForecastingEvents}
      <div class="flex items-center">
        {#if store.componentsConfig?.[Components.TERRITORY_SUMMARY]}
          <TogglingInfoModal config={store.componentsConfig[Components.TERRITORY_SUMMARY]} />
        {/if}

        <ButtonGroup bind:value={chartViewIndex}>
          <Button value={0}>Hourly</Button>
          <Button value={1}>Trend</Button>
          <Button value={2}>Events</Button>
        </ButtonGroup>
      </div>
    {/if}

    <Card class="w-full !min-h-[224px] {showForecastingEvents ? '' : '!p-0'}">
      {#if !showForecastingEvents}
        <Button
          class="hourly-trend-button px-sm text-xs"
          size="sm"
          onclick={() => (chartViewIndex = chartViewIndex === 0 ? 1 : 0)}
        >
          {chartViewIndex === 0 ? 'Hourly' : 'Trend'}
        </Button>
      {/if}

      {#if chartViewIndex === 0}
        <AgChart
          loading={isLoadingHourlyForecastData || isLoadingAnalogs}
          data={dailyHourlyPredictions?.filter((i) => i.targetDate === selectedDay)}
          options={{
            height: 220,
            title: { text: `Forecasted ${store.capitalizedOutagesLabel}`, fontSize: 14 },
            axes: [
              {
                nice: false,
                position: 'bottom',
                title: { text: `Hour (${timezoneAbbr})` },
                type: 'number',
                label: {
                  formatter: ({ value }: AgAxisLabelFormatterParams) => {
                    return DateTime.fromFormat(selectedDay, 'yyyy-MM-dd')
                      .set({
                        hour: value
                      })
                      .toFormat('ha');
                  }
                },
                crossLines: forecastAnalogs.flatMap((analog) => {
                  const start = DateTime.fromISO(analog.targetStartTime);
                  const end = DateTime.fromISO(analog.targetEndTime);
                  const selected = DateTime.fromFormat(selectedDay, 'yyyy-MM-dd');
                  const selectedStart = selected.startOf('day');
                  const selectedEnd = selected.endOf('day');

                  if (end < selectedStart || start > selectedEnd) return [];

                  const ranges = [];

                  const END_HOUR = 23;

                  // Event starts and spans into the selected day but ends after it
                  if (start.hasSame(selected, 'day') && end > selectedEnd) {
                    ranges.push({
                      range: [start.hour, END_HOUR],
                      strokeWidth: 0,
                      ...getDefaultAnalogCrosslines(analog)
                    });
                  }

                  // Event ends within the selected day but starts before it
                  if (end.hasSame(selected, 'day') && start < selectedStart) {
                    ranges.push({
                      range: [0, end.hour],
                      strokeWidth: 0,
                      ...getDefaultAnalogCrosslines(analog)
                    });
                  }

                  // Event is completely within the selected day
                  if (start >= selectedStart && end <= selectedEnd) {
                    ranges.push({
                      range: [start.hour, end.hour],
                      ...getDefaultAnalogCrosslines(analog)
                    });
                  }

                  return ranges;
                })
              },
              {
                min: 0,
                position: 'left',
                type: 'number',
                ...(dailyHourlyPredictions?.length &&
                  Math.max(...dailyHourlyPredictions.map((i) => i.totalOutagesSum ?? 0)) <
                    yAxisMax && {
                    max: yAxisMax
                  }),
                label: {
                  formatter: function ({ value }: AgAxisLabelFormatterParams) {
                    if (value % 1 === 0) {
                      return value;
                    }
                    return '';
                  }
                }
              }
            ],
            series: [
              {
                type: 'line',
                xKey: 'hour',
                tooltip: {
                  renderer: (data: CustomTooltipRendererParams) =>
                    mobileViewTooltipRenderer(data, selectedDay)
                },
                yKey: 'totalOutagesSum',
                yName: `Forecasted ${store.capitalizedOutagesLabel}`,
                marker: { fill: OUTAGES_STROKE_COLOR, stroke: OUTAGES_STROKE_COLOR },
                stroke: OUTAGES_STROKE_COLOR
              },
              {
                type: 'line',
                xKey: 'hour',
                tooltip: {
                  renderer: (data: CustomTooltipRendererParams) =>
                    mobileViewTooltipRenderer(data, selectedDay)
                },
                yKey: 'midnightOutages',
                yName: `Forecasted ${store.capitalizedOutagesLabel}`,
                marker: { fill: ACTUALS_STROKE_COLOR, stroke: ACTUALS_STROKE_COLOR },
                stroke: ACTUALS_STROKE_COLOR
              }
            ],
            legend: { enabled: false }
          }}
        />
      {:else if chartViewIndex === 1}
        <AgChart
          loading={isChartLoading}
          data={chartData}
          options={{
            axes: [
              { position: 'bottom', title: { text: 'Hour' }, type: 'number' },
              { position: 'left', title: { text: 'Range' }, type: 'number' }
            ],
            height: 220,
            legend: { enabled: false },
            series: rangeAreaSeriesItems,
            title: { text: 'Trailing 24-Hour Forecast', fontSize: 14 }
          }}
        />
      {:else}
        <EventsChartMobile dailyEvents={filteredDailyEvents} />
      {/if}
    </Card>
  </MobileView>

  <DesktopView class="flex-1">
    <div class="flex flex-col gap-3 w-full">
      {#if isPointForecastView}
        <div class="first-row">
          {#if isLoadingForecastCards}
            <PointForecastCardLoading class="forecast-card" />
            <PointForecastCardLoading class="forecast-card" />
            <PointForecastCardLoading class="forecast-card" />
            <PointForecastCardLoading class="forecast-card" />
            <PointForecastCardLoading class="forecast-card" />
          {:else if Object.keys(groupedSystemWeatherData).length}
            {#each Object.entries(groupedSystemWeatherData) as [date, data], index}
              <PointForecastCard
                {date}
                {...index === 0 && { midnightOutagesSum, midnightCustomersAffectedSum }}
                outageData={groupedSystemDailyEvents[date]}
                regionName={selectedRegion.name}
                selected={selectedDay === date}
                {viewLevel}
                weatherData={data}
                onClick={() => {
                  selectedDay = date;
                  selectedDayIndex = index;
                }}
                onKeydown={({ key }: { key: string }) => {
                  if (key === 'Enter') selectedDay = date;
                }}
              />
            {/each}
          {:else}
            <EmptyPointForecastCard />
            <EmptyPointForecastCard />
            <EmptyPointForecastCard />
            <EmptyPointForecastCard />
            <EmptyPointForecastCard />
          {/if}
        </div>
      {:else}
        <div class="first-row">
          {#if isForecastLoading}
            <ForecastCardLoading class="forecast-card" />
            <ForecastCardLoading class="forecast-card" />
            <ForecastCardLoading class="forecast-card" />
            <ForecastCardLoading class="forecast-card" />
            <ForecastCardLoading class="forecast-card" />
          {:else if sessionPredictions.length}
            {#each sessionPredictions as prediction, index}
              <ForecastCard
                data={prediction}
                selected={selectedDay === prediction.date}
                onClick={() => {
                  selectedDay = prediction.date;
                  selectedDayIndex = index;
                }}
                onKeydown={({ key }: { key: string }) => {
                  if (key === 'Enter') selectedDay = prediction.date;
                }}
              />
            {/each}
          {:else}
            <EmptyForecastCard class="forecast-card" />
            <EmptyForecastCard class="forecast-card" />
            <EmptyForecastCard class="forecast-card" />
            <EmptyForecastCard class="forecast-card" />
            <EmptyForecastCard class="forecast-card" />
          {/if}
        </div>
      {/if}

      <Card class="w-full !h-[18vh] min-h-[170px]">
        <AgChart
          loading={isLoadingHourlyForecastData || isLoadingAnalogs}
          data={dailyHourlyPredictions}
          options={{
            axes: [
              {
                label: {
                  formatter: (value: AgAxisLabelFormatterParams) =>
                    customFormatter(value, LINECHART_X_AXIS_FORMAT_DAILY)
                },
                nice: true,
                position: 'bottom',
                type: 'time',
                crossLines: [
                  {
                    type: 'range',
                    range: [selectedDayDate, nextDayDate],
                    strokeWidth: 0
                  },
                  {
                    ...CURRENT_TIME_CROSS_LINE_SERIES_ITEM,
                    value: new Date()
                  },
                  ...forecastAnalogs.map((analog) => ({
                    range: [new Date(analog.targetStartTime), new Date(analog.targetEndTime)],
                    ...getDefaultAnalogCrosslines(analog)
                  }))
                ]
              },
              {
                min: 0,
                position: 'left',
                title: { text: `Forecasted \n ${store.capitalizedOutagesLabel}` },
                type: 'number',
                ...(dailyHourlyPredictions?.length &&
                  Math.max(...(dailyHourlyPredictions ?? []).map((i) => i.totalOutages ?? 0)) <
                    yAxisMax && {
                    max: yAxisMax
                  }),
                label: {
                  formatter: function ({ value }: AgAxisLabelFormatterParams) {
                    if (value % 1 === 0) {
                      return value;
                    }
                    return '';
                  }
                }
              }
            ],
            legend: { enabled: false },
            padding: { right: 50 },
            series: [
              {
                tooltip: { renderer: (data: LineChartTooltipParams) => tooltipRenderer(data, 0) },
                type: 'line',
                xKey: 'date',
                yKey: 'totalOutagesSum',
                yName: `Forecasted ${store.capitalizedOutagesLabel}`,
                marker: { fill: OUTAGES_STROKE_COLOR, stroke: OUTAGES_STROKE_COLOR },
                stroke: OUTAGES_STROKE_COLOR
              },
              {
                tooltip: { renderer: (data: LineChartTooltipParams) => tooltipRenderer(data, 0) },
                type: 'line',
                xKey: 'date',
                yKey: 'midnightOutages',
                yName: `Forecasted ${store.capitalizedOutagesLabel}`,
                marker: { fill: ACTUALS_STROKE_COLOR, stroke: ACTUALS_STROKE_COLOR },
                stroke: ACTUALS_STROKE_COLOR
              }
            ]
          }}
        />
      </Card>

      <div class="flex">
        {#if store.componentsConfig?.[Components.TERRITORY_SUMMARY]}
          <TogglingInfoModal config={store.componentsConfig[Components.TERRITORY_SUMMARY]} />
        {/if}

        <div class="flex flex-1 justify-between items-start">
          <ButtonGroup size="icon" bind:value={activePageMode}>
            <Button tooltip={PageModes.weather} value={PageModes.weather}>
              <span class="material-icons">foggy</span>
            </Button>
            <Button tooltip={PageModes.outage} value={PageModes.outage}>
              <span class="material-icons">stacked_line_chart</span>
            </Button>
            {#if showForecastingEvents}
              <Button tooltip={PageModes.forecast} value={PageModes.forecast}>
                <span class="material-icons">pie_chart</span>
              </Button>
            {/if}
          </ButtonGroup>

          {#if activePageMode === PageModes.weather}
            <Button
              size="icon"
              variant="ghost"
              tooltip={showDualView ? 'Show Single' : 'Show Dual'}
              onclick={() => (showDualView = !showDualView)}
            >
              <span class="material-icons">
                {showDualView ? 'splitscreen_left' : 'fullscreen_portrait'}
              </span>
            </Button>
          {/if}
        </div>
      </div>

      <div class="second-row flex-1">
        {#if activePageMode === PageModes.weather}
          <div class="flex w-full gap-sm">
            {#if showDualView}
              <DailyMap
                {mapConfigs}
                {spatialData}
                bind:selectedIndex
                loading={isLoadingSpatialData}
                {selectedRegion}
                onRegionClick={handleOnRegionClick}
                isLoadingWeather={isLoadingWeatherForecasts}
                weatherStations={weatherStations[selectedDayIndex]}
                {isCurrentDay}
              />
              <DailyMap
                {mapConfigs}
                {spatialData}
                selectedIndex={selectedIndex + 1}
                loading={isLoadingSpatialData}
                {selectedRegion}
                onRegionClick={handleOnRegionClick}
                isLoadingWeather={isLoadingWeatherForecasts}
                weatherStations={weatherStations[selectedDayIndex]}
                {isCurrentDay}
              />
            {:else}
              <DailyMap
                {mapConfigs}
                {spatialData}
                bind:selectedIndex
                loading={isLoadingSpatialData}
                {selectedRegion}
                onRegionClick={handleOnRegionClick}
                weatherStations={weatherStations[selectedDayIndex]}
                {isCurrentDay}
              />
            {/if}
          </div>
        {:else if activePageMode === PageModes.outage}
          <Card class="w-full min-h-[250px]">
            <AgChart
              loading={isChartLoading}
              data={chartData}
              options={{
                axes: [
                  { position: 'bottom', title: { text: 'Hour' }, type: 'number' },
                  {
                    position: 'left',
                    title: { text: store.capitalizedOutagesLabel },
                    type: 'number',
                    crossLines: threatLevelCrossLines
                  }
                ],
                legend: { enabled: false },
                series: rangeAreaSeriesItems,
                title: { text: 'Trailing 24-Hour Forecast' },
                navigator: { enabled: false }
              }}
            />
          </Card>
        {:else if activePageMode === PageModes.forecast}
          {#if isEventsLoading}
            <Loader />
          {:else}
            <EventsChart dailyEvents={filteredDailyEvents} />
          {/if}
        {/if}
      </div>
    </div>
  </DesktopView>
</section>

<style>
  :global(.hourly-trend-button) {
    background: var(--color-primary);
    position: absolute;
    right: 4px;
    top: 4px;
    z-index: 1;
  }

  .daily .first-row {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  :global(.daily .first-row .forecast-card) {
    width: 19.5%;
    transition: filter 0.3s ease-in-out;
  }

  :global(.daily .first-row .forecast-card:hover) {
    filter: brightness(95%);
  }

  .daily .second-row {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  :global(.daily .second-row .spatial-card) {
    width: 100%;
  }

  @media (max-width: 1555px) {
    .daily .first-row {
      gap: 0.5rem;
      justify-content: flex-start;
    }

    :global(.daily .first-row .forecast-card) {
      width: calc(33.33% - 0.5rem);
    }
  }

  @media (max-width: 800px) {
    .daily .second-row {
      gap: 0.25rem;
    }

    :global(.daily .second-row .spatial-card) {
      width: 100%;
    }
  }

  @media (max-width: 767px) {
    :global(.daily .first-row, .daily .second-row) {
      display: none !important;
    }
  }

  :global(.daily .forecast-card .headerIcon) {
    display: none;
  }

  @media (max-width: 1075px) {
    :global(.daily .first-row .forecast-card .headerIcon, .weatherIcon) {
      display: block;
    }

    :global(.daily .first-row .alertIcon) {
      display: none;
    }
  }
</style>
