<script lang="ts">
  import { onMount } from 'svelte';
  import { base } from '$app/paths';
  import { goto } from '$app/navigation';
  import { Skeleton } from '$lib/components/ui/skeleton';
  import { ShiftCard, ShiftCardMobile, ShiftCardLoading } from '$lib/components';
  import { getOutageColor } from '$lib/config/utils';
  import store from '$lib/stores/app.svelte';
  import { FeatureFlag, type ChartConfig, type HourlyShift, type Shift } from '$lib/types';
  import { getChartConfigs, getHourlyShifts, getLatestSessionId, getShifts } from '$lib/api';

  const SHOWN_SHIFTS_NUMBER = 3;

  let chartConfigs = $state<ChartConfig[]>([]);
  let hourlyShifts = $state<HourlyShift[]>([]);
  let isLoading = $state<boolean>(true);
  let shifts = $state<Shift[]>([]);
  let totalOutageRange = $state<string>('');
  let warningColor = $state<string>('var(--color-text)');
  let yAxisChartDomainMap = $state<Record<string, { min: number; max: number }>>({});

  onMount(async () => {
    if (!validateRouteEntry()) {
      goto(`${base}/`);
      return;
    }

    isLoading = true;

    await Promise.allSettled([fetchShifts(), fetchHourlyShifts()]);

    isLoading = false;
  });

  function calculateYAxisChartDomain() {
    const domainMap = hourlyShifts.reduce(
      (map, { hourlyZones }) => {
        hourlyZones.forEach(({ weatherAttributes }) => {
          Object.entries(weatherAttributes).forEach(([key, value]) => {
            if (map[key]) {
              map[key].min = Math.min(map[key].min, value);
              map[key].max = Math.max(map[key].max, value);
            } else {
              map[key] = { min: value, max: value };
            }

            if (key === 'threatLevel') {
              const range = chartConfigs.find((c) => c.attributeName === 'threatLevel')?.yaxis
                .range;
              if (range) {
                map[key].min = Math.min(map[key].min, range.min);
                map[key].max = Math.max(map[key].max, range.max);
              }
            }
          });
        });
        return map;
      },
      {} as Record<string, { min: number; max: number }>
    );
    return domainMap;
  }

  function validateRouteEntry() {
    return store.featureFlags?.[FeatureFlag.shifts];
  }

  function setWarningDisplay() {
    const min = shifts.reduce((acc, s) => acc + s.outages.min, 0);
    const max = shifts.reduce((acc, s) => acc + s.outages.max, 0);
    totalOutageRange = `${Math.round(min)} - ${Math.round(max)}`;
    const avgOutageCount = (max + min) / 2;
    warningColor = getOutageColor(avgOutageCount)!;
  }

  async function fetchShifts() {
    try {
      const sessionId = await getLatestSessionId('shift_zone');
      shifts = (await getShifts(sessionId)).slice(0, SHOWN_SHIFTS_NUMBER);
      if (shifts.length) setWarningDisplay();
    } catch (err) {
      console.error(err);
    }
  }

  async function fetchHourlyShifts() {
    try {
      const res = await getChartConfigs();
      chartConfigs = res ? res.configs.filter((c) => c.isDisplayed) : [];
      const sessionId = await getLatestSessionId('hourly_zone');
      hourlyShifts = await getHourlyShifts(sessionId);
      if (hourlyShifts.length) yAxisChartDomainMap = calculateYAxisChartDomain();
    } catch (err) {
      console.error(err);
    }
  }
</script>

<svelte:head>
  <title>Hourly</title>
  <meta name="Hourly" content="Hourly" />
</svelte:head>

<section class="shifts flex flex-col gap-sm">
  {#if isLoading && !shifts.length}
    <Skeleton class="h-[28px] w-[245px]" />
  {/if}
  {#if shifts.length}
    {#if totalOutageRange}
      <div class="warning-display text-m" style:border-color={warningColor}>
        <span class="material-icons warning-icon" style:color={warningColor}>warning</span>
        {totalOutageRange} outages in the next 36 hours
      </div>
    {/if}
  {/if}
  <div class="flex flex-col gap-3">
    <div class="mobile-view mobile-first-row">
      {#if isLoading}
        <ShiftCardLoading class="w-full" />
      {:else if chartConfigs}
        <ShiftCardMobile {chartConfigs} {hourlyShifts} {shifts} />
      {:else}
        <span class="text-lg text-center">No Shifts Found</span>
      {/if}
    </div>

    <div class="row first-row">
      {#if isLoading}
        <ShiftCardLoading class="shift-card" />
        <ShiftCardLoading class="shift-card" />
        <ShiftCardLoading class="shift-card" />
      {:else if chartConfigs}
        <ShiftCard
          {chartConfigs}
          hourlyShift={hourlyShifts[0]}
          shift={shifts[0]}
          {yAxisChartDomainMap}
        />
        <ShiftCard
          {chartConfigs}
          hourlyShift={hourlyShifts[1]}
          shift={shifts[1]}
          {yAxisChartDomainMap}
        />
        <ShiftCard
          {chartConfigs}
          hourlyShift={hourlyShifts[2]}
          shift={shifts[2]}
          {yAxisChartDomainMap}
        />
      {:else}
        <span class="text-lg text-center">No Shifts Found</span>
      {/if}
    </div>
  </div>
</section>

<style>
  section {
    color: var(--color-text);
  }

  .warning-display {
    align-self: flex-start;
    border-bottom: 1px solid;
    padding-bottom: 4px;
  }

  .warning-icon {
    font-size: 16px;
    position: relative;
    top: 4px;
  }

  .shifts .row {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    min-height: 706px;
  }

  :global(.shifts .shift-card) {
    width: 33%;
  }

  :global(.shifts .mobile-view) {
    display: none;
  }

  .shifts .mobile-first-row {
    min-height: 706px;
  }

  @media (max-width: 1200px) {
    .shifts .row {
      gap: 0.5rem;
    }

    :global(.shifts .row .shift-card) {
      width: calc(50% - 0.25rem);
    }
  }

  @media (max-width: 767px) {
    :global(.shifts .mobile-view) {
      display: flex;
    }

    :global(.shifts .first-row) {
      display: none !important;
    }
  }
</style>
