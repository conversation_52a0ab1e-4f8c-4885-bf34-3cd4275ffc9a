@import 'mapboxgl-legend/dist/style.css';

.mapboxgl-ctrl-legend {
  margin: 0 !important;
  max-width: none;
}

.mapboxgl-ctrl-legend .panes {
  background: var(--color-component);
  border: 1px solid var(--color-border);
}

.mapboxgl-ctrl-legend .minimizer {
  background: var(--color-component) !important;
  color: white !important;
  border: 1px solid var(--color-border);
}

.mapboxgl-ctrl-legend .minimizer {
  background-color: var(--color-component) !important;
  display: flex;
  justify-content: center;
  align-items: center;
}

.mapboxgl-ctrl-legend .minimizer::before {
  font-family: 'Material Icons';
  content: 'layers' !important;
  font-size: 18px;
  color: var(--color-text);
}

.mapboxgl-ctrl-legend .minimizer:hover {
  background-color: var(--color-component) !important;
  filter: contrast(50%);
}

.mapboxgl-ctrl-legend summary {
  color: var(--color-text);
  margin: 3px;
  text-transform: none;
}

.mapboxgl-ctrl-legend ul.list {
  margin: 3px;
}

.mapboxgl-ctrl-legend .list li {
  color: var(--color-text);
}

.mapbox-popup {
  color: var(--color-text) !important;
}

.mapboxgl-ctrl-legend-pane summary {
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  padding-left: 14px;
}

/* Create the carrot using ::before */
.mapboxgl-ctrl-legend-pane summary::before {
  content: '';
  position: absolute;
  left: 0;
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 6px solid var(--color-text);
  transition: transform 0.3s ease;
}

.mapboxgl-ctrl-legend-pane[open] summary::before {
  transform: rotate(180deg);
}

.mapboxgl-ctrl-legend .list li {
  text-transform: none !important;
}
