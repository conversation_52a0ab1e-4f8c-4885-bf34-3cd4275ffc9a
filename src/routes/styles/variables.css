:root {
  /* sizes */
  --size-xs: 0.25rem;
  --size-s: 0.5rem;
  --size-m: 1rem;
  --size-l: 1.5rem;
  --size-xl: 2rem;
  --size-sidebar: 8.2rem;
  --size-sidebar-expanded: 14rem;
  --size-topbar: 4rem;
  --size-sidebar-sm: 3rem;
  --vh: calc(100vh / 100);
  --content-padding: 1.5rem;
  --content-padding-mobile: 1rem;
  --mobile-margin-top: 4rem;

  /* roundings */
  --rounding-xs: 0.125rem;
  --rounding-s: 0.25rem;
  --rounding-m: 0.5rem;
  --rounding-l: 1rem;
  --rounding-xl: 1.5rem;

  --border-radius: var(--rounding-m);
  --border-radius-inner: calc(var(--rounding-m) - 1px);

  /* Animations */
  --slide-speed: 250ms ease-out;

  /* typography */
  --font-primary:
    ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>,
    'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
    'Segoe UI Symbol', 'Noto Color Emoji';
  --font-body:
    Arial, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell,
    'Open Sans', 'Helvetica Neue', sans-serif;
  --font-mono: 'Fira Mono', monospace;

  /* gray */
  --color-gray-100: hsla(0, 0%, 98%, 1);
  --color-gray-200: hsla(135, 3%, 85%, 1);
  --color-gray-300: hsla(135, 3%, 72%, 1);
  --color-gray-400: hsla(135, 3%, 60%, 1);
  --color-gray-500: hsla(135, 3%, 49%, 1);
  --color-gray-600: hsla(135, 3%, 38%, 1);
  --color-gray-700: hsla(135, 3%, 31%, 1);
  --color-gray-800: hsla(135, 3%, 24%, 1);
  --color-gray-900: hsla(135, 3%, 12%, 1);

  --off-white: rgb(245, 245, 245);

  --color-nav-drawer: #e5e8eb;
  --color-background-accent: #e1e6ed;
  --color-accent-text: var(--color-gray-600);

  --color-success: #3cb371;
  --color-warning: #f7911d;
  --color-danger: #d54040;
  --color-info: #0043ce;
  --color-secondary-text: var(--color-gray-500);

  --color-background: #fafafa;
  --color-component: #fff;
  --color-border: #d8dad9;
  --color-text: #000;
  --color-accentGray: #e8e8e8;
  --color-link: var(--color-primary);
  --color-modal-overlay: rgba(10, 10, 10, 0.95);

  /*Configurable Values*/
  --color-primary: #36813c;
  --color-secondary: #6480ba;
  --color-sidebar-background: var(--color-primary);
  --color-nav-active: #24521e;
}

.dark {
  --color-nav-drawer: #242424;
  --color-background-accent: var(--color-gray-600);
  --color-accent-text: var(--color-gray-300);

  --color-success: #166534;
  --color-warning: #aa4409;
  --color-danger: #7f1d1d;
  --color-info: #1e40af;
  --color-text: var(--off-white);
  --color-secondary-text: var(--color-gray-200);

  --color-background: #121212;
  --color-component: #161616;
  --color-border: #282828;
  --color-text: #f5f5f5;
  --color-accentGray: #32353e;
  --color-modal-overlay: rgba(0, 0, 0, 0.95);

  /*Configurable Values*/
  --color-primary: #24521e;
  --color-secondary: #6480ba;
  --color-sidebar-background: var(--color-component);
  --color-nav-active: var(--color-primary);
}

@media screen and (max-width: 480px) {
  :root {
    --size-sidebar: var(--size-sidebar-sm);
  }
}
