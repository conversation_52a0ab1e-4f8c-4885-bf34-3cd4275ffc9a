.mapboxgl-popup {
  max-width: none !important;
  z-index: 2;
}

.mapboxgl-popup-close-button {
  color: var(--color-text);
}

.mapboxgl-popup-content {
  background: var(--color-component);
  color: var(--color-text);
  line-height: normal;
  border-radius: var(--border-radius);
  border: 1px solid var(--color-border) !important;
}

.mapboxgl-popup-content .popup-feature-name {
  font-size: 0.875rem;
  font-weight: bold;
  margin-bottom: 0.75rem;
  text-align: center;
  white-space: normal;
  text-transform: uppercase;
}

.mapboxgl-popup-content span {
  font-size: 0.825rem;
}

.mapboxgl-popup-tip {
  border-bottom-color: var(--color-border) !important;
  border-top-color: var(--color-border) !important;
}

.info-row {
  display: flex;
  margin-bottom: 0.375rem;
  justify-content: center;
}

.info-row .value {
  font-weight: bold;
}

.shifts .popup-region-info .info-row .label {
  text-align: right;
}

.popup-region-info .info-row .value {
  margin-left: 0.5rem;
}

.mapboxgl-ctrl.mapboxgl-ctrl-legend {
  margin: 8px 8px 0 0 !important;
}

.mapboxgl-ctrl-legend-pane {
  padding: 8px !important;
}

.mapboxgl-ctrl button.mapboxgl-ctrl-compass {
  display: flex !important;
}

.mapboxgl-ctrl button.mapboxgl-ctrl-compass .mapboxgl-ctrl-icon {
  background-color: var(--color-component);
  background-image: url("data:image/svg+xml;charset=utf-8,<svg width='29' height='29' viewBox='0 0 29 29' xmlns='http://www.w3.org/2000/svg' fill='%233333'> <path d='M10.5 14l4-8 4 8h-8z' fill='%23ff0000'/> <path id='south' d='M10.5 16l4 8 4-8h-8z' fill='%23ccc'/> </svg>");
}

.mapboxgl-ctrl-scale {
  background-color: var(--color-component);
  color: var(--color-text);
  border-radius: 4px;
  border: 1px solid var(--color-border) !important;
  width: 60px !important;
}

.mapboxgl-ctrl-group {
  display: flex;
  width: auto;
  background: var(--color-component) !important;
  align-items: center;
  border: 1px solid var(--color-border) !important;
  box-shadow: none !important;
  overflow: hidden;
}

.mapboxgl-ctrl-group button + button {
  border-top: none;
  border-radius: 0 !important;
  border-left: 1px solid var(--color-border) !important;
}
