import { etrServiceClient } from './http-client';
import qs from 'qs';
import { StormMode } from '$lib/types';
import type {
  EtrSummary,
  EventBody,
  RawStormData,
  StormData,
  TerritorySimChartData,
  Resource,
  ResourceOperationBody,
  RegionalResourceState,
  AggregatedResourceOperations,
  EtrOutageOverview,
  SimulationSession,
  SimulatedRegion,
  RegionalEtrLevel,
  SimulationOutageScale,
  SimulationTime,
  SessionEtrResponse,
  Crew,
  ResourceVisualization,
  SystemStateHistory,
  ActiveCrew,
  EtrSimulation,
  CustomOutagesData,
  OutagesOverview,
  OutagesData
} from '$lib/types';
import { formatStormData } from '$lib/utils';
import { OutagesChartScope } from '$lib/config/etr';
import { ErrorStatusCodes } from '$lib/config/main';
import type { AxiosError } from 'axios';

export const getLatestEtrSessionId = async (stormId: string): Promise<string> => {
  return await etrServiceClient.get('etrs/sessions/latest', {
    params: { stormId }
  });
};

export const getSessionEtrs = async (id: string): Promise<SessionEtrResponse> => {
  return await etrServiceClient.get(`etrs/${id}/latest`);
};

export const getHistoricalSessionEtrs = async (id: string): Promise<SessionEtrResponse> => {
  return await etrServiceClient.get(`etrs/${id}/type?etrType=result`);
};

export const getEtrResults = async (
  stormID: string,
  isForecasted: boolean
): Promise<EtrSimulation | TerritorySimChartData[]> => {
  return etrServiceClient.get('etrs/latest/ETRResults', {
    params: { isForecasted, stormID }
  });
};

export const getHistoricalEtrResults = async (
  stormID: string,
  isForecasted: boolean,
  sessionId: string
): Promise<EtrSimulation | TerritorySimChartData[]> => {
  return etrServiceClient.get('etrs/historical/ETRResults', {
    params: { isForecasted, sessionId, stormID }
  });
};

export const getLatestSimulation = async (
  stormId: string,
  etrSessionId: string
): Promise<SimulationSession> => {
  return await etrServiceClient.get('etrs/latest/session/userSimulation', {
    params: { stormId, etrSessionId }
  });
};

export const getEtrSimulationResults = async (
  stormID: string,
  isForecasted: boolean,
  simulationSessionId: string
): Promise<TerritorySimChartData[]> => {
  try {
    return await etrServiceClient.get('etrs/ETRSimulationResults', {
      params: { stormID, isForecasted, simulationSessionId }
    });
  } catch (err: unknown) {
    const error = err as AxiosError;
    if (error?.response?.status === ErrorStatusCodes.NOT_FOUND) {
      return [];
    } else {
      throw error;
    }
  }
};

export const getSimulationResources = async (
  stormId: string,
  type: 'current' | 'future',
  simulationSessionId: string
): Promise<Resource[]> => {
  return etrServiceClient.get(`etrs/resources/${stormId}/${type}`, {
    params: { generationType: 'simulation', simulationSessionId }
  });
};

export const postEtrEvent = async (body: EventBody): Promise<void> => {
  return await etrServiceClient.post('etrs/events', body);
};

export const postResourceOperation = async (body: ResourceOperationBody): Promise<void> => {
  return await etrServiceClient.post('v2/resources/operations', body);
};

export const getSessionEtrSummary = async (
  id: string,
  type: OutagesChartScope
): Promise<EtrSummary> => {
  return await etrServiceClient.get(`etrs/${id}/latest/summary/${type}`);
};

export const getOutagesOverview = async (
  scope: OutagesChartScope,
  isForecast: boolean,
  summarySessionId?: string,
  startTime?: string
): Promise<EtrOutageOverview[]> => {
  const response: OutagesOverview = await etrServiceClient.get('etrs/outages/timeframe/overview', {
    params: {
      regionsToFilter: scope === OutagesChartScope.SYSTEM ? 'System' : '',
      isForecast,
      ...(summarySessionId && { summarySessionId }),
      ...(startTime && { startTime })
    }
  });

  const outages = Object.entries(response.outageRecord).flatMap(([scope, list]) =>
    list.map((i) => ({ ...i, scope }))
  );

  return outages;
};

export const getActiveStorm = async (): Promise<StormData | null> => {
  const list: RawStormData[] = await etrServiceClient.get('etrs/storms/active');

  if (list.length) {
    const sortedList = list
      .filter((d) => d.stormMode !== StormMode.ARCHIVE)
      .sort((a, b) => b.creationDate.localeCompare(a.creationDate));
    // Return the storm with the latest creationDate
    return sortedList.length ? formatStormData(sortedList[0]) : null;
  } else {
    return null;
  }
};

export const getStorm = async (id: string): Promise<StormData | null> => {
  const data: RawStormData = await etrServiceClient.get(`etrs/storms/${id}`);
  return formatStormData(data);
};

export const getAllStorms = async (): Promise<StormData[]> => {
  const list: RawStormData[] = await etrServiceClient.get('etrs/storms');
  return list.map(formatStormData);
};

export const getActiveOutages = async (): Promise<CustomOutagesData | null> => {
  const data: OutagesData = await etrServiceClient.get('etrs/outages/latest');
  return data ? { ...data, outageRecord: data?.outageRecords[0] } : null;
};

export const updateStormModel = async (id: string, attr: string, value: any) => {
  const body: Record<string, string> = {};
  body[attr] = value;
  return etrServiceClient.patch(`etrs/storms/${id}`, body);
};

export const getResources = async (
  stormId: string,
  type: 'current' | 'future'
): Promise<Resource[]> => {
  return etrServiceClient.get(`etrs/resources/${stormId}/${type}`);
};

export const getCurrentResourcesV2 = async (stormId: string): Promise<RegionalResourceState> => {
  return etrServiceClient.get(`v2/resources/system-states/latest`, {
    params: { stormId }
  });
};

export const getFutureResourcesV2 = async (
  stormId: string
): Promise<AggregatedResourceOperations[]> => {
  return etrServiceClient.get(`v2/resources/operations/pending`, {
    params: { stormId }
  });
};

export const getDefaultResources = async (): Promise<Resource[]> => {
  return await etrServiceClient.get('etrs/resources/default');
};

export const getDefaultResourcesV2 = async (stormId?: string): Promise<RegionalResourceState> => {
  return await etrServiceClient.get('v2/resources/system-states/default', {
    params: { stormId }
  });
};

export const postDefaultResources = async (body: Resource[]): Promise<void> => {
  return await etrServiceClient.post('etrs/resources/default', body);
};

export const postDefaultResourcesV2 = async (body: RegionalResourceState): Promise<void> => {
  return await etrServiceClient.post('v2/resources/system-states/default', body);
};

export const createBatchCrews = async (stormId: string, crews: Crew[]): Promise<void> => {
  return await etrServiceClient.post('/v2/resources/crews/batch', {
    stormId,
    crews
  });
};

export const getSimulatedRegions = async (
  sessionId: string,
  outageScale: SimulationOutageScale,
  regionalEtrType: RegionalEtrLevel
): Promise<SimulatedRegion[]> => {
  return await etrServiceClient.get('etrs/simulation-regional-chart', {
    params: { sessionId, outageScale, regionalEtrType }
  });
};

export const getMaxEstimatedSimulationTimes = async (
  outageScale: SimulationOutageScale,
  regionalEtrTypes: RegionalEtrLevel[],
  sessionId?: string
): Promise<SimulationTime> => {
  return await etrServiceClient.get('etrs/simulation-regional-chart/max-estimated-dates', {
    params: { sessionId, outageScale, regionalEtrTypes },
    paramsSerializer: (params) => qs.stringify(params, { arrayFormat: 'repeat' })
  });
};

export const getResourceVisualization = async (stormId: string): Promise<ResourceVisualization> => {
  return await etrServiceClient.get(`v2/resources/visualization`, {
    params: { stormId }
  });
};

export const getResourceVisualizationHistory = async (
  startDate: string,
  endDate: string
): Promise<SystemStateHistory> => {
  return await etrServiceClient.get(`/v2/resources/system-states/history`, {
    params: { startDate, endDate }
  });
};

export const getActiveCrews = async (stormId: string, enRoute?: boolean): Promise<ActiveCrew[]> => {
  return await etrServiceClient.post('/v2/resources/crews/search', {
    stormId,
    enRoute
  });
};

export const getResourceRoles = async (): Promise<string[]> => {
  return await etrServiceClient.get(`/v2/resources/crews/resource-roles`);
};

export const getInitialTerritories = async (): Promise<string[]> => {
  return await etrServiceClient.get(`/v2/resources/crews/initial-territories`);
};

export const deletePendingOperation = async (operationId: string): Promise<void> => {
  return await etrServiceClient.delete(`/v2/resources/operations/pending/${operationId}`);
};

export const getToggledEvents = async (aggregateId: string): Promise<EventBody[]> => {
  return await etrServiceClient.post('etrs/events/search', {
    aggregateId,
    type: 'resourceDefaultsToggled'
  });
};

export const setCrewReleaseTime = async (crewId: string, releaseTime: string): Promise<void> => {
  return await etrServiceClient.patch(`/v2/resources/crews/${crewId}/release`, {
    releaseTime
  });
};
