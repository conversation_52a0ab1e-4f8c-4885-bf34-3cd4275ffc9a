import { shiftServiceClient } from './http-client';
import type { HourlyShift, Shift } from '$lib/types';

export const getLatestSessionId = async (type: string): Promise<string> => {
  return await shiftServiceClient.get(`shifts/sessions/${type}/latest`);
};

export const getShifts = async (id: string): Promise<Shift[]> => {
  return await shiftServiceClient.get(`shifts/sessions/${id}`);
};

export const getHourlyShifts = async (id: string): Promise<HourlyShift[]> => {
  return await shiftServiceClient.get(`shifts/hourlyShifts/${id}`);
};
