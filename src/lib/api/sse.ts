import toast from '$lib/stores/toast.svelte';
import { EventSourcePolyfill } from 'event-source-polyfill';
import { getAuthToken } from '../authentication';
import { getBaseApiUrl } from './http-client';

// Example response from endpoint upon completion of a resource operation
// {
//     "id": "67cb1e62fe2ba742f037f101",
//     "stormId": "67cb0b0708b1717f316e0346",
//     "pendingGroupId": null,
//     "crewId": "67cb1e62fe2ba742f037f100",
//     "status": "COMPLETED",
//     "type": "ADD",
//     "creationDate": "2025-03-07T11:27:14.101-05:00",
//     "createdBy": "<EMAIL>",
//     "resources": {
//         "companyWorkers": 500
//     },
//     "executionTime": "2025-03-07T11:28:00-05:00",
//     "specificMetadata": {
//         "toTerritoryId": "66608676df0fa432ed144c64"
//     }
// }

export async function listenToResourceOperationUpdates(
  onUpdate: () => void,
  territoryMap: Map<string, string>
) {
  let eventSource: EventSourcePolyfill | null = null;
  const token = await getAuthToken();
  const baseUrl: string = getBaseApiUrl();

  const connect = () => {
    if (eventSource) {
      eventSource.close();
    }

    eventSource = new EventSourcePolyfill(
      `${baseUrl}storm-etr-service/sse-events/operation-status-updates`,
      {
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
    );

    eventSource.addEventListener(
      'operation-status-update' as keyof EventSourceEventMap,
      (event) => {
        try {
          const messageEvent = event as MessageEvent;
          const operation = JSON.parse(messageEvent.data);
          const count = Object.values(operation.resources)[0];
          const fromTerritory =
            territoryMap.get(operation.specificMetadata.fromTerritoryId) ?? 'Out of System';
          const toTerritory =
            territoryMap.get(operation.specificMetadata.toTerritoryId) ?? 'Out of System';

          const showToast = (action: string, status: 'success' | 'error') => {
            const prefix = status === 'success' ? '' : 'could not ';
            let message = '';

            switch (operation.type) {
              case 'ADD':
                message = `${count} resources ${prefix}${action} ${toTerritory}`;
                break;
              case 'REMOVE':
                message = `${count} resources ${prefix}${action} ${fromTerritory}`;
                break;
              case 'MOVE':
                message = `${count} resources ${prefix}${action} ${fromTerritory} to ${toTerritory}`;
                break;
            }

            toast.addToast({ message, type: status });
          };

          if (operation.status === 'COMPLETED') {
            switch (operation.type) {
              case 'ADD':
                showToast('added to', 'success');
                break;
              case 'REMOVE':
                showToast('removed from', 'success');
                break;
              case 'MOVE':
                showToast('moved from', 'success');
                break;
            }
            onUpdate();
          } else if (operation.status === 'FAILED') {
            switch (operation.type) {
              case 'ADD':
                showToast('added to', 'error');
                break;
              case 'REMOVE':
                showToast('removed from', 'error');
                break;
              case 'MOVE':
                showToast('moved from', 'error');
                break;
            }
            console.error(`Crew ${operation.crew} failed during execution`);
          }
        } catch (error) {
          console.error('Error processing operation update:', error);
        }
      }
    );

    eventSource.onerror = (error) => {
      console.error('Connection lost:', error);
      eventSource?.close();
    };
  };

  connect();

  return eventSource;
}
