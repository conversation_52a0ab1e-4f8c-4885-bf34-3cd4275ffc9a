import { notificationServiceClient } from './http-client';
import type { NotificationSettings, UserNotificationSettings, Subscription } from '$lib/types';

export const getUserNotificationSettings = async (
  userEmail: string
): Promise<UserNotificationSettings[] | null> => {
  return await notificationServiceClient.get(
    `subscriptions?email=${encodeURIComponent(userEmail)}`
  );
};

export const getNotificationSettings = async (): Promise<NotificationSettings[]> => {
  return await notificationServiceClient.get('notifications?isEnabled=true');
};

export const saveNewNotificationSettings = async (body: Subscription): Promise<Subscription> => {
  return await notificationServiceClient.post('subscriptions', body);
};
