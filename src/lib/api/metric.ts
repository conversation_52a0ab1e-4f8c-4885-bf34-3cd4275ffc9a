import qs from 'qs';
import { metricServiceClient } from './http-client';
import type {
  AggregateMetricResponse,
  MetricResponse,
  StormRetrospectivesResponse,
  StormRetrospectiveData,
  RawWeatherData,
  DeltaAmiMeterData
} from '$lib/types';

const DEFAULT_GRANULARITY = 60;

export const getStormMetricData = async (
  metric: MetricResponse['metricType'],
  stormId: string,
  isSystem: boolean = true
): Promise<MetricResponse> => {
  return await metricServiceClient.get(`metrics/${metric}/${stormId}?isSystem=${isSystem}`);
};

export const getStormAggregateOverview = async (
  start: string,
  end: string,
  outageGranularity: number = DEFAULT_GRANULARITY
): Promise<AggregateMetricResponse> => {
  return await metricServiceClient.get(
    `overview/aggregate?start=${start}&end=${end}&outageGranularity=${outageGranularity}`
  );
};

export const getStormAggregate = async (
  stormId: string,
  outageGranularity: number = DEFAULT_GRANULARITY
): Promise<AggregateMetricResponse> => {
  return await metricServiceClient.get(
    `overview/stormAggregate?stormId=${stormId}&outageGranularity=${outageGranularity}`
  );
};

export const getRetrospectives = async (): Promise<StormRetrospectivesResponse> => {
  return metricServiceClient.get(`/retrospectives/search/findAllByOrderByIdDesc`);
};

export const getStormRetrospective = async (stormId: string): Promise<StormRetrospectiveData> => {
  return metricServiceClient.get(`/retrospectives/search/findByStormId?stormId=${stormId}`);
};

export const getLatestWeatherData = async (): Promise<RawWeatherData> => {
  return await metricServiceClient.get('/weather/search/findFirstByOrderByTimestampDesc');
};

export const getAmiData = async (
  stormStart: string,
  stormEnd: string
): Promise<DeltaAmiMeterData> => {
  return await metricServiceClient.get(`/meters/ami-replay`, {
    params: { stormStart, stormEnd },
    headers: { 'Content-Encoding': 'gzip' },
    paramsSerializer: (params) => qs.stringify(params, { arrayFormat: 'repeat' })
  });
};

export const getWeatherDataBetweenTimestamps = async (
  start: string,
  end: string
): Promise<RawWeatherData[]> => {
  return await metricServiceClient.get(
    `/weather/search/findByTimestampBetweenOrderByTimestampAsc?start=${start}&end=${end}`
  );
};
