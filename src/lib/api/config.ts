import type {
  AppConfig,
  ChartConfig,
  ComponentConfig,
  DisplayConfig,
  EventLevel,
  Feature,
  MapConfig,
  Region,
  RoleConfig,
  ThemeConfig,
  ThemeConfigResp
} from '$lib/types';
import type { FeatureFlag } from '$lib/types';
import { configServiceClient } from './http-client';

export const getComponentConfigs = async (): Promise<Record<string, any>> => {
  const resp: { componentConfigs: ComponentConfig[] } = await configServiceClient.get(
    '/componentConfigs?size=40'
  );
  return resp.componentConfigs.reduce(
    (result, item) => ({ ...result, [item.component]: item.configuration }),
    {}
  );
};

export const getMapConfigs = async (): Promise<MapConfig[]> => {
  return await configServiceClient.get('maps/legend');
};

export const getChartConfigs = async (): Promise<{ configs: ChartConfig[] }> => {
  return await configServiceClient.get('charts');
};

export const getEventLevels = async (): Promise<EventLevel[]> => {
  return await configServiceClient.get('eventLevels');
};

export const getAppConfig = async (): Promise<AppConfig> => {
  return await configServiceClient.get('tileset');
};

export const getDisplayConfigs = async (): Promise<Record<string, any>> => {
  const resp: { displayConfigs: DisplayConfig[] } =
    await configServiceClient.get('/displayConfigs?size=40');
  return resp.displayConfigs.reduce(
    (result, item) => ({ ...result, [item.type]: item.configuration }),
    {}
  );
};

export const getRoleConfigs = async (): Promise<Record<string, any>> => {
  const resp: { roleConfigs: RoleConfig[] } = await configServiceClient.get('/roleConfigs?size=40');
  return resp.roleConfigs.reduce(
    (result, item) => ({ ...result, [item.type]: item.configuration }),
    {}
  );
};

export const getThemeConfigs = async (): Promise<Record<string, ThemeConfig>> => {
  const resp: { themeConfigs: ThemeConfigResp[] } =
    await configServiceClient.get('/themeConfigs?size=40');

  return resp.themeConfigs.reduce(
    (result, item) => ({ ...result, [item.type]: item.configuration }),
    {}
  );
};

export const getFeatureFlags = async () => {
  const data: { featureFlags: Feature[] } = await configServiceClient.get('featureFlags');

  return data.featureFlags.reduce(
    (acc, { key, value }) => {
      acc[key as FeatureFlag] = value === 'true';
      return acc;
    },
    {} as Record<FeatureFlag, boolean>
  );
};

export const getRegions = async (): Promise<Region[]> => {
  const data: { regions: Region[] } = await configServiceClient.get('regions?size=60');
  return data?.regions ?? [];
};
