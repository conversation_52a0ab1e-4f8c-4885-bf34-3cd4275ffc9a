import qs from 'qs';
import type {
  DailyEvent,
  DailyPredictionMap,
  HistoricalPrediction,
  HistoricalMetric,
  DailyPredictedSessionData,
  SessionPrediction,
  HourlyForecast,
  ForecastAnalog
} from '$lib/types';
import { predictionsServiceClient } from './http-client';
import { filterNonNull } from '$lib/utils';

export const getPSSessionId = async (
  type: 'daily' | 'daily_zone' | 'hourly_zone' | 'daily_parent_zone',
  targetDate?: string
): Promise<{
  latestDate: string;
  session: string;
}> => {
  const optionalParam = targetDate ? `&targetDate=${targetDate}T12:00:00Z` : '';
  return await predictionsServiceClient.get(
    `prediction/search/sessions/latest?type=${type}${optionalParam}`
  );
};

export const getSessionPredictions = async (
  id: string,
  parentZone?: string
): Promise<SessionPrediction[]> => {
  return await predictionsServiceClient.get(`prediction/search/session/${id}`, {
    params: { parentZone },
    paramsSerializer: (params) => qs.stringify(filterNonNull(params))
  });
};

export const getDailyPredictionMap = async (parentZone?: string): Promise<DailyPredictionMap> => {
  return await predictionsServiceClient.get(`prediction/daily`, {
    params: { parentZone },
    paramsSerializer: (params) => qs.stringify(filterNonNull(params))
  });
};

export const getDailyEvents = async (
  sessionId: string,
  isSystemic: boolean,
  parentZone?: string
): Promise<DailyEvent[]> => {
  return await predictionsServiceClient.get(`eventType/search/session/${sessionId}`, {
    params: { system: isSystemic, parentZone },
    paramsSerializer: (params) => qs.stringify(filterNonNull(params))
  });
};

export const getHistoricalPredictions = async (
  startDate: string,
  endDate: string,
  leadTime: number
): Promise<HistoricalPrediction> => {
  return await predictionsServiceClient.get(
    `predictions/historical?targetType=daily&startDate=${startDate}&endDate=${endDate}&leadTime=${
      leadTime === 0 ? '' : leadTime
    }`
  );
};

export const getHistoricalDonutChartData = async (
  startDate: string,
  endDate: string,
  leadTime: number
): Promise<HistoricalMetric> => {
  return await predictionsServiceClient.get(
    `predictions/historical/metrics?targetType=daily&startDate=${startDate}&endDate=${endDate}&leadTime=${
      leadTime === 0 ? '' : leadTime
    }`
  );
};

export const getDailyHourlyPredictionSummary = async (
  sessionId: string,
  parentZone?: string
): Promise<DailyPredictedSessionData> => {
  return await predictionsServiceClient.get(`prediction/daily/hourly-summary/${sessionId}`, {
    params: { parentZone },
    paramsSerializer: (params) => qs.stringify(filterNonNull(params))
  });
};

export const getHourlyPredictions = async (
  startHour: string,
  endHour: string,
  filteredRegions: string[]
): Promise<HourlyForecast> => {
  return await predictionsServiceClient.get('prediction/hourly', {
    params: { startHour, endHour, filteredRegions },
    paramsSerializer: (params) => qs.stringify(params, { arrayFormat: 'repeat' })
  });
};

export const getForecastAnalogs = async (
  parentZone: string = 'system'
): Promise<ForecastAnalog> => {
  return await predictionsServiceClient.get(
    `forecast-analogs/search/latestByZone?parentZone=${parentZone}`
  );
};
