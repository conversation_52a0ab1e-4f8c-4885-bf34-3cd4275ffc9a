import qs from 'qs';
import { weatherServiceClient } from './http-client';
import type { RegionalWeather } from '$lib/types';
import { filterNonNull } from '$lib/utils';

export const getWSSessionId = async (
  type: 'daily' | 'daily_zone' | 'daily_parent_zone',
  targetDate?: string
): Promise<string> => {
  const optionalParam = targetDate ? `&targetDate=${targetDate}T12:00:00Z` : '';
  return await weatherServiceClient.get(
    `weatherAttributes/sessions/latest?type=${type}${optionalParam}`
  );
};

export const getRegionalWeatherData = async (
  id: string,
  targetType: 'daily' | 'daily_zone' | 'daily_parent_zone',
  parentZone?: string
): Promise<RegionalWeather[]> => {
  return await weatherServiceClient.get(`weatherAttributes/sessions/${id}`, {
    params: { targetType, parentZone },
    paramsSerializer: (params) => qs.stringify(filterNonNull(params))
  });
};
