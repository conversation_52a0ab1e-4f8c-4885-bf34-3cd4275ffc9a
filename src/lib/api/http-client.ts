import { env } from '$env/dynamic/public';
import { getAuthToken, handleAuthError } from '$lib/authentication';
import axios from 'axios';
import type { AxiosInstance } from 'axios';

export const getBaseApiUrl = () => env.PUBLIC_API_GATEWAY_URL || `${env.PUBLIC_APP_URL}/api/`;

/** Function used to create an axios instance based on a base url */
function createAxiosClient(baseURL: string): AxiosInstance {
  const instance = axios.create({
    baseURL
  });

  instance.interceptors.response.use(
    (response) => response.data?._embedded ?? response.data,
    (error) => Promise.reject(error)
  );

  instance.interceptors.request.use(
    async (config) => {
      const token = await getAuthToken();
      config.headers['Authorization'] = `Bearer ${token}`;

      return config;
    },
    (error) => {
      return handleAuthError(error);
    }
  );

  return instance;
}

export const configServiceClient = createAxiosClient(`${getBaseApiUrl()}storm-config-service/`);

export const etrServiceClient = createAxiosClient(`${getBaseApiUrl()}storm-etr-service/`);

export const metricServiceClient = createAxiosClient(`${getBaseApiUrl()}storm-metric-service/`);

export const notificationServiceClient = createAxiosClient(
  `${getBaseApiUrl()}storm-notification-service/`
);

export const predictionsServiceClient = createAxiosClient(
  `${getBaseApiUrl()}storm-prediction-service/`
);

export const regionsServiceClient = createAxiosClient(`${getBaseApiUrl()}storm-regions-service/`);

export const shiftServiceClient = createAxiosClient(`${getBaseApiUrl()}storm-shift-service/`);

export const weatherServiceClient = createAxiosClient(
  `${getBaseApiUrl()}storm-weather-service-v2/`
);

export const eventServiceClient = createAxiosClient(`${getBaseApiUrl()}storm-event-service/`);

export const weatherLayerServiceClient = createAxiosClient(
  `${getBaseApiUrl()}storm-weather-layer-service/`
);
