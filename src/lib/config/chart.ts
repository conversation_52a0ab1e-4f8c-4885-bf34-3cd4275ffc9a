import { base } from '$app/paths';
import type { AgAxisLabelFormatterParams } from 'ag-charts-enterprise';
import { DateTime } from 'luxon';
import store from '$lib/stores/app.svelte';
import type {
  ChartSeries,
  LineChartTooltipParams,
  PieChartTooltipParams,
  Visibility
} from '$lib/types';
import {
  Components,
  EtrChartKeys,
  type CustomTooltipRendererParams,
  type SelectItem
} from '$lib/types';
import { formatNumbers } from '$lib/utils';

const CHART_GREENS = ['#beff00', '#ebdc78', '#32cd32', '#9beab6', '#038081', '#0a4c68'];
export const OUTAGES_STROKE_COLOR = '#7bb143';
export const ACTUALS_STROKE_COLOR = '#3c8bf1';

export const CHART_LINE_DASH_LENGTH = 6;
export const CHART_LINE_DASH_BREAK = 3;
export const CHART_INITIALIZING_TIMEOUT = 50;
export const DEFAULT_CHART_TOOLTIP_PRECISION = 3;
export function generateScale(items: string[]) {
  return items.sort().reduce((prev, curr, idx) => {
    return { ...prev, [curr]: CHART_GREENS[idx] };
  }, {});
}

export const LINECHART_X_AXIS_FORMAT_HOURLY = 'MM-dd ha';
export const LINECHART_X_AXIS_FORMAT_DAILY = 'MM-dd';
export const LINECHART_X_AXIS_FORMAT_MONTHLY = 'yyyy-MM-dd';

export const CHART_COLORS = [
  '#417a2b', // forest green
  '#1455a0', // dark blue
  '#cc6606', // deep orange
  '#a1161b', // crimson
  '#5e3c74', // dark purple
  '#495f65', // dark gray
  '#5c3f35', // cocoa brown
  '#bf6b75', // dusky pink
  '#128086', // deep teal
  '#cc7a36', // burnt orange
  '#7791b2', // muted blue
  '#689664', // sage green
  '#9c85a6', // grayish purple
  '#b85697', // magenta
  '#5a5a5a', // dark gray
  '#7a5b4d', // earthy brown
  '#65a0a8', // muted aqua
  '#c77e93', // soft muted pink
  '#b1a753' // olive gold
];

export const CHART_COLORS_LIGHT = [
  '#76c043', // bright green
  '#2a8ae6', // vibrant blue
  '#ffa24d', // vibrant orange
  '#e12c32', // bright red
  '#a37ee3', // bright purple
  '#7a8f96', // gray
  '#a6755e', // soft tan
  '#ff8ba0', // light pink
  '#34cad1', // bright teal
  '#ffb86a', // pastel orange
  '#b6d0f2', // light blue
  '#9fdc9f', // soft green
  '#d2b9dc', // pastel purple
  '#f78cca', // vibrant pink
  '#a1a1a1', // medium gray
  '#c6a28e', // light brown
  '#9fe4e8', // pale aqua
  '#f7c7d9', // pastel pink
  '#dfd97b' // pastel yellow
];

export function generateChartColorsScale(items: string[], isDark: boolean) {
  return items.sort().reduce((prev, curr, idx) => {
    return { ...prev, [curr]: isDark ? CHART_COLORS[idx] : CHART_COLORS_LIGHT[idx] };
  }, {});
}

export const PERCENTILE_SCALE_LIGHT = {
  '10th Percentile': '#8ec068',
  '50th Percentile': '#f7911d',
  '90th Percentile': '#d54040'
};

export const PERCENTILE_SCALE_DARK = {
  '10th Percentile': '#24521e',
  '50th Percentile': '#aa4409',
  '90th Percentile': '#750e13'
};

export const EVENT_TYPE_DONUT_CHART_COLOR_MAP = {
  'Damaged Pole': '#596fe1',
  Emergency: '#54c3fe',
  'Non Outage': '#3e94a6',
  Outage: '#104861',
  'Service Hazard Wire Down': '#1b7b6c'
};

export const STORM_CROSS_LINE_SERIES_ITEM = {
  lineDash: [2, 2],
  marker: { fill: '#b30900', shape: 'diamond' },
  stroke: '#b30900',
  type: 'line',
  xKey: 'date',
  yKey: 'storm',
  yName: 'Storm'
};

export const TIME_SERIES_COLOR = '#b2beb5';

export const CURRENT_TIME_CROSS_LINE_SERIES_ITEM = {
  lineDash: [2, 2],
  marker: { fill: TIME_SERIES_COLOR, shape: 'diamond' },
  stroke: TIME_SERIES_COLOR,
  type: 'line',
  xKey: 'date',
  yKey: 'currentTime',
  yName: 'Current Time'
};

export const CROSS_AXIS_COLOUR = '#ff7c43';

export const SECONDARY_CROSS_AXIS_COLOR = '#007d79';

export const DEFAULT_HISTORICAL_WORKER_ITEMS = [
  { id: EtrChartKeys.hours, text: 'Hours to Restoration', checked: true },
  { id: EtrChartKeys.totalWorkers, text: 'Workers', checked: true }
];

export const HOURS_TO_RESTORATION_LINE_SERIES_COLORS = {
  hours: '#3c8bf1',
  workers: CROSS_AXIS_COLOUR,
  hourlyRestoration: SECONDARY_CROSS_AXIS_COLOR
};

export function tooltipRenderer(
  params: LineChartTooltipParams,
  precision = DEFAULT_CHART_TOOLTIP_PRECISION,
  unit: string = '',
  color: string = params.color
) {
  const formattedDate = DateTime.fromJSDate(params.datum[params.xKey]).toFormat('MMM d - h:mma');
  return `
    <div class="ag-chart-tooltip-title" style="background-color: ${color}">
      ${params.title}
    </div>
    <div class="ag-chart-tooltip-content">
      ${formattedDate}: ${
        precision === 0 && params.datum[params.yKey] > 0 && params.datum[params.yKey] < 1
          ? '<1'
          : formatNumbers(params.datum[params.yKey], precision)
      } ${unit}
    </div>`;
}
function hoursToRestorationTooltip(params: LineChartTooltipParams) {
  const formattedDate = DateTime.fromJSDate(params.datum[params.xKey]).toFormat('MMM d - h:mma');

  return `
    <div class="ag-chart-tooltip-title" style="background-color: ${params.color}">
      ${params.title}
    </div>
    <div class="ag-chart-tooltip-content">
      <div class="border-b pb-2 border-solid">${formattedDate}: ${formatNumbers(params.datum[params.yKey], DEFAULT_CHART_TOOLTIP_PRECISION)}h</div>
      <a href="${base}/etr/dashboard?sessionId=${params.datum.sessionId}&stormId=${params.datum.stormId}"
       class="text-blue-500 underline text-center block w-full my-2">View Historical Session</a>
    </div>`;
}

function tooltipRendererWorkers(params: LineChartTooltipParams) {
  const { resources } = params.datum;
  const formattedDate = DateTime.fromJSDate(params.datum[params.xKey]).toFormat('MMM d - h:mma');
  const { validations = [] } = store.componentsConfig?.[Components.RESOURCE_VALIDATION] || {};
  const { columns = [] } = store.componentsConfig?.[Components.RESOURCE_TABLE] || {};

  let resourceContent = '';

  if (validations.length) {
    resourceContent = '<hr>';
    validations.forEach((attr) => {
      const col = columns.find((c) => c.field === attr);
      if (col) {
        const spanRow = `<span class="block">${col?.headerName}: ${resources?.[attr]}</span>`;
        resourceContent = `${resourceContent}${spanRow}`;
      }
    });
  }

  return `
    <div class="ag-chart-tooltip-title" style="background-color: ${
      HOURS_TO_RESTORATION_LINE_SERIES_COLORS.workers
    }">
      ${params.title}
    </div>
    <div class="ag-chart-tooltip-content">
      <span>${formattedDate}</span>
      <hr>
      <h6 class="worker-count font-semibold">Total: ${params.datum[params.yKey]}</h6>
      ${resourceContent}
    </div>`;
}

export function getStormCrossLineSeriesItem(yName: string) {
  return { ...STORM_CROSS_LINE_SERIES_ITEM, yName };
}

export function getActiveOutagesChartSeriesItems(items: SelectItem[]) {
  const series: ChartSeries[] = [];
  const options = {
    tooltip: { renderer: tooltipRenderer },
    type: 'line',
    xKey: 'date'
  };
  const chartSettingsMap = {
    [EtrChartKeys.pastOutageCount]: {
      ...options,
      marker: { fill: '#3c8bf1', stroke: '#3c8bf1' },
      stroke: '#3c8bf1',
      yKey: EtrChartKeys.pastOutageCount
    },
    [EtrChartKeys.cumulativeIncidents]: {
      ...options,
      marker: { fill: '#d12771', stroke: '#d12771' },
      stroke: '#d12771',
      yKey: EtrChartKeys.cumulativeIncidents
    },
    [EtrChartKeys.cumulativeRestorations]: {
      ...options,
      marker: { fill: '#8a3ffc', stroke: '#8a3ffc' },
      stroke: '#8a3ffc',
      yKey: EtrChartKeys.cumulativeRestorations
    },
    [EtrChartKeys.cumulativeRestoredOutage]: {
      ...options,
      marker: { fill: '#8a3ffc', stroke: '#8a3ffc' },
      stroke: '#8a3ffc',
      yKey: EtrChartKeys.cumulativeRestoredOutage
    },
    [EtrChartKeys.outageCount]: {
      ...options,
      marker: { fill: '#3cb053', stroke: '#3cb053' },
      stroke: '#3cb053',
      yKey: EtrChartKeys.outageCount
    },
    [EtrChartKeys.cumulativeAffectedCustomers]: {
      ...options,
      marker: { fill: '#f59d38', stroke: '#f59d38' },
      stroke: '#f59d38',
      yKey: EtrChartKeys.cumulativeAffectedCustomers
    },
    [EtrChartKeys.cumulativeCustomersRestored]: {
      ...options,
      marker: { fill: '#f56505', stroke: '#f56505' },
      stroke: '#f56505',
      yKey: EtrChartKeys.cumulativeCustomersRestored
    }
  };
  items.forEach((item) =>
    series.push({ ...chartSettingsMap[item.id as keyof typeof chartSettingsMap], yName: item.text })
  );
  return series;
}

export function getPastOutagesOverviewChartSeriesItems(items: SelectItem[]) {
  const series: ChartSeries[] = [];
  const options = { tooltip: { renderer: tooltipRenderer }, xKey: 'date', type: 'line' };
  const chartSettingsMap = {
    [EtrChartKeys.outageCount]: {
      ...options,
      marker: { fill: '#3c8bf1', stroke: '#3c8bf1' },
      stroke: '#3c8bf1',
      yKey: EtrChartKeys.outageCount
    },
    [EtrChartKeys.restorationCount]: {
      ...options,
      fill: '#3cb053',
      type: 'bar',
      yKey: EtrChartKeys.restorationCount
    },
    [EtrChartKeys.cumulativeIncidents]: {
      ...options,
      marker: { fill: '#d12771', stroke: '#d12771' },
      stroke: '#d12771',
      yKey: EtrChartKeys.cumulativeIncidents
    },
    [EtrChartKeys.cumulativeRestorations]: {
      ...options,
      marker: { fill: '#8a3ffc', stroke: '#8a3ffc' },
      stroke: '#8a3ffc',
      yKey: EtrChartKeys.cumulativeRestorations
    },
    [EtrChartKeys.cumulativeAffectedCustomers]: {
      ...options,
      marker: { fill: '#f59d38', stroke: '#f59d38' },
      stroke: '#f59d38',
      yKey: EtrChartKeys.cumulativeAffectedCustomers
    },
    [EtrChartKeys.cumulativeCustomersRestored]: {
      ...options,
      marker: { fill: '#f56505', stroke: '#f56505' },
      stroke: '#f56505',
      yKey: EtrChartKeys.cumulativeCustomersRestored
    }
  };
  items.forEach((item) =>
    series.push({ ...chartSettingsMap[item.id as keyof typeof chartSettingsMap], yName: item.text })
  );
  return series;
}

export function getWorkerChartSeriesItems(items: SelectItem[]) {
  const series: ChartSeries[] = [];
  const options = {
    tooltip: { renderer: tooltipRenderer },
    type: 'bar',
    xKey: 'date',
    nodeClickRange: 'nearest'
  };
  const chartSettingsMap = {
    [EtrChartKeys.newOutageCount]: {
      ...options,
      fill: '#3c8bf1',
      yKey: EtrChartKeys.newOutageCount
    },
    [EtrChartKeys.uniqueIncidentsCount]: {
      ...options,
      fill: '#3c8bf1',
      yKey: EtrChartKeys.uniqueIncidentsCount
    },
    [EtrChartKeys.restOutageCount]: {
      ...options,
      fill: '#d4b122',
      yKey: EtrChartKeys.restOutageCount
    },
    [EtrChartKeys.projRestOutageCount]: {
      ...options,
      fill: '#d12771',
      yKey: EtrChartKeys.projRestOutageCount
    },
    [EtrChartKeys.assessedOutageCount]: {
      ...options,
      fill: '#3cb053',
      yKey: EtrChartKeys.assessedOutageCount
    },
    [EtrChartKeys.totalWorkers]: {
      ...options,
      tooltip: { renderer: tooltipRendererWorkers },
      yKey: EtrChartKeys.totalWorkers,
      marker: { fill: '#f56505', stroke: '#f56505' },
      stroke: '#f56505',
      type: 'line'
    },
    [EtrChartKeys.restoredPerWorker]: {
      ...options,
      marker: { fill: '#007d79', stroke: '#007d79' },
      stroke: '#007d79',
      yKey: EtrChartKeys.restoredPerWorker,
      type: 'line'
    }
  };

  items.forEach((item) =>
    series.push({ ...chartSettingsMap[item.id as keyof typeof chartSettingsMap], yName: item.text })
  );

  return series;
}

export function getHistoricalWorkerChartSeriesItems(items: SelectItem[]) {
  const series: ChartSeries[] = [];
  const options = {
    tooltip: { renderer: tooltipRenderer },
    type: 'line',
    xKey: 'date'
  };
  const chartSettingsMap = {
    [EtrChartKeys.hours]: {
      ...options,
      marker: {
        fill: HOURS_TO_RESTORATION_LINE_SERIES_COLORS.hours,
        stroke: HOURS_TO_RESTORATION_LINE_SERIES_COLORS.hours
      },
      yKey: EtrChartKeys.hours,
      yName: 'Hours to Restoration',
      stroke: HOURS_TO_RESTORATION_LINE_SERIES_COLORS.hours,
      tooltip: {
        renderer: hoursToRestorationTooltip,
        interaction: {
          enabled: true
        }
      }
    },
    [EtrChartKeys.totalWorkers]: {
      ...options,
      marker: {
        fill: HOURS_TO_RESTORATION_LINE_SERIES_COLORS.workers,
        stroke: HOURS_TO_RESTORATION_LINE_SERIES_COLORS.workers
      },
      tooltip: { renderer: tooltipRendererWorkers },
      yKey: EtrChartKeys.totalWorkers,
      yName: 'Workers',
      stroke: HOURS_TO_RESTORATION_LINE_SERIES_COLORS.workers
    },
    [EtrChartKeys.restoredPerWorker]: {
      ...options,
      marker: {
        fill: HOURS_TO_RESTORATION_LINE_SERIES_COLORS.hourlyRestoration,
        stroke: HOURS_TO_RESTORATION_LINE_SERIES_COLORS.hourlyRestoration
      },
      yKey: EtrChartKeys.restoredPerWorker,
      yName: 'Restoration per Worker',
      stroke: HOURS_TO_RESTORATION_LINE_SERIES_COLORS.hourlyRestoration
    }
  };

  items.forEach((item) => series.push(chartSettingsMap[item.id as keyof typeof chartSettingsMap]));
  return series;
}

export function pieChartTooltipRenderer(
  params: PieChartTooltipParams,
  setActiveMapRegion: boolean = true,
  layerValue: Visibility | undefined = undefined
) {
  // Set the active map region here as there is no way to get onMouseOver event from AgCharts
  const key = `${params.datum[params.calloutLabelKey]}-${params.angleKey}-${
    params.datum[params.angleKey]
  }`;

  const layer = layerValue ?? (params.angleKey as Visibility);

  if (setActiveMapRegion && store.activeMapRegion?.key !== key) {
    store.activeMapRegion = {
      key,
      region: params.datum[params.calloutLabelKey],
      layer,
      value: params.datum[params.angleKey]
    };
  }

  return `
    <div class="ag-chart-tooltip-title" style="background-color: ${params.color}">
      ${params.datum[params.calloutLabelKey]}
    </div>
    <div class="ag-chart-tooltip-content">
      ${formatNumbers(params.datum[params.angleKey])}
    </div>`;
}

export const customFormatter = (params: AgAxisLabelFormatterParams, format: string) => {
  return DateTime.fromJSDate(params.value).toFormat(format);
};

export const customHourFormatter = (params: AgAxisLabelFormatterParams) => {
  const date = DateTime.fromJSDate(params.value);
  if (date.minute === 0) {
    return date.toFormat('ha');
  } else {
    return date.toFormat('h:mma');
  }
};

export function forecastTooltipRenderer(params: CustomTooltipRendererParams) {
  const min = params.datum.min.toFixed(1);
  const max = params.datum.max.toFixed(1);
  return `
    <div class="ag-chart-tooltip-title" style="background-color: ${params.color}">
      ${params.datum[params.xKey]} ${params.datum[params.xKey] === 1 ? 'Hour' : 'Hours'}
    </div>
    <div class="ag-chart-tooltip-content">
      Forecasted ${store.capitalizedOutagesLabel}: ${min !== max ? `${min} - ${max}` : min}
    </div>`;
}

export function mobileViewTooltipRenderer(
  params: CustomTooltipRendererParams,
  selectedDay: string | null
) {
  if (!selectedDay) {
    return '';
  }

  const hour = params.datum[params.xKey];
  const dateTime = DateTime.fromFormat(selectedDay, 'yyyy-MM-dd')
    .set({
      hour
    })
    .toFormat('MMM d - h:mma');

  return `
    <div class="ag-chart-tooltip-title" style="background-color: ${params.color}">
      ${params.title}
    </div>
    <div class="ag-chart-tooltip-content">
      ${dateTime}: ${formatNumbers(params.datum[params.yKey])}
    </div>`;
}
