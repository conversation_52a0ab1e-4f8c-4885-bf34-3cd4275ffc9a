import { DateTime } from 'luxon';
import store from '$lib/stores/app.svelte';
import type { EventLevel, RegionalWeather, HourlyWeatherAttributes } from '$lib/types';
import { getHeatmapColors } from './map';
import { useMilitaryTime } from '$lib/stores/constants';

export function getOutageColor(value: number) {
  const colors = getHeatmapColors();
  if (value === 0) return '#fff';
  if (value < 50) return colors[0];
  if (value < 100) return colors[1];
  if (value < 150) return colors[2];
  if (value < 200) return colors[3];
  if (value >= 200) return colors[4];
}

export function formatDateTime(date: string | number | Date | null, showTimeZone: boolean = false) {
  if (!date) return '-';

  const militaryTime = useMilitaryTime;
  return DateTime.fromJSDate(new Date(date)).toFormat(
    militaryTime
      ? `yyyy-MM-dd, HH:mm ${showTimeZone ? 'ZZZZ' : ''}`
      : `yyyy-MM-dd, h:mma ${showTimeZone ? 'ZZZZ' : ''}`
  );
}

export function formatDate(date: string | number | Date | null) {
  if (!date) return '-';

  return DateTime.fromJSDate(new Date(date)).toFormat('yyyy-MM-dd');
}

export function formatTime(date: string | number | Date | null, showTimeZone: boolean = false) {
  if (!date) return '-';
  const militaryTime = useMilitaryTime;
  return DateTime.fromJSDate(new Date(date)).toFormat(
    militaryTime ? `HH:mm ${showTimeZone ? 'ZZZZ' : ''}` : `h:mma ${showTimeZone ? 'ZZZZ' : ''}`
  );
}

export function formatAsOfTimestamp(date: string | number | Date | null) {
  if (!date) return '-';

  const militaryTime = useMilitaryTime;
  return DateTime.fromJSDate(new Date(date)).toFormat(
    militaryTime ? 'MMMM dd, HH:mm ZZZZ' : 'MMMM dd, h:mma ZZZZ'
  );
}

export function findEventLevel(
  outageCount: number | undefined,
  viewLevel: 'system' | 'pd-area' | 'workgroup' = 'system',
  regionName: string = ''
): EventLevel {
  const eventLevelsData = store.eventLevels;

  if (outageCount) {
    const count = Math.round(outageCount);

    return (
      eventLevelsData.find(({ range, view, name }) => {
        if (viewLevel === 'system') {
          return range?.length && view === 'system' && count >= range[0] && count <= range[1];
        }

        return (
          range?.length &&
          count >= range[0] &&
          count <= range[1] &&
          name === regionName &&
          viewLevel === view
        );
      }) || eventLevelsData.find(({ view }) => view === viewLevel)!
    );
  }

  return eventLevelsData[0];
}

export function findMinEventLevelThreshold(
  viewLevel: 'system' | 'pd-area' = 'system',
  divideBy: number = 1,
  regionName: string = ''
): number {
  const eventLevelsData = store.eventLevels;
  const eventLevel = eventLevelsData?.find(({ view, name }) => {
    if (viewLevel === 'system') {
      return view === 'system';
    }

    return view === 'pd-area' && name === regionName;
  });

  if (!eventLevel?.range?.[1]) return 1;

  return eventLevel.range[1] / divideBy;
}

const MIN_THREAT_LEVEL = 3;
const MIN_SUN_THRESHOLD = 25;
const MIN_CLOUD_THRESHOLD = 75;
const MIN_PRECIP_THRESHOLD = 0.1;
const WIND_GUST_THRESHOLD = 30;
const AVG_WIND_THRESHOLD = 10;

export function getHourlyWeatherIcon(weather: HourlyWeatherAttributes | undefined) {
  if (!weather) {
    return { type: 'unknown', description: 'No weather data available' };
  }

  const {
    threatLevelMax,
    totalPrecip,
    cloudCoverPercentMean,
    windGustMaxHigh,
    averageWindSustained
  } = weather ?? {};

  if (threatLevelMax >= MIN_THREAT_LEVEL) {
    return { type: 'storm', description: 'Severe weather or storm conditions' };
  }

  if (totalPrecip > MIN_PRECIP_THRESHOLD) {
    if (cloudCoverPercentMean <= MIN_CLOUD_THRESHOLD) {
      return { type: 'rain-sun', description: 'Rain showers with some sunshine' };
    } else {
      return { type: 'rain', description: 'Rainy and overcast' };
    }
  }

  if (windGustMaxHigh >= WIND_GUST_THRESHOLD && averageWindSustained >= AVG_WIND_THRESHOLD) {
    return { type: 'wind', description: 'Windy conditions with strong gusts' };
  }

  if (totalPrecip <= MIN_PRECIP_THRESHOLD) {
    if (cloudCoverPercentMean < MIN_SUN_THRESHOLD) {
      return { type: 'sun', description: 'Clear skies and sunny' };
    } else if (cloudCoverPercentMean <= MIN_CLOUD_THRESHOLD) {
      return { type: 'cloudy-sun', description: 'Partly cloudy with some sunshine' };
    } else {
      return { type: 'cloudy', description: 'Overcast and cloudy' };
    }
  }

  return { type: 'unknown', description: 'Weather conditions unknown' };
}

export function getDailyWeatherIcon(weatherData: RegionalWeather, threatLevel: number) {
  const { precip, cloudCoverPercentMean, windGustMax, windSustained } =
    weatherData.weatherAttributes ?? {};

  if (threatLevel >= MIN_THREAT_LEVEL) {
    return { type: 'storm', description: 'Severe weather or storm conditions' };
  }

  if (precip > MIN_PRECIP_THRESHOLD) {
    if (cloudCoverPercentMean <= MIN_CLOUD_THRESHOLD) {
      return { type: 'rain-sun', description: 'Rain showers with some sunshine' };
    } else {
      return { type: 'rain', description: 'Rainy and overcast' };
    }
  }

  if (windGustMax >= WIND_GUST_THRESHOLD && windSustained >= AVG_WIND_THRESHOLD) {
    return { type: 'wind', description: 'Windy conditions with strong gusts' };
  }

  if (precip <= MIN_PRECIP_THRESHOLD) {
    if (cloudCoverPercentMean < MIN_SUN_THRESHOLD) {
      return { type: 'sun', description: 'Clear skies and sunny' };
    } else if (cloudCoverPercentMean <= MIN_CLOUD_THRESHOLD) {
      return { type: 'cloudy-sun', description: 'Partly cloudy with some sunshine' };
    } else {
      return { type: 'cloudy', description: 'Overcast and cloudy' };
    }
  }

  return { type: 'unknown', description: 'Weather conditions unknown' };
}

export function getThreatLevelCrossLines(
  regionName: string,
  viewLevel: string,
  isDarkMode: boolean
) {
  return store.eventLevels
    .filter((lvl) =>
      regionName === 'system'
        ? lvl.view === 'system'
        : lvl.view === viewLevel && lvl.name === regionName
    )
    ?.filter((lvl) => lvl.range)
    .map((lvl) => ({
      type: 'range',
      range: [lvl.range[0], lvl.range[1] + 1], // Extend upper bound by 1 so that threat lines don't have a space between them
      strokeWidth: 0,
      fill: lvl.color?.[isDarkMode ? 'dark' : 'light'],
      fillOpacity: 0.4
    }));
}
