import { formatDate, formatDateTime, formatTime } from '$lib/config/utils';
import { formatNumbers, formatDuration } from '$lib/utils';
import type {
  CellClassParams,
  ColDef,
  GridOptions,
  HeaderValueGetterParams,
  ICellRendererParams,
  ValueFormatterParams,
  ValueGetterParams
} from 'ag-grid-enterprise';
import { DateTime } from 'luxon';
import store from '$lib/stores/app.svelte';
import { client, isSystemic, showHistoricalStormModal, timezoneAbbr } from '$lib/stores/constants';
import {
  Components,
  type ActiveCrew,
  type ColumnConfig,
  type FutureResourcesData,
  type IColDef,
  type UnifiedEtrSimData
} from '$lib/types';
import { AccountRoles, getUserRoles } from '$lib/authentication';

const COLUMNS_TYPE_CONFIG: Record<string, ColumnConfig> = {
  number: {
    minWidth: 112,
    cellEditor: 'agNumberCellEditor',
    cellRenderer: ({ value }: ICellRendererParams) => {
      return `${value === null ? '-' : value}`;
    },
    valueFormatter: ({ value }: ValueFormatterParams) => formatNumbers(value),
    cellClass: ({ data, colDef }: CellClassParams) =>
      `cursor-pointer hover:underline ${data.inPending?.[colDef.field!] ? 'text-warning' : ''}`,
    cellEditorParams: { min: 0, precision: 0, step: 1 }
  },
  time: {
    minWidth: 140,
    valueFormatter: ({ value }: ValueFormatterParams) => (value ? formatDuration(value) : '-')
  },
  datetime: {
    minWidth: 175,
    cellEditor: 'agDateStringCellEditor',
    valueFormatter: ({ value }: ValueFormatterParams) => formatDateTime(value),
    headerValueGetter: ({ colDef }: HeaderValueGetterParams) => {
      return `${colDef.headerName} (${timezoneAbbr})`;
    }
  },
  date: {
    minWidth: 120,
    cellEditor: 'agDateStringCellEditor',
    cellEditorParams: { min: formatDate(new Date()) },
    valueFormatter: ({ value }: ValueFormatterParams) => formatDate(value)
  },
  string: {
    minWidth: 125
  }
};

export function getResourcesFields() {
  const resourceFields: string[] =
    store.componentsConfig?.[Components.RESOURCE_VALIDATION]?.validations || [];

  return resourceFields;
}

export const DEFAULT_GRID_OPTIONS: GridOptions = {
  defaultColDef: {
    autoHeaderHeight: true,
    flex: 1,
    singleClickEdit: true,
    sortable: true,
    wrapHeaderText: true,
    suppressMenu: true,
    cellStyle: { display: 'flex', alignItems: 'center' }
  },
  sideBar: {
    toolPanels: [
      {
        id: 'columns',
        labelDefault: 'Columns',
        labelKey: 'columns',
        iconKey: 'columns',
        toolPanel: 'agColumnsToolPanel',
        toolPanelParams: {
          suppressRowGroups: true,
          suppressValues: true,
          suppressPivots: true,
          suppressPivotMode: true
        }
      }
    ]
  },
  getRowId: (params) => params.data.id,
  suppressDragLeaveHidesColumns: true,
  tooltipShowDelay: 250
};

export const getFormattedCols = (cols: ColumnConfig[]): IColDef[] => {
  return cols.map((d) => {
    const colTypeConfig = COLUMNS_TYPE_CONFIG[d.propertyType!];

    return {
      minWidth: colTypeConfig.minWidth,
      ...d,
      flex: 1,
      wrapHeaderText: true,
      autoHeaderHeight: true,
      headerTooltip: d.tooltip,
      ...(colTypeConfig.valueFormatter && {
        valueFormatter: colTypeConfig.valueFormatter
      }),
      ...(d.editable && colTypeConfig.cellEditor && { cellEditor: colTypeConfig.cellEditor }),
      ...(d.editable &&
        colTypeConfig.cellEditorParams && {
          cellEditorParams: colTypeConfig.cellEditorParams
        }),
      ...(colTypeConfig.headerValueGetter && {
        headerValueGetter: colTypeConfig.headerValueGetter
      })
    };
  });
};

export const getHistoricalColumns = (
  showRemoved: boolean,
  onStormToUpdateSelect: (id: string) => void,
  onClickSimilarStorm: (id: string) => void,
  onStatsClick?: (id: string) => void,
  showCheckbox?: boolean
): ColDef[] => {
  window.selectHistoricalStormToUpdate = onStormToUpdateSelect;
  if (showHistoricalStormModal && onStatsClick) {
    window.selectHistoricalStorm = onStatsClick;
  }
  const cols: ColumnConfig[] =
    store.componentsConfig?.[Components.ETR_HISTORICAL_TABLE]?.columns || [];
  const formattedCols = showHistoricalStormModal
    ? [
        {
          cellRenderer: ({ data }: ICellRendererParams) =>
            `<span class="material-icons text-primary mt-1" onclick="event.stopPropagation(); selectHistoricalStorm('${data.id}')">query_stats</span>`,
          maxWidth: 60,
          minWidth: 60,
          preventExport: true
        },
        ...getFormattedCols(cols)
      ]
    : getFormattedCols(cols);

  if (showCheckbox) {
    formattedCols.unshift({
      checkboxSelection: true,
      maxWidth: 50,
      minWidth: 50
    });
  }

  formattedCols.push({
    cellRenderer: ({ data }: ICellRendererParams) => `
      <span
        class="material-icons ${showRemoved ? 'settings_backup_restore' : 'close'}"
        onclick="selectHistoricalStormToUpdate('${data.id}')"
      >
        ${showRemoved ? 'settings_backup_restore' : 'close'}
      </span>
    `,
    preventExport: true,
    sortable: false,
    maxWidth: 75,
    minWidth: 75
  });
  return formattedCols.map((col) => {
    if (col.field === 'displayName') {
      col.minWidth = 330;
      col.editable = true;
      col.cellRenderer = ({ value }: ICellRendererParams) =>
        `${value}<span class="material-icons edit">edit</span>`;
      col.valueSetter = (params) => {
        if (params.newValue) {
          params.data.displayName = params.newValue;
          return true;
        }
        return false;
      };
      col.valueFormatter = (row) =>
        row?.data?.displayName === row?.data?.stormName
          ? formatDate(row?.data?.displayName)
          : row.data.displayName;
    }

    if (col.field === 'similarStorms') {
      col.valueFormatter = ({ value }) =>
        value.map(({ displayName }: { displayName: string }) => displayName);

      col.cellRenderer = ({ data, value }: ICellRendererParams) => {
        const container = document.createElement('div');
        container.className = 'flex items-center gap-2 !no-underline';

        container.onclick = (event) => {
          event.stopPropagation();
          onClickSimilarStorm(data.id);
        };

        const icon = document.createElement('span');
        icon.className = 'material-icons text-primary cursor-pointer';
        icon.textContent = 'query_stats';

        const countText = document.createElement('span');
        countText.textContent = `${value.length} ${value.length === 1 ? 'storm' : 'storms'}`;

        container.appendChild(icon);
        container.appendChild(countText);

        return container;
      };
    }

    if (col.propertyType === 'datetime') {
      col.minWidth = 180;
    }

    if (col.propertyType === 'time') {
      col.minWidth = 225;
    }

    return col;
  });
};

export function getDefaultResourcesColumns(): ColDef[] {
  const cols: ColumnConfig[] = store.componentsConfig?.[Components.RESOURCE_TABLE]?.columns || [];
  const resourceFields: string[] =
    store.componentsConfig?.[Components.RESOURCE_VALIDATION]?.validations || [];

  return getFormattedCols(cols.slice(0, -1)).map((col) => ({
    ...col,
    editable: ({ node, colDef: col }) => {
      const isEditableField = resourceFields.includes(col.field ?? '');

      if (node.isRowPinned() || !isEditableField) {
        return false;
      }

      if (client === 'dlc') {
        return getUserRoles().includes(AccountRoles.STORM_RESOURCE_MANAGER);
      }

      return true;
    },
    cellStyle: (params: any) => {
      if (params.colDef.field === 'territoryName') {
        return null;
      }

      const value = Number(params.value);
      return isNaN(value) || value < 0 ? { border: '1px solid red' } : { border: 0 };
    }
  }));
}

export function getResourcesColumns(): ColDef[] {
  const cols: ColumnConfig[] = store.componentsConfig?.[Components.RESOURCE_TABLE]?.columns || [];
  const resourceFields: string[] =
    store.componentsConfig?.[Components.RESOURCE_VALIDATION]?.validations || [];

  return getFormattedCols(cols).map((col) => ({
    ...col,
    editable: resourceFields.includes(col.field!) && isSystemic
  }));
}

export function getCurrentResourcesColumns(isReadOnly: boolean): ColDef[] {
  const cols: ColumnConfig[] =
    store.componentsConfig?.[Components.CURRENT_RESOURCE_TABLE]?.columns || [];
  // Omit last two column definitions if isReadOnly=true
  return getFormattedCols(isReadOnly ? cols.slice(0, -2) : cols).map((col) => {
    if (col.field === 'resourcesNeeded') {
      col.cellClass = 'highlight';
      col.headerClass = 'highlight';
    }
    if (col.field === 'delta') {
      col.cellStyle = ({ value }) => {
        if (value) {
          if (0 < value) return { backgroundColor: 'rgb(0, 163, 108, 0.5)' };
          if (value < 0) return { backgroundColor: 'rgb(136, 8, 8, 0.7)' };
        }
        return { backgroundColor: 'inherit' };
      };
    }
    return col;
  });
}

export function getFutureResourcesColumns(): ColDef[] {
  const cols: ColumnConfig[] =
    store.componentsConfig?.[Components.FUTURE_RESOURCE_TABLE]?.columns || [];

  return [
    ...getFormattedCols(cols),
    {
      cellEditor: 'agDateStringCellEditor',
      headerName: 'ETA Date',
      cellEditorParams: { min: formatDate(new Date()) },
      editable: true,
      valueGetter: ({ data }) => {
        return formatDate(data.arrivalTime);
      },
      valueSetter: (params) => {
        const date = DateTime.fromISO(params.data.arrivalTime);
        const day = DateTime.fromFormat(params.newValue, 'yyyy-MM-dd').get('day');
        const month = DateTime.fromFormat(params.newValue, 'yyyy-MM-dd').get('month');
        const year = DateTime.fromFormat(params.newValue, 'yyyy-MM-dd').get('year');
        const updated = date.set({ year, month, day });

        params.data.prevArrivalTime = params.data.prevArrivalTime || params.data.arrivalTime;
        params.data.arrivalTime = updated.toFormat(`yyyy-MM-dd'T'HH:mm:ssZZZ`);

        return true;
      },
      field: 'arrivalTime',
      minWidth: 120
    },
    {
      editable: true,
      field: 'arrivalTime',
      headerName: 'ETA Time',
      minWidth: 100,
      valueGetter: ({ data }) => {
        return DateTime.fromISO(data.arrivalTime).toFormat('hh:mm');
      },
      valueSetter: (params) => {
        const date = DateTime.fromISO(params.data.arrivalTime);
        const hour = DateTime.fromFormat(params.newValue, 'hh:mm').get('hour');
        const minute = DateTime.fromFormat(params.newValue, 'hh:mm').get('minute');
        const isAfternoon = date.get('hour') > 12;
        const updated = date.set({ hour: isAfternoon ? hour + 12 : hour, minute });

        params.data.prevArrivalTime = params.data.prevArrivalTime || params.data.arrivalTime;
        params.data.arrivalTime = updated.toFormat(`yyyy-MM-dd'T'HH:mm:ssZZZ`);

        return true;
      }
    },
    {
      editable: true,
      field: 'arrivalTime',
      headerName: 'ETA AM/PM',
      cellEditor: 'agSelectCellEditor',
      cellEditorParams: {
        values: ['AM', 'PM']
      },
      minWidth: 80,
      valueGetter: ({ data }) => {
        return DateTime.fromISO(data.arrivalTime).toFormat('a');
      },
      valueSetter: (params) => {
        const date = DateTime.fromISO(params.data.arrivalTime);
        const hour = DateTime.fromISO(params.data.arrivalTime).get('hour');
        const isAfternoon = params.newValue === 'PM';
        const updated = date.set({ hour: isAfternoon ? hour + 12 : hour - 12 });

        params.data.prevArrivalTime = params.data.prevArrivalTime || params.data.arrivalTime;
        params.data.arrivalTime = updated.toFormat(`yyyy-MM-dd'T'HH:mm:ssZZZ`);

        return true;
      }
    }
  ];
}

export function getPendingResources(): ColDef[] {
  const cols: ColumnConfig[] =
    store.componentsConfig?.[Components.PENDING_RESOURCE_OPERATION_TABLE]?.columns || [];

  return [
    ...getFormattedCols(cols),
    {
      cellEditor: 'agDateStringCellEditor',
      headerName: 'ETA Date',
      cellEditorParams: { min: formatDate(new Date()) },
      editable: false,
      valueGetter: ({ data }) => {
        return formatDate(data.arrivalTime);
      },
      valueSetter: (params) => {
        const arrivalTime = DateTime.fromISO(params.data.arrivalTime);
        const newDate = DateTime.fromFormat(params.newValue, 'yyyy-MM-dd');
        const updated = arrivalTime.set({
          year: newDate.get('year'),
          month: newDate.get('month'),
          day: newDate.get('day')
        });

        params.data.prevArrivalTime = params.data.prevArrivalTime || params.data.arrivalTime;
        params.data.arrivalTime = updated.toFormat(`yyyy-MM-dd'T'HH:mm:ssZZZ`);

        return true;
      },
      field: 'arrivalTime',
      minWidth: 120
    },
    {
      editable: false,
      field: 'arrivalTime',
      headerName: 'ETA Time',
      minWidth: 100,
      valueGetter: ({ data }) => {
        return DateTime.fromISO(data.arrivalTime).toFormat('hh:mm');
      },
      valueSetter: (params) => {
        const date = DateTime.fromISO(params.data.arrivalTime);
        const hour = DateTime.fromFormat(params.newValue, 'hh:mm').get('hour');
        const minute = DateTime.fromFormat(params.newValue, 'hh:mm').get('minute');
        const isAfternoon = date.get('hour') > 12;
        const updated = date.set({ hour: isAfternoon ? hour + 12 : hour, minute });

        params.data.prevArrivalTime = params.data.prevArrivalTime || params.data.arrivalTime;
        params.data.arrivalTime = updated.toFormat(`yyyy-MM-dd'T'HH:mm:ssZZZ`);

        return true;
      }
    },
    {
      editable: false,
      field: 'arrivalTime',
      headerName: 'ETA AM/PM',
      cellEditor: 'agSelectCellEditor',
      cellEditorParams: {
        values: ['AM', 'PM']
      },
      minWidth: 80,
      valueGetter: ({ data }) => {
        return DateTime.fromISO(data.arrivalTime).toFormat('a');
      },
      valueSetter: (params) => {
        const date = DateTime.fromISO(params.data.arrivalTime);
        const hour = DateTime.fromISO(params.data.arrivalTime).get('hour');
        const isAfternoon = params.newValue === 'PM';
        const updated = date.set({ hour: isAfternoon ? hour + 12 : hour - 12 });

        params.data.prevArrivalTime = params.data.prevArrivalTime || params.data.arrivalTime;
        params.data.arrivalTime = updated.toFormat(`yyyy-MM-dd'T'HH:mm:ssZZZ`);

        return true;
      }
    }
  ];
}

export function getEtrColumns(): ColDef[] {
  const cols: ColumnConfig[] =
    store.componentsConfig?.[Components.ETR_SUMMARY_TABLE]?.columns || [];

  return getFormattedCols(cols).map((col) => {
    if (col.field === 'projectedETR') {
      col.cellStyle = ({ data }) => {
        if (data.projectedETR && data.targetedETR) {
          const projectedETR = DateTime.fromISO(data.projectedETR);
          const targetedETR = DateTime.fromISO(data.targetedETR);
          if (projectedETR < targetedETR) return { backgroundColor: 'rgb(0, 163, 108, 0.5)' };
          if (targetedETR < projectedETR) return { backgroundColor: 'rgb(136, 8, 8, 0.7)' };
        }
      };
    }

    return col;
  });
}

export function getEtrSimColumns(etrResults: UnifiedEtrSimData[]): ColDef[] {
  const uniqueIds = [...new Set(etrResults.map((r) => r.id))];
  const firstCol: ColDef = {
    field: 'id',
    headerName: 'Total Workers',
    maxWidth: 82,
    minWidth: 82
  };
  const actuals = etrResults.filter((r) => !r.isSimulation);
  const actualsMap = actuals.reduce(
    (map, r) => {
      map[r.id] = r.totalResources;
      return map;
    },
    {} as { [id: string]: number }
  );
  const cols = uniqueIds
    .sort((a, b) => a.localeCompare(b))
    .map((id) => ({
      autoHeight: true,
      cellClass: ({ colDef, data }: CellClassParams) => {
        return Number(data.id) === actualsMap[colDef.field as string] ? 'highlighted' : undefined;
      },
      cellRenderer: ({ value }: ICellRendererParams) => {
        return value
          ? `<div class="my-2 text-[11px]">
            <div class="leading-none mb-1">${formatDate(value)}</div>
            <div class="leading-none">${formatTime(value)}</div>
          </div>`
          : '';
      },
      field: id,
      minWidth: 110
    }));
  return [firstCol, ...cols];
}

export function getForecastHistoricalColumns(onSelectForecast: (date: string) => void): ColDef[] {
  const cols: ColumnConfig[] =
    store.componentsConfig?.[Components.FORECAST_HISTORICAL_TABLE]?.columns || [];

  const formattedCols = [
    {
      cellRenderer: ({ data }: ICellRendererParams) => {
        const icon = document.createElement('span');
        icon.className = 'material-icons text-primary mt-1 cursor-pointer';
        icon.textContent = 'query_stats';
        icon.onclick = (event) => {
          event.stopPropagation();
          onSelectForecast(data.sessionDate);
        };
        return icon;
      },
      maxWidth: 60,
      minWidth: 60,
      preventExport: true
    },
    ...getFormattedCols(cols)
  ];

  return formattedCols;
}

export function getResourcesActivityColumns(
  removePendingOperation: (crew: FutureResourcesData) => void
): ColDef[] {
  const cols: ColumnConfig[] =
    store.componentsConfig?.[Components.RESOURCE_ACTIVITY_TABLE]?.columns || [];

  const colDefs = getFormattedCols(cols);

  colDefs.push({
    preventExport: true,
    minWidth: 75,
    maxWidth: 75,
    sortable: false,
    cellRenderer: ({ data }: ICellRendererParams) => {
      const span = document.createElement('span');
      span.className = 'material-icons relative top-sm cursor-pointer text-danger';
      span.innerText = 'close';
      span.addEventListener('click', () => removePendingOperation(data));
      return span;
    }
  });

  return colDefs;
}

export function getAnalogEventsColumns(): IColDef[] {
  const cols: ColumnConfig[] =
    store.componentsConfig?.[Components.ANALOG_EVENTS_TABLE]?.columns || [];

  const formattedCols = getFormattedCols(cols);

  formattedCols.unshift({
    checkboxSelection: true,
    maxWidth: 50,
    minWidth: 50,
    preventExport: true
  });

  return formattedCols;
}

export function getCrewLogsColumns(
  roles: string[],
  onShowReleaseCrews: (crew: ActiveCrew) => void
): ColDef[] {
  const cols: ColumnConfig[] = [
    {
      headerName: 'Resource Type',
      field: 'resourceType',
      propertyType: 'string'
    },
    {
      headerName: 'Orig. Location',
      field: 'initialTerritory',
      propertyType: 'string'
    },
    {
      headerName: 'Assigned Location',
      field: 'toTerritoryId',
      propertyType: 'string'
    },
    ...roles.map((role) => ({
      headerName: role,
      valueGetter: ({ data }: ValueGetterParams) => data?.[role],
      field: role,
      propertyType: 'number' as const,
      editable: false
    })),
    {
      headerName: 'Status',
      field: 'status',
      propertyType: 'string'
    },
    {
      headerName: 'Arrival Time',
      field: 'arrivalTime',
      propertyType: 'datetime'
    },
    {
      headerName: 'Release Time',
      field: 'releaseTime',
      propertyType: 'datetime',
      cellRenderer: ({ data }: ICellRendererParams) => {
        if (data.releaseTime) {
          return formatDateTime(data.releaseTime);
        } else {
          const button = document.createElement('button');
          button.className = 'bg-primary rounded px-md my-sm text-white';
          button.textContent = 'Release Crew';
          button.onclick = (event) => {
            event.stopPropagation();
            onShowReleaseCrews(data);
          };
          return button;
        }
      }
    },
    {
      headerName: 'Notes',
      field: 'notes',
      propertyType: 'string'
    }
  ];

  return getFormattedCols(cols);
}
