import { MapStyle, type MapConfig, type MapItem, type MapStyleOption } from '$lib/types';
import darkImg from '$lib/images/dark-v11.jpg';
import lightImg from '$lib/images/light-v11.jpg';
import satelliteStreets from '$lib/images/satellite-streets-v12.jpg';
import streetsDefault from '$lib/images/streets-v12.jpg';
import type { FillLayerSpecification, LngLatLike } from 'mapbox-gl';
import mapboxgl from 'mapbox-gl';
import { get } from 'svelte/store';
import { isDarkMode } from '$lib/stores/theme';

const mapboxToken =
  'pk.eyJ1IjoiZXNvdXJjZSIsImEiOiJjbGQ1MWY0anQwMmxqM3FzZHA0azZzY2UyIn0.KVcK7sl3PpM5erwMLyMIqw';
mapboxgl.accessToken = mapboxToken;

export const MAP_BOUNDS: mapboxgl.LngLatBoundsLike = [
  [-125, 24.5],
  [-66.5, 49.5]
];

const DEFAULT_OPACITY = 0.5;

const HOVERED_OPACITY = 0.75;

export const DEFAULT_MAP_UNITS_SYSTEM = 'imperial';

export const DEFAULT_RADAR_CONFIG = {
  opacity: 0.3,
  radarUrl: 'https://api.rainviewer.com/public/weather-maps.json',
  colorScheme: '4'
};

export const DEFAULT_WIND_CONFIG = {
  overallOpacity: 0.35,
  particleCount: 1700,
  particleLifespan: 250,
  particleSize: 1.1,
  speedFactor: 0.065
};

export enum MapZoomLevels {
  LOW = 1,
  MOBILE_MEDIUM = 5,
  MEDIUM = 6,
  HIGH = 11,
  HIGHER = 12,
  VERY_HIGH = 13
}

// Center of United States
const DEFAULT_MAP_CENTER: LngLatLike = [-103.46, 44.58];

export const DEFAULT_MAP_SETTINGS = {
  zoom: MapZoomLevels.MEDIUM,
  minZoom: MapZoomLevels.MEDIUM,
  center: DEFAULT_MAP_CENTER,
  style: MapStyle.dark
};

export const MAP_STYLE_OPTIONS: MapStyleOption[] = [
  {
    label: 'Dark',
    value: MapStyle.dark,
    imgSrc: darkImg
  },
  {
    label: 'Light',
    value: MapStyle.light,
    imgSrc: lightImg
  },
  {
    label: 'Street',
    value: MapStyle.street,
    imgSrc: streetsDefault
  },
  {
    label: 'Satellite Streets',
    value: MapStyle.streetSatellite,
    imgSrc: satelliteStreets
  }
];

export const HEATMAP_FILL_COLORS_DARK = [
  '#6668D1',
  '#296E9B',
  '#4c7557',
  '#817441',
  '#B65648',
  '#772A55'
];
export const HEATMAP_FILL_COLORS_LIGHT = [
  '#8a8bd5',
  '#49aef0',
  '#6bdd73',
  '#edca43',
  '#d77b3d',
  '#cb5050'
];
export const GRADIENT_COLORS = [
  '#0046a8',
  '#0085d8',
  '#66c7ff',
  '#c0f0ff',
  '#ff6b70',
  '#e21828',
  '#890d16'
];

export const GRADIENT_COLORS_DARK = [
  '#002d75',
  '#005f94',
  '#3498b2',
  '#7fb8cc',
  '#d15256',
  '#a10d1d',
  '#5e0a10'
];

export const getHeatmapColors = (lightColorLegend?: string[], darkColorLegend?: string[]) => {
  if (lightColorLegend?.length && darkColorLegend?.length) {
    return get(isDarkMode) ? darkColorLegend : lightColorLegend;
  }

  return get(isDarkMode) ? HEATMAP_FILL_COLORS_DARK : HEATMAP_FILL_COLORS_LIGHT;
};

export const getGradientColors = (
  lightColorLegend?: string[],
  darkColorLegend?: string[],
  steps?: number
): string[] => {
  let colors: string[];

  if (lightColorLegend?.length && darkColorLegend?.length) {
    colors = get(isDarkMode) ? darkColorLegend : lightColorLegend;
  } else {
    colors = get(isDarkMode) ? GRADIENT_COLORS_DARK : GRADIENT_COLORS;
  }

  if (!steps || steps <= 0 || steps >= colors.length) {
    return colors;
  }

  const stepSize = (colors.length - 1) / (steps - 1);
  return Array.from({ length: steps }, (_, i) => colors[Math.round(i * stepSize)]);
};

// Record Keeping - BE app config provides the below two objects
//
// export const stateFillLayer: mapboxgl.FillLayer = {
//   id: 'kentucky-fill',
//   paint: {
//     'fill-color': '#000',
//     'fill-opacity': 0.5
//   },
//   source: 'regions',
//   'source-layer': 'kentucky',
//   type: 'fill'
// };
//
// export const regionBorderLayer: mapboxgl.LineLayer = {
//   id: 'region-borders',
//   paint: { 'line-color': '#fff', 'line-width': 1 },
//   source: 'regions',
//   'source-layer': 'regions',
//   type: 'line'
// };

const regionFillLayer: FillLayerSpecification = {
  id: 'region-fill',
  source: 'regions',
  'source-layer': 'regions',
  type: 'fill'
};

/**
 * For range-to-color match under the map legend
 * https://github.com/markusand/mapboxgl-legend
 */
export function getColorSteps(
  steps: number[] | string[],
  colors: string[]
): Array<number | string> {
  const mapSteps: Array<number | string> = [];
  steps.forEach((step: number | string, i: number) => {
    if (i !== 0) mapSteps.push(step);
    mapSteps.push(colors[i]);
  });
  return mapSteps;
}

/**
 * For 1:1 value-to-color match under the map legend
 * https://github.com/markusand/mapboxgl-legend
 * Must have a default catch-all color (visible as 'OTHER' in the legend)
 */
export function getColorMatches(
  matchValues: number[] | string[],
  colors: string[]
): Array<number | string> {
  const matches: Array<number | string> = [];

  matchValues.forEach((matchValue: number | string, i: number) => {
    matches.push(matchValue);
    matches.push(colors[i]);
  });
  matches.push('#000'); // Default OTHER category (to be hidden)
  return matches;
}

export function getRegionalFillLayer(mapConfig: MapConfig, id: string): FillLayerSpecification {
  const {
    attributeType,
    legendType,
    legendValues,
    unit,
    gradient,
    displayName,
    lightColorLegend,
    darkColorLegend
  } = mapConfig;
  const colors = gradient
    ? getGradientColors(lightColorLegend, darkColorLegend)
    : getHeatmapColors(lightColorLegend, darkColorLegend);

  const legendColorData =
    legendType === 'match'
      ? getColorMatches(legendValues, colors)
      : getColorSteps(legendValues, colors);

  return {
    ...regionFillLayer,
    source: id,
    'source-layer': id,
    metadata: { name: `${displayName} ${unit ? `(${unit})` : ''}`, labels: { other: false } },
    paint: {
      'fill-color': [legendType, ['feature-state', attributeType], ...legendColorData],
      'fill-opacity': [
        'case',
        ['boolean', ['feature-state', 'hover'], false],
        HOVERED_OPACITY,
        DEFAULT_OPACITY
      ]
    }
  };
}

export function getRegionalImpactFillLayer(): FillLayerSpecification {
  const colors = getHeatmapColors();

  return {
    ...regionFillLayer,
    metadata: { labels: { other: false }, name: 'Impact' },
    paint: {
      'fill-color': [
        'match',
        ['feature-state', 'impact'],
        ...colors.flatMap((item, index) => [index + 1, item]),
        /* other */ '#fff'
      ],
      'fill-opacity': [
        'case',
        ['boolean', ['feature-state', 'hover'], false],
        HOVERED_OPACITY,
        DEFAULT_OPACITY
      ]
    }
  };
}

export function getOutageFillLayer(
  { steps, attributeName, legendName, lightColorLegend, darkColorLegend }: MapItem,
  id: string
): FillLayerSpecification {
  const isMatch = typeof steps[0] === 'string';
  const colors = getGradientColors(lightColorLegend, darkColorLegend);
  const legendColorData = isMatch ? getColorMatches(steps, colors) : getColorSteps(steps, colors);

  return {
    ...regionFillLayer,
    source: id,
    'source-layer': id,
    metadata: { name: legendName, labels: { other: false } },
    paint: {
      'fill-color': [
        isMatch ? 'match' : 'step',
        ['feature-state', attributeName],
        ...legendColorData
      ],
      'fill-opacity': [
        'case',
        ['boolean', ['feature-state', 'hover'], false],
        HOVERED_OPACITY,
        DEFAULT_OPACITY
      ]
    }
  };
}

export function formatRegionId(region: string): string {
  return region.trim().toLowerCase().replace(/\s+/g, '-');
}
