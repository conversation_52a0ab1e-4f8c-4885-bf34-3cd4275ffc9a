/*
  Below showcases if we need to use a custom cell for the Table and how it would generally work

  This would be the example column to add:

  {
    headerName: 'ACTIVE',
    valueGetter: (row) => row.data?.isActive,
    cellRenderer: ActiveCellRenderer
  }

  We would also create a gridOptionsStore that we can use across the app including the below file that will hold the important options object

  Note: You could also use a function instead of a class which may prove to be easier to work with.

  Ex:
  cellRenderer: function(params) {
    // Create a new checkbox input element
    const checkbox = document.createElement('input');
    checkbox.type = 'checkbox';

    // Set the initial state of the checkbox based on the cell value
    checkbox.checked = params.value;

    // Add an event listener to update the cell value when the checkbox is clicked
    checkbox.addEventListener('change', function(event) {
      params.data.checkbox = event.target.checked;
      params.api.refreshCells({rowNodes: [params.node], columns: [params.column]});
    });

    // Return the checkbox input element
    return checkbox;
  }

  This is the ActiveCellRenderer class file:

  import { get } from 'svelte/store';
  import { Button } from '$src/components/ui';
  import { gridOptionsStore } from '$src/stores';
  import type { ICellRendererParams } from 'ag-grid-enterprise';
  import type { TableRow } from '$src/types';

  // https://www.ag-grid.com/javascript-data-grid/component-cell-renderer/
  export default class ActiveCellRenderer {
    gui;
    data: Partial<TableRow> = {};
    isActive = false;
    button: Button | null = null;
    removeListener: (() => void) | undefined = undefined;

    constructor() {
      // create empty div to place svelte component in
      this.gui = document.createElement('div');
    }

    // Gets called once before the renderer is used
    init(params: ICellRendererParams) {
      this.isActive = params.value;
      this.data = params.data;

      // https://svelte.dev/docs#run-time-client-side-component-api
      this.button = new Button({
        target: this.gui,
        props: {
          size: 'md',
          type: this.isActive ? 'primary' : 'secondary'
        }
      });
      this.removeListener = this.button.$on('click', this.doSomething.bind(this));
    }

    doSomething(e: MouseEvent) {
      // Do something here such as update the grid in order to refresh the GUI..or have the app do something else non-grid related
      // In theory if we're only changing the cell css itself and that data associated with it we could probably do that here. Depends.
      e.stopPropagation();
      this.isActive = !this.isActive;
      const options = get(gridOptionsStore);
      // Update the current row's data in order for "refresh" to actually be called. Won't be called unless the grid finds a change
      this.data.isActive = this.isActive;
      options?.api?.refreshCells();
    }

    getGui() {
      return this.gui;
    }

    // Gets called whenever the cell refreshes, but if Ag-grid doesn't notice a change in row data, won't run.
    refresh(params: ICellRendererParams) {
      this.isActive = params.value;

      // Return false in order to make the grid recreate the dom node. Normally return true. Depends on the use case and need to run GUI update.
      return false;
    }

    // Gets called when the cell is removed from the grid
    destroy() {
      // Do cleanup, remove event listener from button
      if (this.button) {
        // Check that the button element exists as destroy() can be called before getGui()
        this.removeListener?.();
        this.button.$destroy();
      }
    }
  }

*/
