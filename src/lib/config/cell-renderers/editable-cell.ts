import { EditableCell } from '$lib/components';
import { mount, unmount } from 'svelte';
import type { ICellRendererParams } from 'ag-grid-enterprise';

export default class EditableCellRenderer {
  component: EditableCell | null = null;
  gui: HTMLElement;

  constructor() {
    this.gui = document.createElement('div');
  }

  init({ value }: ICellRendererParams) {
    this.component = mount(EditableCell, {
      target: this.gui,
      props: { value }
    });
  }

  getGui() {
    return this.gui;
  }

  destroy() {
    if (this.component) {
      unmount(this.component);
    }
  }
}
