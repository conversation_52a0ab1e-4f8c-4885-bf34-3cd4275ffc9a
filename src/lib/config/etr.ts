import { DateTime } from 'luxon';
import { formatNumbers } from '$lib/utils';
import {
  StormMode,
  type AggregateMetricRecord,
  type StormData,
  type AggregateMetricRecordWithRegion,
  type SystemStateSnapshot
} from '../types';
import { isSystemic } from '$lib/stores/constants';
import { PERCENTILE_SCALE_LIGHT, PERCENTILE_SCALE_DARK, generateChartColorsScale } from './chart';
import { formatDateTime } from './utils';
import { type AgLineSeriesOptions } from 'ag-charts-enterprise';
import { formatRegionId } from './map';

export const MS_IN_SECOND = 1000;
export const MS_IN_HOUR = 60 * 60 * 1000;
export const ETR_DATA_VALIDITY_TIME = 90 * 60 * 1000;
export const ETR_AGGTEGATION_TYPE = 'ETR';
export const REQUESTED_FORECAST_OUTAGES_TIME_WINDOW = 24 * MS_IN_HOUR;
export const REQUESTED_OUTAGES_TIME_WINDOW = 6 * MS_IN_HOUR;
const TEN_MINUTES_MS = 10 * 60 * 1000;
export const STORM_WINDOW_HOURS = 6;

export enum OutageScales {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high'
}

export enum OutagesChartScope {
  REGION = 'region',
  SYSTEM = 'system'
}

export enum SystemStatus {
  MODEL_RUNNING = 'model_running',
  MODEL_NOT_RUNNING = 'model_not_running',
  SIMULATION_RUNNING = 'simulation_running',
  SIMULATION_NOT_RUNNING = 'simulation_not_running'
}

export const ETR_CHART_SPECIAL_MARKERS = {
  currentResources: 'Current Resources',
  simulatedResources: 'Simulated Resources'
};

export function getColorScale(data: Record<string, any>[], isDark: boolean, isSystemic: boolean) {
  if (isSystemic) {
    return isDark ? PERCENTILE_SCALE_DARK : PERCENTILE_SCALE_LIGHT;
  }

  return generateChartColorsScale([...new Set(data.map((d) => d.group))], isDark);
}

function getItemColor(
  isSystemicView: boolean,
  isCurrentResources: boolean,
  regionColor: string,
  isDark: boolean
) {
  if (!isSystemicView || !isCurrentResources) return regionColor;

  return isDark ? '#1455a0' : '#2a8ae6';
}

export function getLineChartSeriesItems(
  data: Record<string, any>[],
  isDark: boolean,
  colorScheme: Record<string, string>
) {
  const isSystemicView = isSystemic;
  return Object.entries(colorScheme).map(([name, color]) => {
    const isCurrentResources = name === ETR_CHART_SPECIAL_MARKERS.currentResources;
    const seriesItem: AgLineSeriesOptions = {
      type: 'line',
      yKey: 'hoursToRestoration',
      xKey: 'value',
      showInLegend: isCurrentResources && !isSystemicView ? false : true,
      yName: name,
      stroke: color,
      data: data.filter(({ group }) => group === name),
      marker: {
        stroke: isDark ? '#fff' : '#000',
        shape: 'diamond',
        fill: getItemColor(isSystemicView, isCurrentResources, color, isDark),
        itemStyler: ({ datum }: Record<string, any>) => {
          if (datum.isFilled) {
            return {
              size: 16,
              fill: getItemColor(isSystemicView, false, colorScheme[datum.group], isDark),
              strokeWidth: 1
            };
          } else {
            return {
              size: 6,
              fill: getItemColor(isSystemicView, false, colorScheme[datum.group], isDark),
              strokeWidth: 0
            };
          }
        }
      },
      tooltip: {
        renderer({ datum, yName }: Record<string, any>) {
          const projectedETR = formatDateTime(datum.date);
          return `
            <div class="ag-chart-tooltip-title" style="background-color:${getItemColor(isSystemicView, isCurrentResources, colorScheme[datum.group], isDark)}">
             ${datum.isFilled && !isSystemicView ? `${yName} - Current Resources` : yName}
            </div>
            <ul class="ag-chart-tooltip-content p-0">
              <li>
                <div class="flex text-xs p-xs gap-sm border-b border-solid border-border">
                  <div><p class="text-xs">Total Workers</p></div>
                  <p class="text-xs" />${formatNumbers(datum.value)}</p>
                </div>
              </li>
              <li>
                <div class="flex text-xs p-xs gap-sm border-b border-solid border-border">
                  <div><p class="text-xs">Hours to Restoration</p></div>
                  <p class="text-xs" />${datum.hoursToRestoration.toFixed(2)}h</p>
                </div>
              </li>
              <li>
                <div class="flex text-xs p-xs gap-sm">
                  <div><p class="text-xs">Projected ETR:</p></div>
                  <p class="text-xs" />${projectedETR}</p>
                </div>
              </li>
            </ul>
          `;
        }
      }
    };

    return seriesItem;
  });
}

export function getDataRequestStartTime(storm: StormData): string {
  const now = DateTime.now();

  const forecastDataRequestStart = now.minus({
    milliseconds: REQUESTED_FORECAST_OUTAGES_TIME_WINDOW
  });
  const stormStart = DateTime.fromJSDate(storm.stormStartDate!);
  const stormDataRequestStart = stormStart.minus({ milliseconds: REQUESTED_OUTAGES_TIME_WINDOW });

  const startTime =
    storm.stormMode === StormMode.FORECAST ? forecastDataRequestStart : stormDataRequestStart;

  return startTime.toISO()!;
}

export function groupByTimestamp(data: Record<string, AggregateMetricRecord[]>) {
  const result: Record<string, AggregateMetricRecordWithRegion[]> = {};

  for (const [location, incidents] of Object.entries(data)) {
    incidents.forEach((incident) => {
      const { timestamp, ...rest } = incident;
      const time = new Date(timestamp).getTime();

      // Round to the nearest 10-minute interval
      const rounded = Math.round(time / TEN_MINUTES_MS) * TEN_MINUTES_MS;
      const unixTs = Math.floor(rounded / MS_IN_SECOND).toString();

      if (!result[unixTs]) {
        result[unixTs] = [];
      }

      result[unixTs].push({ ...rest, region: location, timestamp });
    });
  }

  return result;
}

const roundTo10Min = (dt: DateTime, interval: number) =>
  DateTime.fromMillis(Math.floor(dt.toMillis() / interval) * interval);

export function formatWorkerRecords(
  snapshots: SystemStateSnapshot[],
  timestamps: number = 15,
  isSystemView: boolean = false,
  endTime: DateTime = DateTime.utc(),
  step: number = TEN_MINUTES_MS
) {
  const intervals: DateTime[] = [];
  for (let i = 0; i <= timestamps; i++) {
    intervals.push(endTime.minus({ milliseconds: (timestamps - i) * step }));
  }

  return intervals.flatMap((interval) => {
    const roundedInterval = roundTo10Min(interval, step);

    const previousSnapshot = snapshots
      .filter((s) => DateTime.fromISO(s.timestamp) <= roundedInterval)
      .at(-1);

    if (!previousSnapshot) return [];

    if (isSystemView) {
      const resources = previousSnapshot.systemState.aggregatedResources;

      const totalWorkers = Object.values(resources).reduce((sum, val) => sum + val, 0);

      return {
        totalWorkers,
        resources,
        region: 'System',
        date: roundedInterval.toJSDate(),
        timestamp: roundedInterval.toISO()!
      };
    } else {
      return previousSnapshot.systemState.regionalResourceStates.map((region) => {
        const { resources } = region;

        const totalWorkers = Object.values(resources).reduce((sum, val) => sum + val, 0);

        return {
          totalWorkers,
          resources,
          region: region.territoryName,
          date: roundedInterval.toJSDate(),
          timestamp: roundedInterval.toISO()!
        };
      });
    }
  });
}

export function groupSystemStatesByTimestamp(
  data: {
    timestamp: string;
    totalWorkers: number;
    region: string;
    date: Date;
    resources: Record<string, number>;
  }[]
): Record<
  string,
  {
    timestamp: string;
    totalWorkers: number;
    region: string;
    date: Date;
    resources: Record<string, number>;
  }[]
> {
  const result: Record<string, any[]> = {};

  data.forEach((record) => {
    const unixTs: string = (new Date(record.timestamp).getTime() / MS_IN_SECOND).toString();
    if (!result[unixTs]) {
      result[unixTs] = [];
    }
    result[unixTs].push(record);
  });

  return result;
}

export function mergeResourceTotals(
  resourcesMap: Record<
    number,
    {
      timestamp: string;
      totalWorkers: number;
      region: string;
    }[]
  >,
  metricsMap: Record<string, AggregateMetricRecordWithRegion[]>
): Record<number, AggregateMetricRecordWithRegion[]> {
  const result: Record<number, AggregateMetricRecordWithRegion[]> = {};

  for (const timestampKey in metricsMap) {
    const metricEntries = metricsMap[timestampKey];
    const resourceEntries = resourcesMap[+timestampKey] ?? [];

    const resourceTotalsByRegion = Object.fromEntries(
      resourceEntries.map((r) => [formatRegionId(r.region), r.totalWorkers])
    );

    result[+timestampKey] = metricEntries.map((entry) => {
      const regionKey = formatRegionId(entry.region);
      const overrideTotal = resourceTotalsByRegion[regionKey];

      return {
        ...entry,
        totalResources: overrideTotal
      };
    });
  }

  return result;
}
