import type {
  CellClassParams,
  ColDef,
  HeaderValueGetterParams,
  ICellRendererParams,
  TooltipRendererParams,
  ValueFormatterParams
} from 'ag-grid-enterprise';
import type { DateTime } from 'luxon';

declare global {
  interface Window {
    onSelectDailySession: (date: string) => void;
  }
}

export type IconName = string;
export type WindSpeed = '20' | '30' | '40';

export type LayerType =
  | 'symbol'
  | 'fill'
  | 'line'
  | 'raster'
  | 'model'
  | 'slot'
  | 'circle'
  | 'heatmap'
  | 'fill-extrusion'
  | 'raster-particle'
  | 'hillshade'
  | 'background'
  | 'sky'
  | 'clip';

export enum Components {
  EXTERNAL_RESOURCES_LIST = 'external-resources-list',
  CLIENT_LOGO = 'client-logo',
  HEADER_LOGO = 'header-logo',
  TERRITORY_SUMMARY = 'territory-summary',
  ETR_SUMMARY_TABLE = 'etr-summary-table',
  ETR_HISTORICAL_TABLE = 'etr-historical-table',
  CURRENT_RESOURCE_TABLE = 'current-resource-table',
  FUTURE_RESOURCE_TABLE = 'future-resource-table',
  FUTURE_RESOURCE_TABLE_V2 = 'future-resource-table-v2',
  PENDING_RESOURCE_OPERATION_TABLE = 'pending-resource-operation-table',
  RESOURCE_ENTRY_TABLE = 'resource-entry-table',
  RESOURCE_TABLE = 'resource-table',
  RESOURCE_VALIDATION = 'resource-validation',
  RESOURCE_ACTIVITY_TABLE = 'resource-activity-table',
  ETR_OUTAGES_CHART = 'etr-outages-chart',
  ETR_HISTORICAL_OUTAGES_CHART = 'etr-historical-outages-chart',
  ETR_WORKERS_CHART = 'etr-workers-chart',
  NAVIGATION_ITEMS = 'navigation-items',
  EXTERNAL_NAVIGATION_ITEMS = 'external-navigation-items',
  ETR_MAP = 'etr-map',
  RETROSPECTIVE_MAP = 'retrospective-map',
  SPATIAL_MAP = 'spatial-map',
  HOURLY_MAP = 'hourly-map',
  HOURLY_MAP_REGIONS = 'hourly-map-regions',
  FORECAST_HISTORICAL_TABLE = 'forecast-historical-table',
  HOURLY_CHART = 'hourly-chart',
  RESTORATION_MAP = 'restoration-map',
  FORECAST_HISTORICAL_VALIDATION = 'forecast-historical-validation',
  POINT_FORECAST_CARDS = 'point-forecast-cards',
  ANALOG_EVENTS_TABLE = 'analog-events-table',
  ETR_MAP_OUTAGES_SOURCE = 'etr-map-outages-source',
  BANNER_INFO_ICON = 'banner-info-icon'
}

export enum PageModes {
  weather = 'Weather',
  outage = 'Outage Forecast',
  forecast = 'Forecast Events'
}

export enum EtrChartKeys {
  pastOutageCount = 'pastOutageCount',
  cumulativeIncidents = 'cumulativeIncidents',
  cumulativeRestorations = 'cumulativeRestorations',
  cumulativeRestoredOutage = 'cumulativeRestoredOutage',
  outageCount = 'outageCount',
  restorationCount = 'restorationCount',
  cumulativeAffectedCustomers = 'cumulativeAffectedCustomers',
  cumulativeCustomersRestored = 'cumulativeCustomersRestored',
  newOutageCount = 'newOutageCount',
  uniqueIncidentsCount = 'uniqueIncidentsCount',
  restOutageCount = 'restOutageCount',
  projRestOutageCount = 'projRestOutageCount',
  assessedOutageCount = 'assessedOutageCount',
  totalWorkers = 'totalWorkers',
  restoredPerWorker = 'restoredPerWorker',
  hours = 'hours',
  hoursToRestoration = 'hoursToRestoration'
}

export enum FeatureFlag {
  daily = 'storm.modules.daily.enabled',
  hourly = 'storm.modules.hourly.enabled',
  shifts = 'storm.modules.shifts.enabled',
  forecastRetrospective = 'storm.modules.forecastRetrospective.enabled',
  etr = 'storm.modules.etr.enabled',
  analogs = 'storm.modules.analogs.enabled',
  wildfire = 'storm.modules.wildfire.enabled'
}

export enum MapStyle {
  street = 'streets-v12',
  streetSatellite = 'satellite-streets-v12',
  light = 'light-v11',
  dark = 'dark-v11'
}

export enum Visibility {
  customers = 'totalAffectedCustomers',
  outages = 'totalIncidents',
  cumulativeCustomers = 'cumulativeAffectedCustomers',
  cumulativeIncidents = 'cumulativeIncidents',
  totalCustomers = 'totalCumulativeCustomersAffected',
  totalOutages = 'totalCumulativeIncidents',
  cumulativeCustomersAffected = 'cumulativeCustomersAffected',
  cumulativeOutages = 'cumulativeOutages',
  rawCustomers = 'totalRawAffectedCustomers',
  rawOutages = 'totalRawIncidents',
  threatLevel = 'threatLevel'
}

export enum Weather {
  sustainedWind = 'windSustained',
  windGustMax = 'windGustMax',
  totalPrecipitation = 'precip',
  minTemperature = 'temperatureMin',
  meanTemperature = 'temperatureMean',
  maxTemperature = 'temperatureMax',
  spcThreatLevel = 'threatLevel'
}

export enum StormMode {
  ACTIVE = 'active',
  ARCHIVE = 'archive',
  FORECAST = 'forecast',
  POST_RESTORATION = 'post-restoration',
  REMOVED = 'removed'
}

export enum SimulationOutageScale {
  low = 'LOW',
  medium = 'MEDIUM',
  high = 'HIGH'
}

export enum RegionalEtrLevel {
  current = 'CURRENT',
  currentOptimized = 'RESULT_OPTIMIZED',
  plusResouces = 'PLUS_RESOURCES',
  plusResourcesOptimized = 'PLUS_RESOURCES_OPTIMIZED'
}

export enum OutageSource {
  refined = 'refined',
  raw = 'raw'
}

// Combination of all component configs
export type ComponentsConfigMap = {
  [Components.EXTERNAL_RESOURCES_LIST]?: LinksListConfig;
  [Components.CLIENT_LOGO]?: ImageConfig;
  [Components.HEADER_LOGO]?: ImageConfig;
  [Components.TERRITORY_SUMMARY]?: InfoBoxConfig;
  [Components.ETR_SUMMARY_TABLE]?: TableConfig;
  [Components.ETR_HISTORICAL_TABLE]?: TableConfig;
  [Components.CURRENT_RESOURCE_TABLE]?: TableConfig;
  [Components.FUTURE_RESOURCE_TABLE]?: TableConfig;
  [Components.FUTURE_RESOURCE_TABLE_V2]?: TableConfig;
  [Components.PENDING_RESOURCE_OPERATION_TABLE]?: TableConfig;
  [Components.RESOURCE_ENTRY_TABLE]?: TableConfig;
  [Components.RESOURCE_ACTIVITY_TABLE]?: TableConfig;
  [Components.RESOURCE_TABLE]?: TableConfig;
  [Components.RESOURCE_VALIDATION]?: ValidationsConfig;
  [Components.ETR_OUTAGES_CHART]?: ChartsConfig;
  [Components.ETR_HISTORICAL_OUTAGES_CHART]?: ChartsConfig;
  [Components.ETR_WORKERS_CHART]?: ChartsConfig;
  [Components.NAVIGATION_ITEMS]?: NavigationItemsConfig;
  [Components.EXTERNAL_NAVIGATION_ITEMS]?: ExternalNavigationItemsConfig;
  [Components.ETR_MAP]?: MapItems;
  [Components.RETROSPECTIVE_MAP]?: MapItems;
  [Components.RESTORATION_MAP]?: MapItems;
  [Components.SPATIAL_MAP]?: MapItems;
  [Components.HOURLY_MAP]?: MapItems;
  [Components.HOURLY_MAP_REGIONS]?: MapItems;
  [Components.HOURLY_CHART]?: HourlyChartsConfig;
  [Components.FORECAST_HISTORICAL_TABLE]?: TableConfig;
  [Components.FORECAST_HISTORICAL_VALIDATION]?: ValidationsConfig;
  [Components.POINT_FORECAST_CARDS]?: CardValidationsConfig<'customersAffected' | 'outages'>;
  [Components.ANALOG_EVENTS_TABLE]?: TableConfig;
  [Components.ETR_MAP_OUTAGES_SOURCE]?: MapOutageItems;
  [Components.BANNER_INFO_ICON]?: InfoBoxConfig;
};

export interface Icon {
  box: number;
  boxW?: number;
  boxH?: number;
  name: IconName;
  svg?: string;
}

export interface SessionPrediction {
  date: string;
  min: number;
  mid?: number;
  max: number;
  predictions: Record<string, number>;
}

export interface HistoricalPrediction {
  endDate: string;
  sessions: HistoricalPredictedSessions[];
  startDate: string;
}

export interface HistoricalMetric {
  match: number;
  off1: number;
  off2: number;
  other: number;
}

export interface HistoricalPredictedSessions {
  customersHigh: number;
  customersLow: number;
  outageRangeHigh: number;
  outageRangeLow: number;
  sessionDate: string;
  sessionId: string;
  totalCustomers: number;
  totalOutages: number;
  actualIncidents: number;
}

export interface HistoricalPredictedSessionsWithDate extends HistoricalPredictedSessions {
  date: Date;
}

export interface Region {
  centroid: [number, number];
  displayName: string;
  name: string;
  type: string;
  hasSubregions?: boolean;
}

export interface RegionalWeather {
  day: string;
  displayName: Region['displayName'];
  name: Region['name'];
  weatherAttributes: {
    precip: number;
    threatLevel: string;
    windGustMax: number;
    windSustained: number;
    temperatureMax: number;
    temperatureMean: number;
    temperatureMin: number;
    cloudCoverPercentMean: number;
    threatLevelCategory: string;
  };
}

export interface SpatialData {
  name: Region['name'];
  displayName?: Region['displayName'];
  attributes: RegionalWeather['weatherAttributes'] & {
    outages: number;
    customersAffected: number;
    threatLevel: string;
  };
}

export interface Score {
  color: string;
  title: string;
  value: number;
}

export interface EventLevel {
  category?: string;
  color?: { dark: string; light: string };
  description: string;
  eventCode: string;
  eventDisplayName: string;
  range: [number, number];
  view: 'system' | 'pd-area' | 'workgroup' | null;
  name?: string;
}

export interface MapConfig {
  defaultDisplay: boolean;
  displayName: string;
  legendType: 'match' | 'step';
  legendValues: number[] | string[];
  unit: string;
  attributeType: Weather;
  icon: string;
  gradient: boolean;
  precision?: number;
  lightColorLegend?: string[];
  darkColorLegend?: string[];
  tooltipLabel?: string;
  secondaryTooltip?: {
    label: string;
    attributeType: string;
    precision: number;
  };
}

export interface MapOutageItems {
  items: MapOutageConfig[];
}

export interface MapOutageConfig {
  name: string;
  attributeName: OutageSource;
  icon: string;
  tooltip: string;
}

export interface EtrMapConfig {
  steps: number[];
  attributeName: string;
  displayName: string;
}

export interface Shift {
  end: string;
  outages: { max: number; min: number };
  regions: { displayName: string; name: string; outageCount: number }[];
  start: string;
  steps: number[];
}

export interface HourlyShift {
  endTime: string;
  hourlyZones: {
    date: string;
    displayName: string;
    outages: number;
    region: string;
    weatherAttributes: WeatherThreatData;
  }[];
  shiftNo: 1 | 2 | 3;
  startTime: string;
}

interface WeatherThreatData {
  outages: number;
  customersAffected: number;
  windGustMax: number;
  windSustained: number;
  precip: number;
  precipMean: number;
  temperatureMin: number;
  temperatureMean: number;
  temperatureMax: number;
  threatLevel: number;
}

export interface AppConfig {
  center: { value: [number, number] };
  regionBorderLayer: {
    id: string;
    paint: { [key: string]: number | string };
    source: string;
    'source-layer': string;
    type: LayerType;
  };
  stateFillLayer: {
    id: string;
    paint: { [key: string]: number | string };
    source: string;
    'source-layer': string;
    type: LayerType;
  };
  tilesetUrl: { value: string };
  networkDistributionUrl?: { value: string };
  devicesUrl?: { value: string };
  maxBounds: { value: [[number, number], [number, number]] };
  etrMapId: { value: string };
  regionCircleLayer: {
    id: string;
    paint: {
      [key: string]: number | string;
    };
    type: string;
    source: string;
  };
  regionWindSpeedTextLayer: {
    id: string;
    layout: {
      [key: string]: number;
    };
    paint: { [key: string]: number };
    source: string;
    type: string;
  };
  regionResourceCountTextLayer: {
    id: string;
    layout: {
      [key: string]: number;
    };
    paint: { [key: string]: number };
    source: string;
    type: string;
  };
}

export interface NotificationOption {
  levels?: string[];
}

export interface NotificationConfig {
  isSelected: boolean;
  selectedLevel: string;
  id: number;
  displayName: string;
  displayOrder: number;
  module: string;
  options: NotificationOption | null;
  isEnabled: boolean;
}

export interface NotificationSettings {
  id: number;
  displayName: string;
  displayOrder: number;
  module: string;
  options: NotificationOption | null;
  isEnabled: boolean;
}

interface User {
  id: number;
  email: string;
  lastModified: number[];
}

interface Notification {
  id: number;
  displayName: string;
  displayOrder: number;
  module: string;
  options: NotificationOption;
  isEnabled: boolean;
}

interface Metadata {
  level: string;
}

export interface UserNotificationSettings {
  id: number;
  user: User;
  notification: Notification;
  lastModifiedDate: number[];
  metadata: Metadata;
}

export interface Subscription {
  email: string;
  notificationId: number;
  options: { level?: string };
  subscribed: boolean;
}

export interface DailyPredictionMap {
  [date: string]: {
    date: string;
    hourlyPredictions: { hour: string; max: number; min: number; mid?: number }[];
  };
}

export interface ChartConfig {
  attributeName: string;
  displayName: string;
  isDefaultDisplay: boolean;
  isDisplayed: boolean;
  type: 'weather';
  yaxis: { range: { max: number; min: number }; unit: string };
}

export interface RegionalEtr {
  activeCustOutages?: number | '-';
  activeOutages?: number | '-';
  id?: number;
  location: string;
  outageScale: 'low' | 'medium' | 'high';
  projectedETR: string | null;
  remainingCustOutages: number;
  remainingOutages: number;
  resources: { [key: string]: number } | null;
  type: 'current' | 'simulation' | null;
  generationTime: string;
}

export interface Resource {
  arrivalTime: null;
  territoryId: string;
  territoryName: string;
  totalResources: Record<string, number>;
}

export interface ResourceV2 {
  territoryId: string;
  territoryName: string;
  resources: Record<string, number>;
}

export interface RegionalResourceState {
  regionalResourceStates: ResourceV2[];
  stormId?: string;
}

export interface AggregatedResourceOperations {
  id: string;
  type: string;
  dispatchTime: string;
  executionTime: string;
  fromTerritoryId: string;
  toTerritoryId: string;
  resources: Record<string, number>;
}

export interface ResourceData {
  delta?: number;
  futureTotal?: number;
  id: number;
  resourcesNeeded?: number;
  territoryName: string;
  total: number;
  [key: string]: number | string | undefined;
}

export interface FutureResourcesData {
  fromTerritoryId: string;
  toTerritoryId: string;
  resourceType: string;
  total: number;
  dispatchTime: string;
  executionTime: string;
  status: string;
  id: string;
}

export interface ComponentConfig {
  component: string;
  configuration: Record<string, any>;
}

export interface DisplayConfig {
  configuration: Record<string, any>;
  type: string;
}

export interface RoleConfig {
  configuration: Record<string, any>;
  type: string;
}

export interface ThemeConfig {
  colorPrimary: string;
  colorSecondary: string;
  colorSidebar: string;
  colorNavActive: string;
  chartColors?: string[];
}

export interface ThemeConfigResp {
  configuration: ThemeConfig;
  type: string;
}

export interface ColumnConfig {
  propertyType?: 'number' | 'string' | 'time' | 'date' | 'datetime';
  field?: string;
  headerName?: string;
  editable?: boolean;
  preventExport?: boolean;
  precision?: number;
  type?: string;
  tooltip?: string;
  minWidth?: number;
  cellStyle?: { [key: string]: string };
  cellEditor?: string;
  cellRenderer?: (params: ICellRendererParams) => void;
  valueFormatter?: (params: ValueFormatterParams) => any;
  cellClass?: (params: CellClassParams) => any;
  cellEditorParams?: { min: any; precision?: number; step?: number };
  headerValueGetter?: (params: HeaderValueGetterParams) => any;
}

export interface EventBody {
  aggregateType?: string;
  aggregateId?: string;
  data: { [key: string]: any };
  event: string;
  eventDate?: string;
}

export interface ResourceOperationBody {
  linkedPendingOperationId: string | null;
  stormId: string;
  type: string;
  executionTime: string | null;
  specificMetadata: {
    fromTerritoryId?: string;
    toTerritoryId?: string;
  };
  resources: Record<string, number>;
}

export interface ResourceMoveMetadata {
  movedResourceType: string;
  fromTerritoryId: string;
  toTerritoryId: string;
}

export interface ResourceAddMetadata {
  toTerritoryId: string;
}

export interface EtrSummaryStats {
  avgGustMph: number;
  avgPrecipInch: number;
  avgWindMph: number;
  forecastDateTime: string;
  totalActiveOutages: number;
  totalNewOutages: number;
  totalRestoredOutages: number;
  totalAssessedOutages: number;
}

export interface EtrSummary {
  sessionId: string;
  stormEnd: string;
  stormStart: string;
  summaries: EtrSummaryStats[];
}

export interface EtrOutageOverview {
  cumulativeOutage: number;
  cumulativeRestoredOutage: number;
  activeOutage: number;
  restoredOutage: number;
  forecastDateTime: string;
  historical: boolean;
  scope?: string;
}

export interface ChartDataPoint {
  date: string;
  group: string;
  isFilled?: boolean;
  isHighlighted?: boolean;
  value: number | string;
}

export interface PieChartDataPoint {
  group: string;
  value: number;
}

export interface SessionEtrResponse {
  forecastMetadata: { [key: string]: any };
  generationTime: string;
  regionalETRs: RegionalEtr[];
  sessionId: string;
  stormStartDate: string;
  systemResources: Record<string, number>;
  systemRestoration: {
    high: string;
    low: string;
    medium: string;
  };
}

export interface EtrSimulationItem {
  projectedETR: string;
  simulation: boolean;
  totalResources: number;
}

export interface SimulationSession {
  sessionId: string;
  sessionType: string;
  triggeredBy: string;
  stormId: string;
  stormStartDate: string;
  generationTime: string;
}

export interface EtrSimulation {
  stormStartDate: string;
  systemRestoration: {
    high: EtrSimulationItem[];
    low: EtrSimulationItem[];
    medium: EtrSimulationItem[];
  };
}

export interface Feature {
  key: string;
  value: string;
}

export interface RawStormData {
  creationDate: string;
  expirationDate: string | null;
  metadata: Record<string, string | number>;
  stormEndDate: string | null;
  id: string;
  stormName: string;
  stormMode: StormMode;
  displayName: string;
  stormStartDate: string | null;
  generationType: 'system' | 'manual';
  projectedRegionalRestoration: Record<string, string>;
  projectedETA: string | null;
}

export interface StormData {
  creationDate: Date | null;
  displayName: string;
  expirationDate: Date | null;
  generationType: 'system' | 'manual';
  id: string;
  metadata: Record<string, any>;
  projectedETA: string | null;
  projectedETR: string | null; // Derived Attr
  projectedRegionalRestoration: Record<string, string>;
  stormEndDate: Date;
  stormMode: StormMode;
  stormName: string;
  stormStartDate: Date;
  stormDuration: number | null; // Derived Attr
}

export interface RetrospectiveStorm extends StormData {
  totalCumulativeIncidents: number;
  totalCumulativeCustomersAffected: number;
  windGustThresholds: WindGustStats;
  windGustKmh: number;
  averageWindSpeed: number;
  outageRecords: RetrospectiveRegionSummary[];
  similarStorms: RetrospectiveStorm[];
}

export interface OutageRecord {
  activeCustomerOutages: number;
  activeIncidents: number;
  potentialMomentaryCustomerOutages: number | null;
  potentialMomentaryIncidents: number | null;
  region: string;
  restoredCustomerOutages: number;
  restoredIncidents: number;
}

export interface OutagesOverview {
  outageRecord: {
    System: {
      activeCustomersAffected: number;
      activeOutage: number;
      cumulativeCustomersAffected: number;
      cumulativeOutage: number;
      cumulativeRestoredCustomersAffected: number;
      cumulativeRestoredOutage: number;
      forecastDateTime: string;
      historical: boolean;
      restoredCustomersAffected: number;
      restoredOutage: number;
    }[];
  };
}

export interface CustomOutagesData extends OutagesData {
  outageRecord: OutageRecord;
}
export interface OutagesData {
  createdTimestamp: string;
  generatedBy: string;
  id: string;
  lastUpdated: string;
  outageRecords: OutageRecord[];
  sessionId: { date: string; timestamp: string };
}

export interface TerritorySimChartData {
  estimatedAssessment: null;
  estimatedRestoration: string;
  isSimulation: boolean;
  territoryName: string;
  totalResources: number;
  userSimulation?: boolean;
}

export interface UnifiedEtrSimData {
  id: string;
  isSimulation: boolean;
  projectedETR: string;
  totalResources: number;
}

export interface Territory {
  territoryId: string;
  territoryName: string;
}

export interface MetricResponse {
  lastUpdated: string;
  metricType:
    | 'medium-predicted-restorations'
    | 'outages'
    | 'restorations'
    | 'resources'
    | 'cumulative-outages';
  metrics: Metric[];
  stormData: StormOverview;
  stormId: { date: string; timestamp: string };
}

export interface AggregateMetricRecord {
  cumulativeAffectedCustomers: number;
  cumulativeCustomersRestored: number;
  cumulativeIncidents: number;
  cumulativeRestorations: number;
  restoredPerWorker: number;
  timestamp: string;
  totalAffectedCustomerRestorations: number;
  totalAffectedCustomers: number;
  totalIncidents: number;
  totalResources: number;
  totalRestorations: number;
  uniqueAffectedCustomers: number;
  uniqueIncidentsCount: number;
  windGustThresholds: WindGustStats;
  windGustKmh: number;
  averageWindSpeed: number;
  totalRawIncidents: number | null;
  totalRawAffectedCustomers: number | null;
}

export interface AggregateMetricRecordWithRegion extends AggregateMetricRecord {
  region: string;
}

export interface AggregateMetricResponse {
  endTime: string;
  startTime: string;
  regionalMetrics: Record<string, AggregateMetricRecord[]>;
}

export interface Metric {
  records: MetricRecord[];
  timestamp: string;
  sessionId?: string;
}

export interface MetricRecord {
  activeCustomerOutages?: number;
  activeIncidents?: number;
  hoursToRestoration?: number;
  region: string;
  restoredCustomerOutages?: number;
  restoredIncidents?: number;
  cumulativeCustomerOutages?: number;
  cumulativeIncidents?: number;
  cumulativeRestoredCustomerOutages?: number;
  cumulativeRestoredIncidents?: number;
  type?: 'result';
  resources?: Record<string, number>;
  totalWorkers?: number;
}

export interface DailyEvent {
  date: string;
  eventTypes: {
    'Damaged Pole': number;
    Emergency: number;
    'Non Outage': number;
    Outage: number;
    'Service Hazard Wire Down': number;
  };
  outages: number;
  customersAffected: number;
  territory: string;
}

export interface ImageConfig {
  dark: string;
  light: string;
  darkSmall?: string;
  lightSmall?: string;
  label: string;
}

export interface InfoBoxConfig {
  image: ImageConfig;
  label: string;
}

export interface Link {
  url: string;
  label: string;
}

export interface LinksListConfig {
  items: Link[];
}

export interface TableConfig {
  columns: ColumnConfig[];
}

export interface ValidationsConfig {
  validations: string[];
}

export interface SelectItem {
  id: string;
  text: string;
  checked?: boolean;
}

export interface ChartsConfig {
  label: string;
  items: SelectItem[];
}

export interface HourlyChartSelectItem {
  value: string;
  text: string;
  yAxis: string;
  unit: string;
  precision: number;
  category: string;
}

export interface HourlyChartsConfig {
  label: string;
  items: HourlyChartSelectItem[];
}

interface NavigationItem {
  header: string;
  items: MenuItem[];
}

interface ExternalNavigationItem {
  items: ExternalMenuItem[];
}

interface ExternalMenuItem {
  name: string;
  icon: string;
  route: string;
}

interface MenuItem {
  name: string;
  icon: string;
  route: string;
  isExact: boolean;
  isSplash: boolean;
  disabled: boolean;
}
export interface NavigationItemsConfig {
  items: NavigationItem[];
}

export interface ExternalNavigationItemsConfig {
  items: ExternalNavigationItem[];
}

export interface MapItems {
  items: MapItem[];
}

export interface MapItem {
  default: boolean;
  name: string;
  legendName: string;
  steps: number[] | string[];
  attributeName: Visibility;
  icon: string;
  lightColorLegend?: string[];
  darkColorLegend?: string[];
  source: OutageSource;
}

export interface MapStyleOption {
  label: string;
  value: MapStyle;
  imgSrc: string;
}

export interface ActiveMapRegionProps {
  key: string;
  region: string;
  layer: Visibility;
  value: number;
}

export interface RadarData {
  time: number;
  path: string;
}

interface Radar {
  past: RadarData[];
  nowcast: RadarData[];
}

export interface FireData
  extends GeoJSON.FeatureCollection<
    GeoJSON.Point,
    {
      id: string;
      name?: string;
      intensity?: number;
      source?: string;
      timestamp?: string;
      [key: string]: any;
    }
  > {}

export interface WeatherData {
  version: string;
  generated: number;
  host: string;
  radar: Radar;
}

interface WindGustStats {
  over20mph: number;
  over30mph: number;
  over40mph: number;
}

export interface RegionSummary {
  totalCumulativeIncidents: number;
  totalCumulativeCustomersAffected: number;
  windGustThresholds: WindGustStats;
  windGustKmh: number;
  averageWindSpeed: number;
}

export interface RetrospectiveRegionSummary extends RegionSummary {
  region: string;
}

interface StormOverview {
  startDate: string;
  endDate: string;
  name: string;
}

export interface StormRetrospectiveData {
  stormId: string;
  stormOverview: StormOverview;
  stormEndSummary: Record<string, RegionSummary>;
  similarStorms: Record<string, number>[];
}

export interface StormRetrospectivesResponse {
  retrospectives: StormRetrospectiveData[];
}

export interface DailyHourlyValue {
  hour: number;
  date: Date;
  targetDate: string | Date;
  midnightOutages: number;
  midnightCustomersAffected: number;
  totalOutagesSum: number | null;
  totalOutages: number;
  totalCustomersAffectedSum: number;
  predictionUpperConfidenceBoundSum: number;
  predictionLowerConfidenceBoundSum: number;
  predictionUpperConfidenceBoundCustomersAffectedSum: number;
  predictionLowerConfidenceBoundCustomersAffectedSum: number;
}

interface DailyZoneValue {
  day: string;
  values: DailyHourlyValue[];
}

export interface DailyPredictedSessionData {
  session: string;
  date: string;
  totalOutagesMin: number;
  totalCustomersAffectedMin: number;
  predictionUpperConfidenceBoundMin: number;
  predictionLowerConfidenceBoundMin: number;
  predictionUpperConfidenceBoundCustomersAffectedMin: number;
  predictionLowerConfidenceBoundCustomersAffectedMin: number;
  totalOutagesMax: number;
  totalCustomersAffectedMax: number;
  predictionUpperConfidenceBoundMax: number;
  predictionLowerConfidenceBoundMax: number;
  predictionUpperConfidenceBoundCustomersAffectedMax: number;
  predictionLowerConfidenceBoundCustomersAffectedMax: number;
  dailyHourlyValues: DailyZoneValue[];
}

export interface CustomTooltipRendererParams extends TooltipRendererParams {
  datum: {
    [key: string]: any;
    min: number;
    max: number;
  };
  xKey: string;
  yKey: string;
  color: string;
  title: string;
}

interface ZonePredictionAttributes {
  hour: string;
  targetTime: string;
  hourlyPredictionAttributes: {
    weatherAttributes: ZoneWeatherAttributes;
    outageAttributes: ZoneOutageAttributes;
  };
}

export interface ZoneWeatherAttributes {
  totalPrecip: number;
  windGustMax: number;
  windSustained: number;
  temperatureMin: number;
  temperatureMean: number;
  temperatureMax: number;
  threatLevel: number;
  threatLevelCategory: string;
  cloudCoverPercentMean: number;
}

export interface RawWeatherData {
  timestamp: string;
  weatherStations: Record<string, WeatherStation>;
  weatherStationMetadata: Record<string, any>;
}

export interface ZoneOutageAttributes {
  minOutagesPrediction: number;
  maxOutagesPrediction: number;
  pointOutagePrediction: number;
  minCustomersAffectedPrediction: number;
  maxCustomersAffectedPrediction: number;
  pointCustomersAffectedPrediction: number;
}

export interface RegionData {
  region: string;
  totalPrecip: number;
  windGustMaxHigh: number;
  windGustMaxLow: number;
  averageWindSustained: number;
  temperatureLow: number;
  temperatureHigh: number;
  minOutages: number;
  maxOutages: number;
  minCustomersAffected: number;
  maxCustomersAffected: number;
  cumulativeOutages: number;
  cumulativeCustomersAffected: number;
}

export interface HourlyWeatherAttributes {
  totalPrecip: number;
  precipMin: number;
  precipMax: number;
  windGustMaxHigh: number;
  windGustMaxLow: number;
  averageWindSustained: number;
  temperatureLow: number;
  temperatureHigh: number;
  threatLevelMax: number;
  cloudCoverPercentMean: number;
}

export interface HourlyOutageAttributes {
  minOutages: number;
  maxOutages: number;
  minCustomersAffected: number;
  maxCustomersAffected: number;
  cumulativeOutages: number;
  cumulativeCustomersAffected: number;
  cumulativeMinOutages: number;
  cumulativeMaxOutages: number;
  cumulativeMinCustomersAffected: number;
  cumulativeMaxCustomersAffected: number;
}

export interface HourlyForecast {
  sessionId: string;
  timestamp: string;
  startHour: number;
  endHour: number;
  zoneSummaryAttributes: RegionData[];
  summaryAttributes: {
    weatherAttributes: HourlyWeatherAttributes;
    outageAttributes: HourlyOutageAttributes;
  };
  zonePredictions: {
    zone: string;
    hourlyZonePredictions: ZonePredictionAttributes[];
  }[];
}

export interface PieChartTooltipParams {
  color: string;
  datum: { [key: string]: any };
  calloutLabelKey: string;
  angleKey: string;
}

export interface LineChartTooltipParams {
  color: string;
  datum: { [key: string]: any };
  yName: string;
  xKey: string;
  yKey: string;
  title: string;
}

export interface SimulatedRegion {
  territoryName: string;
  resources: {
    companyAssessmentWorkers: number;
    companyRepairWorkers: number;
    contractorAssessmentWorkers: number;
    contractorRepairWorkers: number;
  };
  isSimulation: boolean;
  totalResources: number;
  estimatedRestoration: string;
  estimatedAssessment: string;
  forecastCustomerOutages: number;
  forecastOutages: number;
}

export type SimulationTime = {
  [key in RegionalEtrLevel]: {
    estimatedRestoration: string;
    estimatedAssessment: string;
  };
};

export type ViewLevel = 'system' | 'pd-area' | 'workgroup';

export interface DeltaAmiMeterData {
  meterSessions: DeltaMeterSession[];
}

export interface DeltaMeterSession {
  timestamp: string;
  meters: Record<string, number>;
}

export interface CardValidationsConfig<T> {
  items: CardValidation<T>[];
}
interface CardValidation<T> {
  label: string;
  field: T;
}

export interface Crew {
  initialTerritory: string;
  assignedTerritoryId: string;
  roleAllocations: { role: string; quantity: number }[];
  resourceType: string;
  arrivalTime: string;
}

export interface ResourceVisualization {
  stormId: string;
  resourceCounts: {
    resourceActivationTime: string;
    regionalResourceCount: {
      [key: string]: number;
    };
  }[];
}

export interface ActiveCrew {
  stormId: string;
  id: string;
  currentTerritoryId: string;
  initialTerritory: string;
  assignedTerritoryId: string;
  totalResources: number;
  status: string;
  resources: {
    type: string;
    roleAllocations: {
      role: string;
      quantity: number;
    }[];
  };
  arrivalTime: string;
  releaseTime: string;
  toTerritoryId: string;
}

export interface ForecastAnalog {
  generationTime: string;
  parentZone: string;
  forecastedEventId: string;
  stormAnalogs: StormAnalog[];
}

export interface StormAnalog {
  forecastedEventId: string;
  targetStartTime: string;
  targetEndTime: string;
  similarEvents: number;
  metrics: AnalogMetrics;
  analogEvents: AnalogEvent[];
}

interface AnalogMetrics {
  outageCount: number;
  eventDuration: number;
  windSustainedMean: number;
  precipMax: number;
  windGustMax: number;
  temperatureMean: number;
  threatLevelMax: number;
}

export interface AnalogEvent {
  startTime: string;
  endTime: string;
  outageCount: number;
  metrics: AnalogMetrics;
}

export type ToastMessage = {
  id: number;
  type: 'success' | 'error' | 'info';
  dismissible: boolean;
  timeout?: number;
  message: string;
};

export interface WeatherForecast {
  id: string;
  timestamp: string;
  forecastHour: number;
  weatherStations: Record<string, WeatherStation>;
  weatherStationMetadata: Record<string, any>;
}

export interface WeatherStation {
  stationId: string;
  latitude: number;
  longitude: number;
  region: string;
  workGroup: string;
  weatherAttributes: {
    temperatureCelsius: number;
    humidityPercent: number;
    windBearingDeg: number;
    windGustMph: number;
    windSpeedMph: number;
  };
  windSpeed: number;
  windBearing: number;
}

export interface SystemStateSnapshot {
  timestamp: string;
  systemState: {
    stormId: string | null;
    creationDate: string;
    aggregatedResources: Record<string, number>;
    regionalResourceStates: {
      territoryId: string;
      territoryName: string;
      resources: Record<string, number>;
    }[];
  };
}

export interface SystemStateHistory {
  historicalSystemStates: SystemStateSnapshot[];
}

export interface CrewEntry {
  arrivalDate: DateTime;
  arrivalTime: string;
  assignedTerritory: string;
  initialTerritory: string;
  resourceType: string;
  roleAllocations: { role: string; quantity: number }[];
}

export interface IColDef extends ColDef {
  preventExport?: boolean;
  propertyType?: 'number' | 'string' | 'time' | 'date' | 'datetime';
}

export interface ChartSeries {
  tooltip: {
    renderer: (
      params: LineChartTooltipParams,
      precision?: number,
      unit?: string,
      color?: string
    ) => string;
  };
  type: string;
  xKey: string;
  marker?: { fill: string; stroke: string };
  stroke?: string;
  yKey: string;
  yName: string;
}
