import { get } from 'svelte/store';
import type { ThemeConfig } from '$lib/types';
import { isDarkMode } from '$lib/stores/theme';

export function onToggleTheme() {
  const isDark = get(isDarkMode);
  const newTheme = isDark ? 'light' : 'dark';
  document.querySelector('html')?.classList.toggle('dark', !isDark);
  localStorage.setItem('theme', newTheme);
  isDarkMode.set(!isDark);
}

export function setTheme() {
  const themeValue = localStorage.getItem('theme');
  if (themeValue === 'light') {
    document.querySelector('html')?.classList.remove('dark');
    isDarkMode.set(false);
  }
}

export function setCustomTheme(theme: Record<string, ThemeConfig>, isDarkMode: boolean) {
  const root = document.documentElement;

  const mode = isDarkMode ? theme.dark : theme.light;
  const properties: (keyof ThemeConfig)[] = [
    'colorPrimary',
    'colorSecondary',
    'colorSidebar',
    'colorNavActive'
  ];
  const cssVariables = [
    '--color-primary',
    '--color-secondary',
    '--color-sidebar-background',
    '--color-nav-active'
  ];

  properties.forEach((property, index) => {
    root.style.setProperty(cssVariables[index], mode[property] as string);
  });
}
