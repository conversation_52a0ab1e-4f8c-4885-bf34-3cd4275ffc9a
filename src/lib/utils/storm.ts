import type { AggregateMetricRecord, RawStormData, StormData } from '$lib/types';

export function formatStormData(data: RawStormData): StormData {
  let projectedETR = null;

  if (data.projectedRegionalRestoration && Object.keys(data.projectedRegionalRestoration).length) {
    projectedETR = Object.keys(data.projectedRegionalRestoration)
      .map((key) => data.projectedRegionalRestoration[key])
      .sort((a, b) => b.localeCompare(a))[0];
  }

  return {
    ...data,
    projectedETR,
    stormStartDate: data.stormStartDate ? new Date(data.stormStartDate) : new Date(),
    expirationDate: data.expirationDate ? new Date(data.expirationDate) : null,
    stormEndDate: data.stormEndDate ? new Date(data.stormEndDate) : new Date(),
    creationDate: data.creationDate ? new Date(data.creationDate) : null,
    stormDuration:
      data.stormEndDate && data.stormStartDate
        ? new Date(data.stormEndDate).getTime() - new Date(data.stormStartDate).getTime()
        : null
  };
}

export function formatOverviewData(
  data: AggregateMetricRecord[] | undefined
): AggregateMetricRecord[] {
  if (!data) {
    return [];
  }
  return data.map((i) => ({ ...i, date: new Date(i.timestamp) }));
}
