export function capitalize(text: string | null) {
  return text ? text[0].toUpperCase() + text.slice(1).toLowerCase() : '';
}

export function titleCase(text: string | null) {
  if (text) {
    return text
      .toLowerCase()
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }
  return '';
}

export function toCapitalizedWords(text: string | null) {
  if (!text) {
    return '';
  }

  const words = text.match(/[A-Za-z][a-z]*/g) || [];

  return words.map(capitalize).join(' ');
}

export function filterNonNull<T>(obj: Record<string, T>): Record<string, T> {
  return Object.fromEntries(
    Object.entries(obj).filter(([, value]) => value != null && value !== '')
  );
}
