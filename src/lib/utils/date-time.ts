import { DateTime } from 'luxon';

export function validateDate(value: string) {
  const regex = new RegExp(/^\d{2}\/\d{2}\/\d{4}$/);
  return regex.test(value);
}

export function validateTime(value: string) {
  const regex = new RegExp(/^(0[1-9]|1[0-2]):[0-5][0-9]$/);
  return regex.test(value);
}

export function validatePeriod(value: string) {
  const period = value.toUpperCase();
  return period === 'AM' || period === 'PM';
}

export function formatDuration(milliseconds: number) {
  const isNegative = milliseconds < 0;
  if (isNegative) {
    milliseconds = Math.abs(milliseconds);
  }

  const seconds = Math.round(milliseconds / 1000);
  const minutes = Math.round(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  const remainingHours = hours % 24;
  const remainingMinutes = minutes % 60;

  const parts: string[] = [];

  if (days > 0) {
    parts.push(`${days} day${days > 1 ? 's' : ''}`);
  }

  if (remainingHours > 0) {
    parts.push(`${remainingHours} hour${remainingHours > 1 ? 's' : ''}`);
  }

  if (remainingMinutes > 0) {
    parts.push(`${remainingMinutes} minute${remainingMinutes > 1 ? 's' : ''}`);
  }

  if (parts.length === 0) {
    return '';
  }

  const result = parts.join(', ');
  return isNegative ? `- ${result}` : result;
}

export function isDataTimestampOutdated(value: string | undefined, duration: number = 1) {
  if (!value) {
    return false;
  }

  return Math.abs(DateTime.fromISO(value).diffNow('hours').hours) > duration;
}

export const dayAbbrMap: { [key: number]: string } = {
  1: 'MO',
  2: 'TU',
  3: 'WE',
  4: 'TH',
  5: 'FR',
  6: 'SA',
  7: 'SU'
};
