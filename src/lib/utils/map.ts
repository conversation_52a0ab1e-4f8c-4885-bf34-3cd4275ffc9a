import mapboxgl, { type Map } from 'mapbox-gl';
import type { AggregateMetricRecordWithRegion } from '../types';

export function getClosestTimestamp(
  systemOverviewData: Record<string, AggregateMetricRecordWithRegion[]>,
  currentFrameTime: number
) {
  let closestTimestamp = null;
  let minDiff = Number.MAX_SAFE_INTEGER;

  for (const timestamp in systemOverviewData) {
    const diff = Math.abs(+timestamp - currentFrameTime);

    if (diff < minDiff) {
      minDiff = diff;
      closestTimestamp = timestamp;
    }
  }

  return closestTimestamp;
}

export function fitMapToBounds(
  map: Map | undefined,
  maxBounds: [[number, number], [number, number]] | undefined
) {
  if (!maxBounds || !map) return;

  const bounds = new mapboxgl.LngLatBounds(maxBounds);

  map.fitBounds(bounds, {
    padding: 10,
    maxZoom: 14,
    animate: false
  });
}
