export const roundFloat = (num: number, charts = 2) =>
  Math.round((num + Number.EPSILON) * 10 ** charts) / 10 ** charts;

export function formatNumbers(value: number | string | undefined | null, precision = 0) {
  if (typeof value == 'number' && !Number.isNaN(value)) {
    return Intl.NumberFormat('en-US', { maximumFractionDigits: precision }).format(value);
  } else {
    return '-';
  }
}
export function formatRange(
  value1: number | null | undefined,
  value2: number | null | undefined,
  precision = 0
): string {
  if (!Number.isFinite(value1) || !Number.isFinite(value2)) {
    return '-';
  }

  const formattedValue1 = (value1 ?? 0).toFixed(precision);
  const formattedValue2 = (value2 ?? 0).toFixed(precision);

  return formattedValue1 === formattedValue2
    ? formatNumbers(value1, precision)
    : `${formatNumbers(value1, precision)} - ${formatNumbers(value2, precision)}`;
}
