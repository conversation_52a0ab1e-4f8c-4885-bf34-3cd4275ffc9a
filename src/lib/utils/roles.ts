import store from '$lib/stores/app.svelte';

export function validateRoleRouteAccess(route: string) {
  const authInstance = store.authStore;
  const config = store.roleConfig;

  // Validate - not a Keycloak instance
  if (!authInstance) return true;

  // Invalidate - role config value is null
  if (!config) return false;

  const validRoles = config.routeAccess?.[route]?.whitelist;

  // Validate - no route access information
  if (!validRoles) return true;

  const userRoles = authInstance.keycloak.realmAccess?.roles ?? [];

  // Check if one of the userRoles is a part of the validRoles
  return userRoles.some((role) => validRoles.includes(role));
}

export function getInvalidRouteTooltip(route: string): string {
  return store.roleConfig?.routeAccess?.[route]?.tooltip ?? '';
}
