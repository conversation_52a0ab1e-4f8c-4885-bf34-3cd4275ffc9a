import { formatNumbers } from './number';

export function getDonutSumAmount(data: { value: number }[]): number {
  return data.reduce((res, item) => res + item.value, 0);
}

export function getPercentage(data: { value: number }, dataList: { value: number }[]): string {
  const sum = getDonutSumAmount(dataList);

  if (sum) {
    return formatNumbers((data.value * 100) / sum, 0) + '%';
  }

  return '100%';
}
