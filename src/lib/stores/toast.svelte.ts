import type { ToastMessage } from '$lib/types';

const MAX_ID: number = 10000;

class ToastService {
  public toasts = $state<ToastMessage[]>([]);

  public addToast(toast: Partial<Omit<ToastMessage, 'id'>> & { message: string }) {
    const id = Math.floor(Math.random() * MAX_ID);
    const defaults: ToastMessage = {
      id,
      type: 'info',
      dismissible: true,
      timeout: 5000,
      message: ''
    };
    const newToast = { ...defaults, ...toast };
    this.toasts = [newToast, ...this.toasts];
    if (newToast.timeout) setTimeout(() => this.dismissToast(id), newToast.timeout);
  }

  public dismissToast(id: number) {
    this.toasts = this.toasts.filter((t) => t.id !== id);
  }
}

export default new ToastService();
