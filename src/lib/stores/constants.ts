import { env } from '$env/dynamic/public';
import { DateTime } from 'luxon';

export const authenticationMethod: string = env.PUBLIC_CLIENT_AUTHORIZATION_METHOD;
export const client: string = env.PUBLIC_CLIENT_CODE;
export const isForecast: boolean = env.PUBLIC_ETR_FORECASTING_ENABLED === 'true';
export const isForecastRange: boolean = env.PUBLIC_FORECASTING_SHOW_MEDIAN_OUTAGE_COUNT === 'true';
export const isNotificationSettingsEnabled: boolean =
  env.PUBLIC_IS_NOTIFICATION_SETTINGS_ENABLED === 'true';
export const isSystemic: boolean = env.PUBLIC_IS_SYSTEMIC === 'true';
export const showActiveResources: boolean = env.PUBLIC_SHOW_ACTIVE_RESOURCES === 'true';
export const showAmiMap: boolean = env.PUBLIC_SHOW_AMI_MAP === 'true';
export const showDailyMapWindAnimation: boolean =
  env.PUBLIC_SHOW_DAILY_MAP_WIND_ANIMATION === 'true';
export const showEta: boolean = env.PUBLIC_SHOW_ETA === 'true';
export const showEtrMapWindAnimation: boolean = env.PUBLIC_SHOW_WIND_ANIMATION === 'true';
export const showForecastAnalogs: boolean = env.PUBLIC_SHOW_FORECAST_ANALOGS === 'true';
export const showForecastingEvents: boolean = env.PUBLIC_FORECASTING_SHOW_EVENTS === 'true';
export const showMapWeatherComponents: boolean = env.PUBLIC_SHOW_MAP_WEATHER_COMPONENTS === 'true';
export const showOutagesBanner: boolean = env.PUBLIC_SHOW_OUTAGES_BANNER === 'true';
export const showPointForecast: boolean = env.PUBLIC_SHOW_POINT_FORECAST === 'true';
export const showProbabilisticForecast: boolean = env.PUBLIC_SHOW_PROBABILISTIC_FORECAST === 'true';
export const showUnconfirmedOutages: boolean = env.PUBLIC_SHOW_UNCONFIRMED_OUTAGES === 'true';
export const timezone: string = env.PUBLIC_TIMEZONE;
export const timezoneAbbr: string = DateTime.now().setZone(timezone).toFormat('ZZZZ');
export const useMilitaryTime: boolean = env.PUBLIC_USE_MILITARY_TIME === 'true';
export const warningMessage: string | undefined = env.PUBLIC_WARNING_MESSAGE;
export const showHistoricalStormModal: boolean =
  env.PUBLIC_IS_HISTORICAL_STORM_MODAL_ENABLED === 'true';
