import type { KeycloakAuthorization } from '$lib/security/keycloak-auth';
import type {
  ActiveMapRegionProps,
  AppConfig,
  ComponentsConfigMap,
  EventLevel,
  FeatureFlag,
  NotificationConfig,
  Region,
  ThemeConfig
} from '$lib/types';
import { capitalize } from '$lib/utils';

class Store {
  // Authentication
  // TODO: Needs refactor to have email/userId
  public authenticatedAzureUser = $state<string | null | undefined>(null); // Azure
  public authStore = $state<KeycloakAuthorization | null>(null); // Keycloak
  public isAuthenticated = $state<boolean>(false);

  // Config
  public appConfig = $state<AppConfig | null>(null);
  public componentsConfig = $state<ComponentsConfigMap | null>(null);
  public displayConfig = $state<Record<string, any> | null>(null);
  public notificationConfigs = $state<NotificationConfig[]>([]);
  public roleConfig = $state<Record<string, any> | null>(null);
  public themeConfig = $state<Record<string, ThemeConfig> | null>(null);

  // Feature Flags
  public featureFlags = $state<Record<FeatureFlag, boolean> | null>(null);

  // General
  public activeMapRegion = $state<ActiveMapRegionProps | null>(null);
  public eventLevels = $state<EventLevel[]>([]);
  public regions = $state<Region[]>([]);
  public selectedWarningColor = $state<string | null | undefined>(null);
  public territories = $state<Region[]>([]);

  // Derived
  public outageLabel = $derived<string>(this.displayConfig?.outageLabel?.displayName || 'outage');
  public capitalizedOutagesLabel = $derived<string>(`${capitalize(this.outageLabel)}s`);
  public customersAffectedLabel = $derived<string>(
    capitalize(this.displayConfig?.customersAffectedLabel?.displayName) || 'Affected'
  );
  public etrLabel = $derived<string>(
    this.displayConfig?.etrLabel?.displayName?.toUpperCase() || 'ETR'
  );
  public regionsLabel = $derived<string>(
    this.displayConfig?.regionsLabel?.displayName || 'Regions'
  );
}

export default new Store();
