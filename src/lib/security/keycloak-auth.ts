import Keycloak from 'keycloak-js';
import type { KeycloakConfig, KeycloakProfile } from 'keycloak-js';

/**
 * Class which establishes a connection to Keycloak via the Keycloak Adapter library.
 */
/* eslint-disable @typescript-eslint/no-magic-numbers */
export class KeycloakAuthorization {
  /** Keycloak instance for integrating SSO */
  public keycloak!: Keycloak;

  /** Active user profile established once application has authenticated with Keycloak */
  public user!: KeycloakProfile;

  /** Whether or not the current user has been authenticated through Keycloak */
  public authenticated = false;

  /** Threshold of time (in seconds) remaining until an access token will be invalidated and refreshed. */
  private MIN_VALIDITY_SECONDS_TO_REFRESH = 15;

  /**
   * Initializes the Keycloak instance for the client.
   * @param config KeycloakConfig which is used to initialize a Keycloak instance.
   */
  constructor(config: KeycloakConfig) {
    this.keycloak = new Keycloak(config);
  }

  /**
   * Fully initializes the Keycloak instance and attempts to establish an authenticated connection
   * via the Keycloak adapter.  Must be called independently after a Keycloak instance has been instantiated
   * via the constructor.  This function must be asynchronous due to the fact that we must first wait
   * for the authenticated session to be established before performing web requests.
   */
  public async initializeSession() {
    this.keycloak.onAuthLogout = () => {
      this.authenticated = false;
      this.keycloak.login();
    };

    this.keycloak.onTokenExpired = () => {
      this.refreshToken();
    };

    // Initiate authentication with the Keycloak server
    const authenticated = await this.keycloak.init({
      onLoad: 'login-required',
      checkLoginIframe: false
    });
    this.authenticated = authenticated;

    if (authenticated) {
      await this.keycloak.updateToken(this.MIN_VALIDITY_SECONDS_TO_REFRESH);
      const user = await this.keycloak.loadUserProfile();
      this.user = user;
    }
  }

  /**
   * Function which can be called to sign the user in manually.
   */
  public async signIn() {
    await this.initializeSession();
  }

  public async signOut() {
    this.authenticated = false;
    await this.keycloak.logout();
  }
  /**
   * Function which can be called to refresh user access token manually.
   */
  public async refreshToken() {
    try {
      await this.keycloak.updateToken(this.MIN_VALIDITY_SECONDS_TO_REFRESH);
    } catch (error) {
      this.authenticated = false;
    }
  }
}
