<script lang="ts">
  import { Input, Tooltip } from '$lib/components';
  import { onMount, onDestroy } from 'svelte';

  interface Props {
    class?: string;
    value: string;
    onChange: (value: string) => void;
  }

  let { class: className = '', value = $bindable(''), onChange }: Props = $props();

  let isEditing = $state<boolean>(false);
  let textInputRef = $state<HTMLDivElement>();

  onMount(() => {
    document.addEventListener('click', handleClickOutside);
  });

  onDestroy(() => {
    document.removeEventListener('click', handleClickOutside);
  });

  function handleClickOutside(event: MouseEvent) {
    if (isEditing && textInputRef && !textInputRef.contains(event.target as Node)) {
      isEditing = false;
      submit();
    }
  }

  function submit() {
    if (value) {
      isEditing = false;
      onChange(value);
    }
  }
</script>

{#if isEditing}
  <div class="input-dark w-full {className}" bind:this={textInputRef}>
    <Input required bind:value onblur={submit} />
  </div>
{:else}
  <div
    class="editable-field w-fit {className}"
    role="button"
    tabindex="0"
    ondblclick={() => (isEditing = true)}
  >
    {value}
    <Tooltip>
      {#snippet content()}
        Double-click to edit
      {/snippet}
      {#snippet trigger()}
        <span class="material-icons icon text-link cursor-pointer">edit</span>
      {/snippet}
    </Tooltip>
  </div>
{/if}

<style>
  :global(.input-dark input) {
    background: var(--color-component);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    box-shadow: 0 0 0.5rem rgba(0, 0, 0, 0.33);
    box-sizing: border-box;
    color: var(--color-text);
    font-size: calc(var(--size-xs) * 6);
    padding: 1rem;
    position: relative;
    width: 100%;
  }

  .text-link {
    position: relative;
    top: 3px;
  }
</style>
