<script lang="ts">
  import { AgChart, Table } from '$lib/components';
  import { Card } from '$lib/components/ui/card';
  import { formatNumbers } from '$lib/utils';
  import {
    ETR_CHART_SPECIAL_MARKERS,
    getColorScale,
    getLineChartSeriesItems
  } from '$lib/config/etr';
  import { getEtrSimColumns } from '$lib/config/table';
  import { isDarkMode } from '$lib/stores/theme';
  import store from '$lib/stores/app.svelte';
  import { isSystemic } from '$lib/stores/constants';
  import type { ChartDataPoint, UnifiedEtrSimData } from '$lib/types';
  import type { AgAxisLabelFormatterParams } from 'ag-charts-enterprise';

  type RowData = {
    id: string;
    [category: string]: string;
  };

  interface Props {
    chartData?: ChartDataPoint[];
    etrResults?: UnifiedEtrSimData[];
  }

  let { chartData = [], etrResults = [] }: Props = $props();

  let colorScale = $derived({
    ...getColorScale(chartData, $isDarkMode, isSystemic),
    [ETR_CHART_SPECIAL_MARKERS.currentResources]: 'transparent'
  });
  let columnDefs = $derived(etrResults.length ? getEtrSimColumns(etrResults) : []);
  let rowData = $derived(etrResults.length ? getRowData(etrResults) : []);

  function getRowData(etrResults: UnifiedEtrSimData[]) {
    const rows: RowData[] = [];
    const categoryMap: { [id: number]: RowData } = {};
    etrResults.forEach(({ id, projectedETR, totalResources }) => {
      if (!categoryMap[totalResources]) {
        categoryMap[totalResources] = { id: String(totalResources) };
        rows.push(categoryMap[totalResources]);
      }
      categoryMap[totalResources][id] = projectedETR;
    });
    return rows.sort((a, b) => Number(a.id) - Number(b.id));
  }
</script>

<div class="etr-simulations flex gap-3 flex-row">
  <Card class="etr-card flex flex-col flex-1">
    <div class="flex gap-2 mb-3 items-center">
      <div class="w-[18px] h-[18px] bg-primary"></div>
      <span class="font-bold text-xs">Actual Resources</span>
    </div>

    <Table class="etr-simulation-table" {columnDefs} domLayout="normal" {rowData} />
  </Card>

  <Card class="etr-card flex w-1/3 !h-[600px]">
    <AgChart
      canExpand={true}
      className="w-full"
      data={chartData}
      options={{
        axes: [
          {
            position: 'bottom',
            type: 'number',
            title: { text: 'Total Workers' }
          },
          {
            position: 'left',
            type: 'number',
            title: { text: 'Hours to Restoration' },
            label: {
              formatter: function (params: AgAxisLabelFormatterParams) {
                return formatNumbers(params.value, 2) + 'h';
              }
            }
          }
        ],
        series: getLineChartSeriesItems(chartData, $isDarkMode, colorScale),
        title: { text: `${store.etrLabel} Simulation` }
      }}
    />
  </Card>
</div>

<style>
  :global(.etr-simulation-table .highlighted) {
    background: var(--color-primary);
  }

  @media (max-width: 1461px) {
    :global(.etr-simulations) {
      display: block !important;
      flex-direction: column !important;
    }

    :global(.etr-simulations .etr-card:first-child) {
      height: 450px !important;
      margin-bottom: 1rem;
    }

    :global(.etr-simulations .etr-card) {
      width: 100% !important;
    }
  }
</style>
