<script lang="ts">
  import { Ag<PERSON>hart, ButtonGroup } from '$lib/components';
  import { Card } from '$lib/components/ui/card';
  import { Button } from '$lib/components/ui/button';
  import * as Select from '$lib/components/ui/select';
  import {
    CROSS_AXIS_COLOUR,
    getActiveOutagesChartSeriesItems,
    getWorkerChartSeriesItems,
    tooltipRenderer,
    CHART_LINE_DASH_LENGTH,
    CHART_LINE_DASH_BREAK,
    TIME_SERIES_COLOR,
    customFormatter,
    LINECHART_X_AXIS_FORMAT_HOURLY
  } from '$lib/config/chart';
  import store from '$lib/stores/app.svelte';
  import { Components, EtrChartKeys, type SelectItem, type StormData } from '$lib/types';
  import { DateTime } from 'luxon';
  import type { AgAxisLabelFormatterParams } from 'ag-charts-enterprise';

  const OUTAGE_CHART_ITEMS: SelectItem[] =
    store.componentsConfig?.[Components.ETR_OUTAGES_CHART]?.items ?? [];
  const OUTAGE_CHART_ITEMS_MAP = Object.fromEntries(
    OUTAGE_CHART_ITEMS.map(({ id, text }) => [id, text])
  );
  const WORKER_CHART_ITEMS: SelectItem[] =
    store.componentsConfig?.[Components.ETR_WORKERS_CHART]?.items ?? [];
  const WORKER_CHART_ITEMS_MAP = Object.fromEntries(
    WORKER_CHART_ITEMS.map(({ id, text }) => [id, text])
  );

  interface Props {
    activeStorm?: StormData | null;
    barChartData?: Record<string, any>[];
    isLoadingChartData?: boolean;
    isLoadingLineChartData?: boolean;
    lineChartData?: Record<string, any>[];
    resourcesData?: {
      timestamp: string;
      totalWorkers: number;
      region: string;
      resources: Record<string, number>;
    }[];
    showSystemView?: boolean;
  }

  let {
    activeStorm = null,
    barChartData = [],
    isLoadingChartData = true,
    isLoadingLineChartData = true,
    lineChartData = [],
    resourcesData = [],
    showSystemView = $bindable(true)
  }: Props = $props();

  let barChartSeries = $state<Record<string, any>[]>([]);
  let chartXAxisMax = $state<Date | null>(null);
  let chartXAxisMin = $state<Date | null>(null);
  let lineChartSeries = $state<Record<string, any>[]>([]);
  let selectedView = $state<number>(0);
  let stormCrossLines = $state<Record<string, any>[]>([]);

  let selectedOutageChartItems = $state<SelectItem[]>(OUTAGE_CHART_ITEMS.filter((i) => i.checked));
  let selectedWorkerChartItems = $state<SelectItem[]>(WORKER_CHART_ITEMS.filter((i) => i.checked));

  let selectedOutageChartKeys = $derived<string[]>(selectedOutageChartItems.map((i) => i.id));
  let selectedWorkerChartKeys = $derived<string[]>(selectedWorkerChartItems.map((i) => i.id));

  $effect(() => {
    showSystemView = selectedView === 0;
  });

  $effect(() => {
    if (activeStorm) setStormCrossLines();
  });

  $effect(() => {
    setLineChartSeriesItems(showSystemView, lineChartData, selectedOutageChartItems);
  });

  $effect(() => {
    setBarChartSeriesItems(selectedWorkerChartItems);
  });

  $effect(() => {
    setChartXAxisDomainRange(lineChartData, barChartData);
  });

  function setLineChartSeriesItems(
    isSystemView: boolean,
    data: Record<string, any>[],
    items: SelectItem[]
  ) {
    if (isSystemView) {
      lineChartSeries = getActiveOutagesChartSeriesItems(items);
    } else {
      const yKeys = data
        .map((d) => {
          const keys = Object.keys(d).filter((d) => d !== 'date');
          return keys.length ? keys[0] : null;
        })
        .filter((d) => !!d);
      const uniqueYKeys = [...new Set(yKeys)].sort();
      lineChartSeries = uniqueYKeys.map((d) => ({
        marker: { size: 6 },
        tooltip: { renderer: tooltipRenderer },
        type: 'line',
        xKey: 'date',
        yKey: d,
        yName: d
      }));
    }
  }

  function setBarChartSeriesItems(items: SelectItem[]) {
    barChartSeries = getWorkerChartSeriesItems(items);
  }

  function setStormCrossLines() {
    const options = {
      lineDash: [CHART_LINE_DASH_LENGTH, CHART_LINE_DASH_BREAK],
      stroke: 'red',
      strokeWidth: 2,
      type: 'line',
      label: { text: '' }
    };
    const crossLines = [
      {
        ...options,
        stroke: TIME_SERIES_COLOR,
        value: new Date()
      }
    ];
    if (activeStorm)
      crossLines.push(
        {
          ...options,
          value: DateTime.fromJSDate(activeStorm?.stormStartDate).toJSDate(),
          label: { text: 'Storm Start' }
        },
        {
          ...options,
          value: DateTime.fromJSDate(activeStorm?.stormEndDate).toJSDate(),
          label: { text: 'Storm End' }
        }
      );
    stormCrossLines = crossLines;
  }

  function setChartXAxisDomainRange(
    lineChartData: Record<string, any>[],
    barChartData: Record<string, any>[]
  ) {
    const sortedData: Record<string, any>[] = [
      ...lineChartData,
      ...barChartData,
      ...(activeStorm?.stormEndDate ? [{ date: new Date(activeStorm.stormEndDate) }] : [])
    ].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    if (sortedData.length < 2) return;
    chartXAxisMin = sortedData[0].date;
    chartXAxisMax = sortedData.at(-1)!.date;
  }
</script>

<div class="storm-overview flex flex-col gap-md">
  <Card class="flex flex-col !pb-0 !h-[325px] gap-sm">
    <div
      class="flex items-center flex-wrap gap-4 min-h-[40px] {showSystemView
        ? 'justify-between'
        : 'justify-end'}"
    >
      {#if showSystemView}
        <Select.Root
          type="multiple"
          value={selectedOutageChartKeys}
          onValueChange={(value) => {
            selectedOutageChartKeys = value;
            selectedOutageChartItems = value.map((v) => {
              return { id: v, text: OUTAGE_CHART_ITEMS_MAP[v] };
            });
          }}
        >
          <Select.Trigger class="w-[280px]">
            <div class="flex items-center">
              <Select.SelectionPill
                count={selectedOutageChartItems.length}
                onClear={() => {
                  selectedOutageChartKeys = [];
                  selectedOutageChartItems = [];
                }}
              />
              Chart Settings
            </div>
          </Select.Trigger>
          <Select.Content>
            {#each OUTAGE_CHART_ITEMS as { id, text }}
              <Select.Item value={id}>{text}</Select.Item>
            {/each}
          </Select.Content>
        </Select.Root>
      {/if}

      <ButtonGroup bind:value={selectedView}>
        <Button value={0}>System</Button>
        <Button value={1}>Region</Button>
      </ButtonGroup>
    </div>

    <AgChart
      className="active-outages-chart"
      data={lineChartData}
      options={{
        axes: [
          {
            label: {
              formatter: (value: AgAxisLabelFormatterParams) =>
                customFormatter(value, LINECHART_X_AXIS_FORMAT_HOURLY)
            },
            crossLines: stormCrossLines,
            max: chartXAxisMax,
            min: chartXAxisMin,
            position: 'bottom',
            type: 'time'
          },
          ...(showSystemView &&
          (selectedOutageChartKeys.includes(EtrChartKeys.cumulativeAffectedCustomers) ||
            selectedOutageChartKeys.includes(EtrChartKeys.cumulativeCustomersRestored))
            ? [
                {
                  keys: [
                    EtrChartKeys.cumulativeAffectedCustomers,
                    EtrChartKeys.cumulativeCustomersRestored
                  ],
                  position: 'right',
                  title: {
                    text: 'Customers',
                    color: CROSS_AXIS_COLOUR
                  },
                  type: 'number',
                  label: {
                    color: CROSS_AXIS_COLOUR
                  },
                  tick: {
                    stroke: CROSS_AXIS_COLOUR
                  }
                }
              ]
            : []),
          {
            position: 'left',
            title: {
              text: store.capitalizedOutagesLabel
            },
            type: 'number'
          }
        ],
        series: lineChartSeries,
        legend: { item: { showSeriesStroke: true } },
        title: { text: `Active ${store.capitalizedOutagesLabel}` },
        padding: { right: 50, left: 50 },
        navigator: {
          enabled: true
        }
      }}
      loading={isLoadingChartData || isLoadingLineChartData}
    />
  </Card>

  <Card class="flex flex-col !pb-0 !h-[325px] gap-sm">
    <div class="flex items-center flex-wrap gap-4">
      <Select.Root
        type="multiple"
        value={selectedWorkerChartKeys}
        onValueChange={(value) => {
          selectedWorkerChartKeys = value;
          selectedWorkerChartItems = value.map((v) => {
            return { id: v, text: WORKER_CHART_ITEMS_MAP[v] };
          });
        }}
      >
        <Select.Trigger class="w-[280px]">
          <div class="flex items-center">
            <Select.SelectionPill
              count={selectedWorkerChartItems.length}
              onClear={() => {
                selectedWorkerChartKeys = [];
                selectedWorkerChartItems = [];
              }}
            />
            Chart Settings
          </div>
        </Select.Trigger>
        <Select.Content>
          {#each WORKER_CHART_ITEMS as { id, text }}
            <Select.Item value={id}>{text}</Select.Item>
          {/each}
        </Select.Content>
      </Select.Root>
    </div>

    <AgChart
      className="new-restored-outages-chart"
      data={[...barChartData, ...resourcesData]}
      options={{
        axes: [
          {
            label: {
              formatter: (value: AgAxisLabelFormatterParams) =>
                customFormatter(value, LINECHART_X_AXIS_FORMAT_HOURLY)
            },
            max: chartXAxisMax,
            min: chartXAxisMin,
            position: 'bottom',
            type: 'time',
            crossLines: stormCrossLines
          },
          {
            position: 'left',
            keys: [
              EtrChartKeys.newOutageCount,
              EtrChartKeys.uniqueIncidentsCount,
              EtrChartKeys.restOutageCount,
              EtrChartKeys.projRestOutageCount,
              EtrChartKeys.assessedOutageCount
            ],
            title: { text: store.capitalizedOutagesLabel },
            type: 'number'
          },

          ...(selectedWorkerChartKeys.includes(EtrChartKeys.totalWorkers)
            ? [
                {
                  keys: [EtrChartKeys.totalWorkers],
                  position: 'right',
                  title: {
                    text: 'Workers',
                    color: CROSS_AXIS_COLOUR
                  },
                  type: 'number',
                  label: {
                    color: CROSS_AXIS_COLOUR
                  },
                  tick: {
                    stroke: CROSS_AXIS_COLOUR
                  }
                }
              ]
            : [])
        ],
        series: barChartSeries,
        legend: { item: { showSeriesStroke: true } },
        title: { text: `New/Restored ${store.capitalizedOutagesLabel}` },
        padding: { right: 50, left: 50 },
        navigator: {
          enabled: true
        }
      }}
      loading={isLoadingChartData}
    />
  </Card>
</div>
