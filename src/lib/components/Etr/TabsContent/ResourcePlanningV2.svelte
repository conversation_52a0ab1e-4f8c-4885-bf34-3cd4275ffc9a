<script lang="ts">
  import { onMount } from 'svelte';
  import type { GridApi } from 'ag-grid-enterprise';
  import {
    ResourceAddModal,
    MoveCrewsModal,
    Table,
    AgChart,
    EtrMap,
    ReleaseCrewsModal,
    EditResourcesModal,
    Toggle
  } from '$lib/components';
  import { Card } from '$lib/components/ui/card';
  import * as Dialog from '$lib/components/ui/dialog';
  import * as Tabs from '$lib/components/ui/tabs';
  import { Button } from '$lib/components/ui/button';
  import { deletePendingOperation, getResourceRoles } from '$lib/api';
  import store from '$lib/stores/app.svelte';
  import { isSystemic } from '$lib/stores/constants';
  import {
    Components,
    type ActiveCrew,
    type AggregateMetricRecordWithRegion,
    type FutureResourcesData,
    type LineChartTooltipParams,
    type ResourceData,
    type StormData,
    type Territory
  } from '$lib/types';
  import {
    getCurrentResourcesColumns,
    getResourcesFields,
    getResourcesActivityColumns,
    getCrewLogsColumns,
    DEFAULT_GRID_OPTIONS
  } from '$lib/config/table';
  import {
    CHART_LINE_DASH_BREAK,
    CHART_LINE_DASH_LENGTH,
    customFormatter,
    CROSS_AXIS_COLOUR,
    tooltipRenderer,
    LINECHART_X_AXIS_FORMAT_HOURLY
  } from '$lib/config/chart';
  import { DateTime } from 'luxon';
  import type { AgAxisLabelFormatterParams } from 'ag-charts-enterprise';
  import UpdateEtr from '$lib/components/Etr/UpdateETR.svelte';
  import { Separator } from '$lib/components/ui/separator';

  interface Props {
    activeStorm: StormData | null;
    allowResourceDefaults: boolean;
    crews?: ActiveCrew[];
    currentResourcesRowData?: ResourceData[];
    futureResourcesRowData?: FutureResourcesData[];
    isLoadingAggregateResources: boolean;
    isLoadingCrews?: boolean;
    isLoadingCurrentResources: boolean;
    isLoadingFutureResources: boolean;
    isLoadingResourceVisualization: boolean;
    isLoadingToggledResources: boolean;
    isReadOnly?: boolean;
    resourceChartData: Record<string, any>[];
    stormId: string | null;
    systemOverviewData: Record<string, AggregateMetricRecordWithRegion[]>;
    territories?: Territory[];
    onResourcesMoved: () => void;
    onToggleResourceDefaults: (toggled: boolean) => void;
    pinResourcesSummaryRow: (api: GridApi) => void;
    refreshPage: () => void;
  }

  let {
    activeStorm,
    allowResourceDefaults,
    crews = [],
    currentResourcesRowData = [],
    futureResourcesRowData = [],
    isLoadingAggregateResources,
    isLoadingCrews = false,
    isLoadingCurrentResources,
    isLoadingFutureResources,
    isLoadingResourceVisualization,
    isLoadingToggledResources,
    isReadOnly = false,
    resourceChartData,
    stormId,
    systemOverviewData,
    territories = [],
    onResourcesMoved,
    onToggleResourceDefaults,
    pinResourcesSummaryRow,
    refreshPage
  }: Props = $props();

  let activeCrew = $state<ActiveCrew | null>(null);
  let isDeletingOperation = $state<boolean>(false);
  let resourceRoles: string[] = $state<string[]>([]);
  let selectedOperation = $state<FutureResourcesData | null>(null);
  let showReleaseCrewsModal = $state<boolean>(false);

  onMount(() => {
    fetchResourceRoles();
  });

  async function addResourcesOnSave() {
    await onResourcesMoved();
    await fetchResourceRoles();
  }

  function removePendingOperation(crew: FutureResourcesData) {
    selectedOperation = crew;
  }

  async function onDeletePendingOperation() {
    if (!selectedOperation) return;

    try {
      isDeletingOperation = true;
      await deletePendingOperation(selectedOperation?.id);
      await onResourcesMoved();
      selectedOperation = null;
    } catch (error) {
      console.error('Error deleting pending operation', error);
    } finally {
      isDeletingOperation = false;
    }
  }

  async function fetchResourceRoles() {
    try {
      resourceRoles = await getResourceRoles();
    } catch {
      console.error('Failed to fetch resource roles');
      resourceRoles = [];
    }
  }

  function onShowReleaseCrews(crew: ActiveCrew) {
    activeCrew = crew;
    showReleaseCrewsModal = true;
  }
</script>

<div class="resource-planning flex flex-col gap-md h-[90%] mt-sm">
  <div class="header flex justify-between items-center flex-wrap gap-md">
    <div class="flex gap-sm flex-wrap mr-auto">
      {#if activeStorm && stormId}
        <ResourceAddModal
          disabled={!currentResourcesRowData.length}
          resourceFields={getResourcesFields()}
          stormEndDate={activeStorm?.stormEndDate}
          {stormId}
          stormStartDate={activeStorm?.stormStartDate}
          {territories}
          onSave={addResourcesOnSave}
        />
      {/if}

      {#if !isSystemic && activeStorm && stormId}
        <MoveCrewsModal
          {currentResourcesRowData}
          disabled={!currentResourcesRowData.length}
          resourceFields={getResourcesFields()}
          {stormId}
          stormStartDate={activeStorm?.stormStartDate}
          {territories}
          onSave={() => onResourcesMoved()}
        />
      {/if}
    </div>
    <div class="flex gap-md flex-wrap items-center">
      <UpdateEtr {activeStorm} {refreshPage} />
      <Separator orientation="vertical" class="h-6" />
      <div class="flex gap-md">
        <EditResourcesModal {pinResourcesSummaryRow} {stormId} onSave={() => onResourcesMoved()} />
        <Toggle
          checked={allowResourceDefaults}
          disabled={isLoadingToggledResources}
          label="Resource Defaults"
          onToggle={onToggleResourceDefaults}
        />
      </div>
    </div>
  </div>

  <div class="flex flex-col flex-wrap gap-md items-stretch justify-between lg:flex-row lg:h-full">
    <div class="flex-[4]">
      <div class="flex flex-col gap-3">
        <Card class="etr-card">
          <Table
            columnDefs={getCurrentResourcesColumns(isReadOnly)}
            csvTitle="Resources"
            domLayout="autoHeight"
            isLoading={isLoadingCurrentResources}
            rowData={currentResourcesRowData}
            onSetGridData={pinResourcesSummaryRow}
          />
        </Card>

        <Tabs.Root value="0">
          <Tabs.List>
            <Tabs.Trigger value="0">Resource Activity</Tabs.Trigger>
            <Tabs.Trigger value="1">Crews</Tabs.Trigger>
          </Tabs.List>
          <Tabs.Content value="0">
            <Card>
              <Table
                columnDefs={getResourcesActivityColumns(removePendingOperation)}
                csvTitle="ResourceActivity"
                domLayout="autoHeight"
                isLoading={isLoadingFutureResources}
                rowData={futureResourcesRowData}
              />
            </Card>
          </Tabs.Content>
          <Tabs.Content value="1">
            <Card>
              <Table
                columnDefs={getCrewLogsColumns(resourceRoles, onShowReleaseCrews)}
                csvTitle="CrewLogs"
                gridOptions={{ ...DEFAULT_GRID_OPTIONS, domLayout: 'autoHeight', rowHeight: 50 }}
                isLoading={isLoadingCrews}
                rowData={crews}
              />
            </Card>
          </Tabs.Content>
        </Tabs.Root>
      </div>
    </div>
    <div class="flex flex-[3] flex-col gap-md">
      <Card class="!h-[35vh]" loading={isLoadingResourceVisualization}>
        <AgChart
          data={[
            ...resourceChartData,
            ...(activeStorm?.stormEndDate
              ? [{ date: new Date(activeStorm.stormEndDate), resources: null }]
              : [])
          ]}
          options={{
            series: [
              {
                xKey: 'date',
                yKey: 'resources',
                marker: { enabled: false },
                title: 'Arrived',
                stroke: CROSS_AXIS_COLOUR,
                tooltip: {
                  renderer: (params: LineChartTooltipParams) =>
                    tooltipRenderer(params, 0, '', CROSS_AXIS_COLOUR)
                },
                data: resourceChartData.filter((datum) => new Date(datum.date) <= new Date()),
                interpolation: {
                  type: 'step'
                }
              },
              {
                xKey: 'date',
                yKey: 'pending',
                marker: { enabled: false },
                stroke: CROSS_AXIS_COLOUR,
                title: 'Pending',
                tooltip: {
                  renderer: (params: LineChartTooltipParams) =>
                    tooltipRenderer(params, 0, '', CROSS_AXIS_COLOUR)
                },
                data: resourceChartData.filter((datum) => new Date(datum.date) > new Date()),
                lineDash: [CHART_LINE_DASH_LENGTH, CHART_LINE_DASH_LENGTH],
                interpolation: {
                  type: 'step'
                }
              }
            ],
            axes: [
              {
                label: {
                  formatter: (value: AgAxisLabelFormatterParams) =>
                    customFormatter(value, LINECHART_X_AXIS_FORMAT_HOURLY)
                },
                nice: true,
                type: 'time',
                position: 'bottom',
                crossLines:
                  activeStorm?.stormStartDate && activeStorm?.stormEndDate
                    ? [
                        {
                          type: 'line',
                          value: DateTime.fromJSDate(activeStorm?.stormStartDate).toJSDate(),
                          strokeWidth: 2,
                          label: { text: 'Storm Start' },
                          lineDash: [CHART_LINE_DASH_LENGTH, CHART_LINE_DASH_BREAK],
                          stroke: 'red'
                        },
                        {
                          type: 'line',
                          value: DateTime.fromJSDate(activeStorm?.stormEndDate).toJSDate(),
                          strokeWidth: 2,
                          lineDash: [CHART_LINE_DASH_LENGTH, CHART_LINE_DASH_BREAK],
                          label: { text: 'Storm End' },
                          stroke: 'red'
                        }
                      ]
                    : []
              },
              {
                type: 'number',
                title: { text: 'Active Resources' }
              }
            ],
            padding: { right: 50 },
            legend: { enabled: false }
          }}
        />
      </Card>
      <EtrMap
        loading={isLoadingAggregateResources}
        class="w-full self-start !min-h-[250px] mobile:!min-h-[350px] h-full"
        config={store.componentsConfig?.[Components.ETR_MAP]?.items}
        {systemOverviewData}
        showPlayback
        showWindAnimation
        showResources
      />
    </div>
  </div>
</div>

<Dialog.Root
  open={!!selectedOperation}
  onOpenChange={(isOpening) => {
    if (!isOpening) selectedOperation = null; // Handles ESC Keypress
  }}
>
  <Dialog.Content class="w-[350px] md:w-[600px]">
    <Dialog.Header>
      <Dialog.Title>Confirm Delete?</Dialog.Title>
      <Dialog.Description>
        Are you sure you want to delete this pending operation?
        <p class="my-1">
          <strong>
            [{selectedOperation?.total}
            {selectedOperation?.total === 1 ? 'resource' : 'resources'} from {selectedOperation?.fromTerritoryId}
            to {selectedOperation?.toTerritoryId}]
          </strong>
        </p>
        This action cannot be undone.
      </Dialog.Description>
    </Dialog.Header>

    <Dialog.Footer>
      <Button variant="tertiary" onclick={() => (selectedOperation = null)}>Cancel</Button>
      <Button
        variant="destructive"
        disabled={isDeletingOperation}
        onclick={() => onDeletePendingOperation()}
      >
        Delete
      </Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>

{#if showReleaseCrewsModal && activeCrew && activeStorm && stormId}
  <ReleaseCrewsModal
    crew={activeCrew}
    stormEndDate={activeStorm?.stormEndDate}
    stormStartDate={activeStorm?.stormStartDate}
    onClose={() => (showReleaseCrewsModal = false)}
    onSave={() => onResourcesMoved()}
  />
{/if}
