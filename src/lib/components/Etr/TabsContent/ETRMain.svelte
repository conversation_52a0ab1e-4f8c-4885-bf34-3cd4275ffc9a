<script lang="ts">
  import {
    Ag<PERSON>hart,
    ButtonGroup,
    HeaderPercentile,
    HeaderTargeted,
    HeaderSimulation,
    EtrMap,
    Table,
    Tooltip
  } from '$lib/components';
  import { Card } from '$lib/components/ui/card';
  import { Button } from '$lib/components/ui/button';
  import { isDarkMode } from '$lib/stores/theme';
  import store from '$lib/stores/app.svelte';
  import { isSystemic, showEta } from '$lib/stores/constants';
  import type { CustomTooltipRendererParams } from '$lib/types';
  import { Components, RegionalEtrLevel, SimulationOutageScale, StormMode } from '$lib/types';
  import type {
    RegionalEtr,
    ResourceData,
    ChartDataPoint,
    SimulatedRegion,
    SimulationTime
  } from '$lib/types';
  import { getCurrentResourcesColumns, getEtrColumns } from '$lib/config/table';
  import {
    type OutageScales,
    getLineChartSeriesItems,
    getColorScale,
    ETR_CHART_SPECIAL_MARKERS
  } from '$lib/config/etr';
  import type { GridApi } from 'ag-grid-enterprise';
  import { DateTime } from 'luxon';
  import type { Map as MapboxMapInstance, Popup } from 'mapbox-gl';
  import { formatNumbers } from '$lib/utils';
  import type { AgAxisLabelFormatterParams } from 'ag-charts-enterprise';

  interface Props {
    chartData?: ChartDataPoint[];
    currentResourcesRowData?: ResourceData[];
    currentSimulationResults?: SimulatedRegion[];
    datePickerMaxDate?: string | null;
    datePickerMinDate?: string | null;
    filteredEtrs?: RegionalEtr[];
    isActiveSession: boolean;
    isLoadingEtrSimulationResults: boolean;
    isLoadingHistoricEtrResults: boolean;
    isLoadingResources: boolean;
    isLoadingSessionETRs: boolean;
    isLoadingSimulatedRegions: boolean;
    isLoadingSimulationTimes?: boolean;
    regionalEtrs?: RegionalEtr[];
    selectedEtrLevel: OutageScales;
    sessionDate: Date;
    simulationTimes: Record<SimulationOutageScale, SimulationTime | null>;
    stormMode: StormMode | null;
    stormStartDate: Date | null | undefined;
    systemRestoration: { low: string; medium: string; high: string } | null;
    targetedETR: string | null;
    tooltipData?: Record<string, string[]>;
    onFetchMaxSimulationTimes: (outageScale: SimulationOutageScale) => void;
    onFetchSimulatedRegions: (
      outageScale: SimulationOutageScale,
      regionEtrType: RegionalEtrLevel
    ) => void;
    onResetTargetedETR: () => void;
    onSelectEtrLevel: (level: OutageScales) => void;
    onSelectTargetedEtr: () => void;
    onTargetDateChanged: (date: DateTime) => void;
  }

  let {
    chartData = [],
    currentResourcesRowData = [],
    currentSimulationResults = [],
    datePickerMaxDate = null,
    datePickerMinDate = null,
    filteredEtrs = [],
    isActiveSession,
    isLoadingEtrSimulationResults,
    isLoadingHistoricEtrResults,
    isLoadingResources,
    isLoadingSessionETRs,
    isLoadingSimulatedRegions,
    isLoadingSimulationTimes = true,
    regionalEtrs = [],
    sessionDate,
    simulationTimes,
    stormMode,
    stormStartDate,
    targetedETR,
    selectedEtrLevel,
    systemRestoration,
    tooltipData = {},
    onFetchMaxSimulationTimes,
    onFetchSimulatedRegions,
    onResetTargetedETR,
    onSelectEtrLevel,
    onSelectTargetedEtr,
    onTargetDateChanged
  }: Props = $props();

  let cardView = $state<'Chart' | 'Spatial'>('Spatial');
  let etrLevel = $state<RegionalEtrLevel>(RegionalEtrLevel.current);
  let selectedIndex = $state<0 | 1>(0);
  let selectedSimulationIndex: 0 | 1 = $state<0 | 1>(0);

  let resourceFields: string[] =
    store.componentsConfig?.[Components.RESOURCE_VALIDATION]?.validations || [];

  let colorScale = $derived({
    ...getColorScale(chartData, $isDarkMode, isSystemic),
    [ETR_CHART_SPECIAL_MARKERS.currentResources]: 'transparent'
  });
  let outageLevel = $derived(
    selectedSimulationIndex === 0 ? SimulationOutageScale.medium : SimulationOutageScale.high
  );
  let mapRecords = $derived(
    generateMapRecords(currentSimulationResults, stormStartDate, sessionDate)
  );
  let projectedETR = $derived(
    regionalEtrs.length
      ? regionalEtrs
          .map((e) => e.projectedETR)
          .filter((value) => value !== null && value !== undefined)
          .sort((a, b) => b.localeCompare(a))[0]
      : null
  );
  let selectedConfig = $derived(
    store.componentsConfig?.[Components.RESTORATION_MAP]?.items[selectedIndex]
  );
  let showMap = $derived(cardView === 'Spatial');

  $effect(() => {
    if (showEta) onFetchMaxSimulationTimes(outageLevel);
  });

  $effect(() => {
    if (showEta) onFetchSimulatedRegions(outageLevel, etrLevel);
  });

  function generateChartRecords(records: Record<string, any>[], index: 0 | 1) {
    const data = records.reduce(
      (map, record) => {
        map[record.region] = index === 0 ? record.etrHours : record.etaHours;
        return map;
      },
      { regions: 'Regions' }
    );
    return [data];
  }

  function pinSummaryRow(api: GridApi) {
    if (api.getDisplayedRowCount() <= 1) return;

    const cols = [
      ...resourceFields,
      'activeOutages',
      'activeCustOutages',
      'remainingOutages',
      'remainingCustOutages',
      'total',
      'projectedETA',
      'projectedETR',
      'totalAssessmentWorkers',
      'totalRepairWorkers',
      'futureTotal',
      'resourcesNeeded'
    ];

    const result: Record<string, string | number>[] = [
      { location: 'Summary', territoryName: 'Summary', projectedETA: '', projectedETR: '' }
    ];

    cols.forEach((field) => (result[0][field] = 0));

    const maxValues = {
      projectedETA: null as Date | null,
      projectedETR: null as Date | null
    };

    api.forEachNode((n) => {
      cols.forEach((field) => {
        if (field === 'projectedETA' || field === 'projectedETR') {
          const dateValue = n.data[field] ? new Date(n.data[field]) : null;
          if (dateValue && (!maxValues[field] || dateValue > maxValues[field])) {
            maxValues[field] = dateValue;
          }
        } else {
          result[0][field] += n.data[field] || 0;
        }
      });
    });

    result[0].projectedETA = maxValues.projectedETA?.toISOString() ?? '';
    result[0].projectedETR = maxValues.projectedETR?.toISOString() ?? '';

    // Update the pinned bottom row data
    api.setGridOption('pinnedBottomRowData', result);
  }

  function generateMapRecords(
    regions: SimulatedRegion[],
    stormStart: Date | null | undefined,
    sessionStart: Date
  ) {
    return regions.map(
      ({ territoryName, totalResources, estimatedRestoration, estimatedAssessment }) => {
        const comparisionTime = isActiveSession
          ? stormStart?.toISOString()
          : sessionStart?.toISOString();

        let etrHours = 0;
        let etaHours = 0;

        // if storm is active then use the current time, otherwise if historical session or storm in restoration then use the ETA/ETOR times
        if (comparisionTime) {
          etrHours =
            stormMode === StormMode.ACTIVE
              ? DateTime.fromISO(estimatedRestoration).diffNow().as('hours')
              : DateTime.fromISO(estimatedRestoration)
                  .diff(DateTime.fromISO(comparisionTime))
                  .as('hours');

          etaHours =
            stormMode === StormMode.ACTIVE
              ? DateTime.fromISO(estimatedAssessment).diffNow().as('hours')
              : DateTime.fromISO(estimatedAssessment)
                  .diff(DateTime.fromISO(comparisionTime))
                  .as('hours');
        }

        // if the time is negative then set it to 0 (meaning region is restored)
        return {
          etaHours: etaHours < 0 ? 0 : etaHours,
          etrHours: etrHours < 0 ? 0 : etrHours,
          totalResources,
          region: territoryName
        };
      }
    );
  }

  function getETChartSeriesItems(records: Record<string, any>[]) {
    return records.map((r) => ({
      tooltip: { renderer: tooltipRenderer },
      type: 'bar',
      xKey: 'regions',
      yKey: r.region,
      yName: r.region
    }));
  }

  function tooltipRenderer(params: CustomTooltipRendererParams) {
    return `
      <div class="ag-chart-tooltip-title" style="background-color: ${params.color}">
        ${params.title}
      </div>
      <div class="ag-chart-tooltip-content">
        ${formatNumbers(params.datum[params.yKey])} Hours
      </div>`;
  }

  function customMapPopup(popup: Popup, record: Record<string, any>, map: MapboxMapInstance) {
    return popup
      .setHTML(
        `<div class="popup-region-info">
            <p class="popup-feature-name">${record?.region}</p>
            <div class="info-row">
              <span class="label">Resources:</span>
              <span class="value">
              ${formatNumbers(record?.totalResources)}
              </span>
            </div>
            <div class="info-row">
              <span class="label">ETA:</span>
              <span class="value">
                ${formatNumbers(record?.etaHours)} Hours
              </span>
            </div>
            <div class="info-row">
              <span class="label">ETOR:</span>
              <span class="value">
                ${formatNumbers(record?.etrHours)} Hours
              </span>
            </div>
          </div>`
      )
      .addTo(map);
  }

  function formatCurrentSimulations(simulations: SimulatedRegion[], etrs: RegionalEtr[]) {
    const etrMap = Object.fromEntries(
      etrs.map(({ location, activeCustOutages, activeOutages, id }) => [
        location,
        { activeCustOutages, activeOutages, id }
      ])
    );

    const simulationsMap = Object.fromEntries(
      simulations.map(
        ({
          territoryName,
          resources,
          estimatedAssessment,
          estimatedRestoration,
          totalResources,
          forecastCustomerOutages,
          forecastOutages
        }) => [
          territoryName,
          {
            ...resources,
            location: territoryName,
            projectedETA: estimatedAssessment,
            projectedETR: estimatedRestoration,
            totalAssessmentWorkers:
              resources.companyAssessmentWorkers + resources.contractorAssessmentWorkers,
            totalRepairWorkers: resources.companyRepairWorkers + resources.contractorRepairWorkers,
            forecastCustomerOutages,
            forecastOutages,
            total: totalResources
          }
        ]
      )
    );

    return Object.entries(etrMap).map(([key, etr]) => ({
      ...simulationsMap[key],
      ...etr
    }));
  }
</script>

<div class="flex flex-row gap-3 flex-wrap h-full">
  {#if showEta}
    <div class="flex-[2]">
      <ButtonGroup class="max-w-[200px] ml-sm mb-[-1rem]" bind:value={selectedSimulationIndex}>
        <Button class="!border-b-0 !rounded-b-none" value={0}>Expected</Button>
        <Button class="!border-b-0 !rounded-b-none" value={1}>High</Button>
      </ButtonGroup>

      <Card class="etr-card">
        <HeaderSimulation
          bind:etrLevel
          isLoading={isLoadingSimulationTimes}
          {outageLevel}
          simulationTime={simulationTimes[outageLevel]}
          {tooltipData}
        />

        <Table
          class="!h-0"
          columnDefs={getEtrColumns()}
          csvTitle="ETRSimulation"
          domLayout="autoHeight"
          isLoading={isLoadingSimulatedRegions}
          rowData={formatCurrentSimulations(currentSimulationResults, filteredEtrs)}
          onSetGridData={pinSummaryRow}
        />
      </Card>
    </div>
  {:else}
    <div class="flex-[2]">
      <Card class="etr-card">
        {#if isSystemic}
          <HeaderPercentile
            isLoading={isLoadingSessionETRs}
            {selectedEtrLevel}
            {systemRestoration}
            {onSelectEtrLevel}
          />
        {:else}
          <HeaderTargeted
            {datePickerMaxDate}
            {datePickerMinDate}
            isLoading={isLoadingSessionETRs}
            {projectedETR}
            {stormMode}
            {targetedETR}
            {onResetTargetedETR}
            {onSelectTargetedEtr}
            {onTargetDateChanged}
          />
        {/if}

        <Table
          columnDefs={getEtrColumns()}
          csvTitle="ETR"
          domLayout="autoHeight"
          isLoading={isLoadingSessionETRs}
          rowData={filteredEtrs}
          onSetGridData={pinSummaryRow}
        />
      </Card>

      <Card class="etr-card">
        <Table
          columnDefs={getCurrentResourcesColumns(true)}
          csvTitle="Resources"
          domLayout="autoHeight"
          isLoading={isLoadingResources}
          rowData={currentResourcesRowData}
          onSetGridData={pinSummaryRow}
        />
      </Card>
    </div>
  {/if}

  {#if showEta}
    <Card class="etr-card flex flex-col min-h-[650px] min-w-[350px] flex-1">
      <div class="flex justify-between mb-sm">
        <div class="flex gap-md">
          <Tooltip>
            {#snippet trigger()}
              <span class="material-icons">info</span>
            {/snippet}

            {#snippet content()}
              <span
                >ETA/ETOR values are based on the storm’s start time if it is forecasted, or the
                current time during an active storm.</span
              >
            {/snippet}
          </Tooltip>

          <ButtonGroup size="icon" bind:value={cardView}>
            <Button tooltip="Spatial" value="Spatial">
              <span class="material-icons">map</span>
            </Button>
            <Button tooltip="Chart" value="Chart">
              <span class="material-icons">stacked_line_chart</span>
            </Button>
          </ButtonGroup>
        </div>

        <ButtonGroup bind:value={selectedIndex}>
          <Button value={0}>ETOR</Button>
          <Button value={1}>ETA</Button>
        </ButtonGroup>
      </div>

      {#if showMap}
        <EtrMap
          config={store.componentsConfig?.[Components.RESTORATION_MAP]?.items}
          customPopup={customMapPopup}
          records={mapRecords}
          {selectedConfig}
          showPlayback={isActiveSession}
          showWindGusts={false}
          onLayerSelect={({ name }) => (selectedIndex = name === 'ETOR' ? 0 : 1)}
        />
      {:else}
        <AgChart
          canExpand
          data={generateChartRecords(mapRecords, selectedIndex)}
          options={{
            axes: [
              { position: 'bottom', type: 'category' },
              { position: 'left', title: { text: 'Hours' }, type: 'number' }
            ],
            series: getETChartSeriesItems(mapRecords),
            title: { text: selectedIndex === 0 ? 'ETOR' : 'ETA' }
          }}
        />
      {/if}
    </Card>
  {:else}
    <Card class="etr-card flex-1 min-w-[350px] !h-[670px] self-start">
      <AgChart
        loading={isLoadingEtrSimulationResults && isLoadingHistoricEtrResults}
        canExpand={true}
        className="w-full"
        data={chartData}
        options={{
          axes: [
            {
              position: 'bottom',
              type: 'number',
              title: { text: 'Total Workers' }
            },
            {
              position: 'left',
              type: 'number',
              title: { text: 'Hours to Restoration' },
              label: {
                formatter: function (params: AgAxisLabelFormatterParams) {
                  return formatNumbers(params.value, 2) + 'h';
                }
              }
            }
          ],
          series: getLineChartSeriesItems(chartData, $isDarkMode, colorScale),
          title: { text: `${store.etrLabel} Simulation` }
        }}
      />
    </Card>
  {/if}
</div>
