<script lang="ts">
  import { DateTimePickerModal } from '$lib/components';
  import { Button } from '$lib/components/ui/button';
  import { Skeleton } from '$lib/components/ui/skeleton';
  import store from '$lib/stores/app.svelte';
  import { StormMode } from '$lib/types';
  import { formatDateTime } from '$lib/config/utils';
  import type { DateTime } from 'luxon';

  interface Props {
    datePickerMaxDate?: string | null;
    datePickerMinDate?: string | null;
    isLoading?: boolean;
    projectedETR: string | null;
    stormMode: StormMode | null;
    targetedETR: string | null;
    onResetTargetedETR: () => void;
    onSelectTargetedEtr: () => void;
    onTargetDateChanged: (date: DateTime) => void;
  }

  let {
    datePickerMaxDate = null,
    datePickerMinDate = null,
    isLoading = true,
    projectedETR,
    stormMode,
    targetedETR,
    onResetTargetedETR,
    onSelectTargetedEtr,
    onTargetDateChanged
  }: Props = $props();
</script>

<div class="etr-table-header">
  <DateTimePickerModal
    maxDate={datePickerMaxDate}
    minDate={datePickerMinDate}
    presetDate={targetedETR}
    title="Targeted {store.etrLabel}"
    onReset={onResetTargetedETR}
    onSave={onTargetDateChanged}
  >
    <Button class="button-low flex-col gap-0" variant="outline" onclick={onSelectTargetedEtr}>
      <span class="etr-header">Targeted {store.etrLabel}</span>
      <div class="flex">
        <span class="font-semibold">
          {#if targetedETR}
            {formatDateTime(targetedETR)}
          {:else}
            Edit
          {/if}
        </span>
        <span class="material-icons ml-1 edit">edit</span>
      </div>
    </Button>
  </DateTimePickerModal>

  <div class="projected-etr flex flex-col items-center justify-center">
    <span class="etr-header">Projected {store.etrLabel}</span>

    {#if isLoading}
      <Skeleton />
    {:else if projectedETR}
      <span class="font-semibold">
        {formatDateTime(projectedETR)}
      </span>
    {:else}
      Not Available
    {/if}
  </div>

  {#if stormMode === StormMode.POST_RESTORATION}
    <div class="text-success flex items-center justify-center p-2">
      <span class="material-icons mr-2">check_circle</span>
      <div class="flex flex-col items-center justify-center">
        <span>Restoration</span>
        <span>Complete</span>
      </div>
    </div>
  {/if}
</div>

<style>
  .etr-table-header {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }

  :global(.etr-table-header button) {
    background: var(--color-accentGray);
    border: 1px solid var(--color-border);
    height: 64px;
    width: 210px;
  }

  .projected-etr {
    background: var(--color-accentGray);
    border: 1px solid var(--color-border);
    height: 64px;
    width: 200px;
    border-radius: var(--border-radius);
  }

  .etr-header {
    font-size: 10px;
    font-weight: bold;
  }

  .edit {
    color: var(--color-text);
    font-size: 16px;
    position: relative;
    top: 2px;
  }
</style>
