<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog';
  import { Button } from '$lib/components/ui/button';
  import toast from '$lib/stores/toast.svelte';
  import { getAuthToken, getUserData } from '$lib/authentication';
  import { SystemStatus } from '$lib/config/etr';
  import store from '$lib/stores/app.svelte';
  import { getBaseApiUrl, postEtrEvent } from '$lib/api';
  import type { StormData } from '$src/lib/types';
  import { EventSourcePolyfill } from 'event-source-polyfill';
  import { onDestroy, onMount } from 'svelte';

  interface Props {
    activeStorm?: StormData | null;
    refreshPage: () => void;
  }

  let { activeStorm, refreshPage }: Props = $props();

  let isSubmissionModalOpen = $state<boolean>(false);
  let isModelRunning = $state<boolean>(false);
  let isModelSubmitted = $state<boolean>(false);
  let eventSource = $state<EventSourcePolyfill | null>(null);

  async function onSubmitModel() {
    if (!activeStorm) return;
    try {
      await postEtrEvent({
        event: 'executeModelCommand',
        aggregateId: activeStorm.id,
        data: {
          stormId: activeStorm.id,
          user: getUserData()
        }
      });
      isSubmissionModalOpen = false;
      isModelRunning = true;
      isModelSubmitted = true;
      toast.addToast({ message: 'Model run successfully submitted', type: 'success' });
    } catch (e) {
      toast.addToast({ message: 'Error running model', type: 'error' });
    }
  }

  async function setUpSSE() {
    const eventSourceUrl = `${getBaseApiUrl()}storm-etr-service/modelStatus`;
    const token = await getAuthToken();
    if (!token) {
      console.error('No authentication token available. Cannot establish WebSocket connection.');
      return;
    }
    eventSource = new EventSourcePolyfill(eventSourceUrl, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    eventSource.onmessage = (event) => {
      if (event.data === 'keep-alive') return;
      try {
        const receivedMessage = JSON.parse(event.data);
        const { data: status, type } = receivedMessage;
        if (type === 'status') {
          const isRunning = status === SystemStatus.MODEL_RUNNING;
          isModelRunning = isRunning;
          if (isModelSubmitted && !isRunning) {
            toast.addToast({
              message: 'Model run completed successfully',
              type: 'success'
            });
            isModelSubmitted = false;
            refreshPage();
          }
        }
      } catch (err) {
        console.error(err);
      }
    };
    eventSource.onerror = (error) => {
      console.error('SSE error:', error);
    };
  }

  onMount(async () => {
    setUpSSE();
  });

  onDestroy(() => {
    if (eventSource) {
      eventSource.close();
    }
  });
</script>

<Dialog.Root bind:open={isSubmissionModalOpen}>
  <Dialog.Trigger>
    <Button class="w-48" disabled={isModelRunning} size="sm">
      <div class="flex gap-sm items-center justify-center">
        {#if isModelRunning}
          <span class="material-icons animate-spin">sync</span>
          <span>{store.etrLabel}s Updating...</span>
        {:else}
          <span class="material-icons">search_insights</span>
          <span>Update {store.etrLabel}</span>
        {/if}
      </div>
    </Button>
  </Dialog.Trigger>
  <Dialog.Content class="md:w-[550px] w-[350px]">
    <Dialog.Header>
      <Dialog.Title>Update {store.etrLabel}?</Dialog.Title>
      <Dialog.Description
        >This will trigger a model run immediately to reflect the latest resources.</Dialog.Description
      >
    </Dialog.Header>
    <Dialog.Footer>
      <Button variant="tertiary" onclick={() => (isSubmissionModalOpen = false)}>Cancel</Button>
      <Button onclick={() => onSubmitModel()}>Submit</Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
