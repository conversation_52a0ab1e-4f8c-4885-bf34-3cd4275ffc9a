<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import { Skeleton } from '$lib/components/ui/skeleton';
  import store from '$lib/stores/app.svelte';
  import { OutageScales } from '$lib/config/etr';
  import { formatDateTime } from '$lib/config/utils';

  interface Props {
    isLoading?: boolean;
    selectedEtrLevel: OutageScales;
    systemRestoration: { low: string; medium: string; high: string } | null;
    onSelectEtrLevel: (level: OutageScales) => void;
  }

  let { isLoading = true, selectedEtrLevel, systemRestoration, onSelectEtrLevel }: Props = $props();
</script>

<div class="etr-table-header">
  <Button
    class="button-low flex flex-col items-center justify-center gap-0 {selectedEtrLevel === 'low'
      ? 'selected'
      : ''}"
    variant="outline"
    onclick={() => onSelectEtrLevel(OutageScales.LOW)}
  >
    <div class="flex">
      Low
      <span class="material-icons ml-2 warning low">warning</span>
    </div>

    {#if isLoading}
      <Skeleton class="mt-sm" />
    {:else if systemRestoration?.low}
      <span class="projected-etr">
        Projected {store.etrLabel}
        <span class="block">
          {formatDateTime(systemRestoration.low)}
        </span>
      </span>
    {/if}
  </Button>

  <Button
    class="button-medium flex flex-col items-center justify-center gap-0 {selectedEtrLevel ===
    'medium'
      ? 'selected'
      : ''}"
    variant="outline"
    onclick={() => onSelectEtrLevel(OutageScales.MEDIUM)}
  >
    <div class="flex">
      Medium
      <span class="material-icons ml-2 warning medium">warning</span>
    </div>

    {#if isLoading}
      <Skeleton class="mt-sm" />
    {:else if systemRestoration?.medium}
      <span class="projected-etr">
        Projected {store.etrLabel}
        <span class="block">
          {formatDateTime(systemRestoration.medium)}
        </span>
      </span>
    {/if}
  </Button>

  <Button
    class="button-high flex flex-col items-center justify-center gap-0 {selectedEtrLevel === 'high'
      ? 'selected'
      : ''}"
    variant="outline"
    onclick={() => onSelectEtrLevel(OutageScales.HIGH)}
  >
    <div class="flex">
      High
      <span class="material-icons ml-2 warning high">warning</span>
    </div>

    {#if isLoading}
      <Skeleton class="mt-sm" />
    {:else if systemRestoration?.high}
      <span class="projected-etr">
        Projected {store.etrLabel}
        <span class="block">
          {formatDateTime(systemRestoration.medium)}
        </span>
      </span>
    {/if}
  </Button>
</div>

<style>
  .etr-table-header {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }

  :global(.etr-table-header button) {
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    height: 64px;
    width: 200px;
  }
  :global(.etr-table-header .selected) {
    border-width: 2px;
  }

  :global(.etr-table-header .button-low) {
    background: hsla(126, 29%, 47%, 0.2);
  }

  :global(.etr-table-header .button-medium) {
    background: hsla(32, 93%, 54%, 0.2);
  }

  :global(.etr-table-header .button-high) {
    background: hsla(3, 100%, 35%, 0.2);
  }

  :global(.etr-table-header .selected.button-low) {
    border-color: var(--color-success) !important;
  }

  :global(.etr-table-header .selected.button-medium) {
    border-color: var(--color-warning) !important;
  }

  :global(.etr-table-header .selected.button-high) {
    border-color: var(--color-danger) !important;
  }

  .material-icons {
    font-size: 20px;
  }

  .etr-table-header .warning.low {
    color: var(--color-success);
  }

  .etr-table-header .warning.medium {
    color: var(--color-warning);
  }

  .etr-table-header .warning.high {
    color: var(--color-danger);
  }

  .projected-etr {
    font-size: 12px;
  }

  @media (max-width: 767px) {
    .projected-etr {
      display: none;
    }
  }
</style>
