<script lang="ts">
  import { Tooltip } from '$lib/components';
  import { Button } from '$lib/components/ui/button';
  import { Skeleton } from '$lib/components/ui/skeleton';
  import { type SimulationTime } from '$lib/types';
  import { RegionalEtrLevel } from '$lib/types';
  import { formatDateTime } from '$lib/config/utils';

  const TOOLTIP_TYPES: Record<string, Record<RegionalEtrLevel, string>> = {
    MEDIUM: {
      [RegionalEtrLevel.current]: 'current_pill_medium',
      [RegionalEtrLevel.currentOptimized]: 'current_optimized_pill_medium',
      [RegionalEtrLevel.plusResouces]: 'plus_resources_pill_medium',
      [RegionalEtrLevel.plusResourcesOptimized]: 'plus_resources_optimized_pill_medium'
    },
    HIGH: {
      [RegionalEtrLevel.current]: 'current_pill_high',
      [RegionalEtrLevel.currentOptimized]: 'current_optimized_pill_high',
      [RegionalEtrLevel.plusResouces]: 'plus_resources_pill_high',
      [RegionalEtrLevel.plusResourcesOptimized]: 'plus_resources_optimized_pill_high'
    }
  };

  const SIMULATION_TYPES: Record<RegionalEtrLevel, string> = {
    [RegionalEtrLevel.current]: 'Current',
    [RegionalEtrLevel.currentOptimized]: 'Current Optimized',
    [RegionalEtrLevel.plusResouces]: '+ Resources',
    [RegionalEtrLevel.plusResourcesOptimized]: '+ Resources Optimized'
  };

  interface Props {
    isLoading?: boolean;
    etrLevel: RegionalEtrLevel;
    outageLevel: string;
    simulationTime: SimulationTime | null;
    tooltipData?: Record<string, string[]>;
  }

  let {
    etrLevel = $bindable(),
    isLoading = true,
    outageLevel,
    simulationTime,
    tooltipData = {}
  }: Props = $props();

  $effect(() => {
    if (simulationTime) {
      if (!simulationTime[etrLevel]) {
        Object.keys(SIMULATION_TYPES).some((key) => {
          if (simulationTime[key as keyof SimulationTime]) {
            etrLevel = key as RegionalEtrLevel;
            return true;
          }
          return false;
        });
      }
    }
  });
</script>

<div class="etr-simulation">
  {#if isLoading}
    <div class="flex flex-wrap gap-sm mb-sm">
      {#each Object.values(SIMULATION_TYPES) as label}
        <Button class="flex-col gap-0 bg-inherit" disabled={true} variant="outline">
          <span class="block font-semibold text-left mb-xs">{label}</span>
          <div class="flex items-center gap-sm mb">
            <span class="text-xs">ETA:</span>
            <Skeleton class="mb-0 w-[75px]" />
          </div>
          <div class="flex items-center gap-sm">
            <span class="text-xs">ETOR:</span>
            <Skeleton class="mb-0 w-[75px]" />
          </div>
        </Button>
      {/each}
    </div>
  {/if}

  <div class="flex flex-wrap gap-sm mb-sm">
    {#each Object.entries(SIMULATION_TYPES) as [key, value]}
      {#if simulationTime?.[key as RegionalEtrLevel]}
        <Tooltip>
          {#snippet content()}
            <ul>
              {#each tooltipData?.[TOOLTIP_TYPES?.[outageLevel]?.[key as RegionalEtrLevel]] ?? [] as item}
                <div class="p-sm item">
                  <li>{item}</li>
                </div>
              {/each}
            </ul>
          {/snippet}
          {#snippet trigger()}
            <Button
              class="flex-col {key === etrLevel ? 'selected' : ''} gap-0 bg-inherit"
              variant="outline"
              onclick={() => (etrLevel = key as RegionalEtrLevel)}
            >
              <span class="block font-semibold text-left mb-xs">{value}</span>
              <div class="flex items-center gap-sm mb">
                <span class="text-xs">
                  ETA: {formatDateTime(
                    simulationTime[key as RegionalEtrLevel]?.estimatedAssessment
                  )}
                </span>
              </div>
              <div class="flex items-center gap-sm">
                <span class="text-xs">
                  ETOR: {formatDateTime(
                    simulationTime[key as RegionalEtrLevel]?.estimatedRestoration
                  )}
                </span>
              </div>
            </Button>
          {/snippet}
        </Tooltip>
      {/if}
    {/each}
  </div>
</div>

<style>
  :global(.etr-simulation button) {
    height: 85px;
    width: 205px;
  }

  :global(.etr-simulation button.selected) {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px var(--color-primary);
  }

  .item:not(:last-child) {
    border-bottom: 1px solid var(--color-border);
  }
</style>
