<script lang="ts">
  interface Props {
    color?: string;
    max?: number;
    min?: number;
    title?: string;
    value?: number;
  }

  let { color = 'black', max = 100, min = 0, title = '', value = 0 }: Props = $props();
</script>

<div class="progress flex-1" {title}>
  <div title="{value}/{max}" class="progress-bar">
    {#if value}
      <span class="value">{value}%</span>
      <div
        class="inner-bar"
        style:--width={((value - min) / (max - min)) * 100}
        style:--bar-color={color}
      ></div>
    {/if}
  </div>
</div>

<style>
  .progress {
    display: flex;
    align-items: center;
    margin: 0.25rem;
  }

  .progress-bar {
    background: var(--color-accentGray);
    border-radius: var(--border-radius);
    height: 14px;
    overflow: hidden;
    position: relative;
    width: 100%;
  }

  .value {
    font-size: 10px;
    margin-left: 6px;
    position: absolute;
    left: 0;
    z-index: 1;
  }

  .inner-bar {
    background-color: var(--bar-color);
    width: calc(var(--width) * 1%);
    height: 14px;
    position: relative;
  }

  .inner-bar:after {
    content: '';
    width: 0px;
    height: 0px;
    border-style: solid;
    border-width: 0px 0px 14px 14px;
    border-color: var(--bar-color) var(--bar-color) transparent var(--bar-color);
    position: absolute;
    top: 0px;
    right: -14px;
    margin-left: -14px;
  }
</style>
