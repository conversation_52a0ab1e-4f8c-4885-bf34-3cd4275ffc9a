<script lang="ts">
  interface Props {
    class?: string;
  }

  let { class: className = '' }: Props = $props();
</script>

<div class="loading-ellipsis {className}">
  <div class="dot-1"></div>
  <div class="dot-2"></div>
  <div class="dot-3"></div>
</div>

<style>
  .loading-ellipsis {
    display: inline;
  }

  .loading-ellipsis div {
    animation: bouncedelay 1.4s infinite ease-in-out;
    animation-fill-mode: both;
    background: var(--color-text);
    border-radius: 100%;
    bottom: 3px;
    display: inline-block;
    height: 3px;
    position: relative;
    width: 3px;
  }

  .loading-ellipsis .dot-1 {
    animation-delay: -0.32s;
  }

  .loading-ellipsis .dot-2 {
    animation-delay: -0.16s;
  }

  @keyframes bouncedelay {
    0%,
    80%,
    100% {
      transform: scale(0);
      opacity: 0;
    }

    40% {
      transform: scale(1);
      opacity: 100;
    }
  }
</style>
