<script lang="ts">
  import { Card } from '$lib/components/ui/card';
  import SeverityBar from '../SeverityBar/SeverityBar.svelte';

  interface Props {
    class?: string;
  }

  let { class: className = '' }: Props = $props();

  const EMPTY_DATA_FIELDS_AMOUNT = 5;

  const emptyData: ''[] = Array(EMPTY_DATA_FIELDS_AMOUNT).fill('');
</script>

<Card class="empty-forecast-card {className}">
  <div>
    <div class="container">
      <div class="data">
        <div class="header">No Data</div>
      </div>
    </div>
  </div>

  <div class="scores">
    {#each emptyData as i}
      <div class="score-row flex items-center {i}">
        <div class="event-level"></div>
        <SeverityBar max={100} min={0} value={0} />
      </div>
    {/each}
  </div>
</Card>

<style>
  .container {
    align-items: flex-start;
    display: flex;
    justify-content: space-between;
  }

  .header {
    font-size: 1rem;
    height: 52px;
  }

  .event-level {
    background: var(--color-accentGray);
    border-radius: 50%;
    height: 14px;
    width: 14px;
  }
</style>
