<script lang="ts">
  import { Tooltip, SeverityBar } from '$lib/components';
  import { Button } from '$lib/components/ui/button';
  import { Card } from '$lib/components/ui/card';
  import { isDarkMode } from '$lib/stores/theme';
  import store from '$lib/stores/app.svelte';
  import { isForecastRange } from '$lib/stores/constants';
  import type { SessionPrediction } from '$lib/types';
  import { dayAbbrMap, roundFloat } from '$lib/utils';
  import { DateTime } from 'luxon';
  import type { Snippet } from 'svelte';

  interface Props {
    isMapView?: boolean;
    selectedDay: string | null;
    sessionPredictions: SessionPrediction[];
    onDayChange: (date: string, index: number) => void;
    children?: Snippet;
  }

  let {
    isMapView = $bindable(false),
    selectedDay,
    sessionPredictions,
    onDayChange,
    children
  }: Props = $props();

  let prediction = $derived(sessionPredictions.find(({ date }) => selectedDay === date));

  let scores = $derived(
    prediction
      ? Object.entries(prediction.predictions)
          .map(([key, value]) => ({
            color: store.eventLevels?.[+key]?.color?.[$isDarkMode ? 'dark' : 'light'],
            eventLevel: store.eventLevels?.[+key],
            title: key,
            value: roundFloat(value * 100)
          }))
          .filter(({ eventLevel }) => !!eventLevel)
      : []
  );

  let warnedScore = $derived(scores.reduce((res, item) => (item.value > res.value ? item : res)));

  let weekDay = $derived(prediction ? DateTime.fromISO(prediction.date).toFormat('cccc') : '');

  $effect(() => {
    if (warnedScore) store.selectedWarningColor = warnedScore.color;
  });

  function getWarningColor(predictions: SessionPrediction['predictions']) {
    const ps = Object.entries(predictions).map(([, value], i) => ({
      color: store.eventLevels?.[i]?.color?.[$isDarkMode ? 'dark' : 'light'],
      value
    }));
    return ps.reduce((res, item) => (item.value > res.value ? item : res)).color;
  }
</script>

<div class="forecast-card-mobile flex flex-col gap-sm w-full">
  <div class="days flex flex-wrap gap-1 justify-center">
    {#each sessionPredictions as prediction, index}
      <Button
        class="day-button gap-0 !p-[3px] text-xs {selectedDay === prediction.date
          ? 'selected'
          : ''}"
        size="sm"
        onclick={() => onDayChange(prediction.date, index)}
        variant="outline"
      >
        {dayAbbrMap[DateTime.fromISO(prediction.date).weekday]}
        <span
          class="material-icons score-icon"
          style:--color={getWarningColor(prediction.predictions)}
        >
          warning
        </span>
      </Button>
    {/each}
    {#if children}
      <Button class="p-1 text-xs" size="sm" onclick={() => (isMapView = !isMapView)}>
        {isMapView ? 'Show Card' : 'Show Map'}
      </Button>
    {/if}
  </div>

  {#if isMapView}
    <div class="flex">
      {@render children?.()}
    </div>
  {:else}
    <Card class="display-card flex-1">
      <div>
        <div class="header">
          {weekDay}
          <span class="material-icons score-icon" style:--color={warnedScore?.color}>
            warning
          </span>
        </div>
        <div class="sub-header">
          {prediction?.date}
        </div>
      </div>

      <div class="scores">
        {#each scores as score}
          <div class="score-row flex">
            {#if score.eventLevel}
              <Tooltip align="start">
                {#snippet content()}
                  <div class="tooltip-container">
                    <div class="header">
                      <h3>{score.eventLevel.eventDisplayName}</h3>
                    </div>
                    <div class="content">
                      <span>{score.eventLevel.description}</span>
                    </div>
                  </div>
                {/snippet}
                {#snippet trigger()}
                  <div class="event-level" style:background={score.color}>
                    {score.eventLevel.eventCode}
                  </div>
                {/snippet}
              </Tooltip>
            {/if}
            <SeverityBar
              value={score.value}
              title={score.title}
              color={score.color}
              min={0}
              max={100}
            />
          </div>
        {/each}
        <div class="range mr-1" style:--color={warnedScore?.color}>
          <div class="mr-1 leading-4">
            {#if isForecastRange && typeof prediction?.mid === 'number'}
              <div class="flex items-center gap-xs">
                <div>{roundFloat(prediction.min, 0)}</div>
                <div class="text-lg">{roundFloat(prediction.mid, 0)}</div>
                <div>{roundFloat(prediction.max, 0)}</div>
              </div>
            {:else if prediction}
              <div>{roundFloat(prediction.min, 0)} / {roundFloat(prediction.max, 0)}</div>
            {/if}
          </div>
          <div class="outages-label">{store.capitalizedOutagesLabel}</div>
        </div>
      </div>
    </Card>
  {/if}
</div>

<style>
  .header {
    align-items: center;
    display: flex;
    font-size: 1rem;
    margin-bottom: 0.125rem;
  }

  .sub-header {
    font-size: 0.75rem;
    margin-bottom: 0.5rem;
  }

  .score-icon {
    color: var(--color);
    font-size: 1rem !important;
    margin-left: 1px;
  }

  .range {
    color: var(--color-text);
    font-size: 0.75rem;
    margin-top: 8px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .outages-label {
    display: inline;
    border-bottom: 1px solid var(--color);
  }

  .event-level {
    border-radius: 50%;
    color: var(--color-text);
    font-size: 10px;
    line-height: 14px;
    height: 14px;
    width: 14px;
  }

  :global(.forecast-card-mobile .day-button) {
    background: var(--color-component);
    border: 1px solid var(--color-border);
    padding: 6px;
  }

  :global(.forecast-card-mobile .day-button.selected) {
    box-shadow: 0 0 0 2px var(--color-primary);
  }

  :global(.card-map-button) {
    background: var(--color-primary);
  }
</style>
