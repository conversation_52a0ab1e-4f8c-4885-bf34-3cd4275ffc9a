<script lang="ts">
  import { SeverityBar, Tooltip } from '$lib/components';
  import { Card } from '$lib/components/ui/card';
  import { isDarkMode } from '$lib/stores/theme';
  import store from '$lib/stores/app.svelte';
  import { isForecastRange } from '$lib/stores/constants';
  import type { SessionPrediction } from '$lib/types';
  import { roundFloat } from '$lib/utils';
  import { DateTime } from 'luxon';

  interface Props {
    data: SessionPrediction;
    selected: boolean;
    onClick: () => void;
    onKeydown: (e: KeyboardEvent) => void;
  }

  let { data, selected, onClick, onKeydown }: Props = $props();

  let scores = $derived(
    data
      ? Object.entries(data.predictions)
          .map(([key, value]) => ({
            color: store.eventLevels?.[+key]?.color?.[$isDarkMode ? 'dark' : 'light'],
            eventLevel: store.eventLevels[+key],
            title: key,
            value: roundFloat(value * 100)
          }))
          .filter(({ eventLevel }) => !!eventLevel)
      : []
  );

  let warnedScore = $derived(scores.reduce((res, item) => (item.value > res.value ? item : res)));

  let weekDay = $derived(data ? DateTime.fromISO(data.date).toFormat('cccc') : '');

  $effect(() => {
    if (warnedScore && selected) store.selectedWarningColor = warnedScore.color;
  });
</script>

<Card class="forecast-card cursor-pointer" {selected} onclick={onClick} onkeydown={onKeydown}>
  <div>
    <div class="header">
      {weekDay}
      <span class="material-icons score-icon" style:--color={warnedScore?.color}> warning </span>
    </div>
    <div class="sub-header">
      {data.date}
    </div>
  </div>

  <div class="scores">
    {#each scores as score (score.title)}
      <div class="score-row flex">
        {#if score.eventLevel}
          <Tooltip align="start">
            {#snippet content()}
              <div class="tooltip-container">
                <div class="header">
                  <h3>{score.eventLevel.eventDisplayName}</h3>
                </div>
                <div class="content">
                  <span>{score.eventLevel.description}</span>
                </div>
              </div>
            {/snippet}
            {#snippet trigger()}
              <div class="event-level" style:background={score.color}>
                {score.eventLevel.eventCode}
              </div>
            {/snippet}
          </Tooltip>
        {/if}

        <SeverityBar
          value={score.value}
          title={score.title}
          color={score.color}
          min={0}
          max={100}
        />
      </div>
    {/each}
    <div class="range mr-1" style:--color={warnedScore?.color}>
      <div class="mr-1 leading-4">
        {#if isForecastRange && typeof data.mid === 'number'}
          <div class="flex items-center gap-xs">
            <div>{roundFloat(data.min, 0)}</div>
            <div class="text-lg">{roundFloat(data.mid, 0)}</div>
            <div>{roundFloat(data.max, 0)}</div>
          </div>
        {:else}
          <div>{roundFloat(data.min, 0)} / {roundFloat(data.max, 0)}</div>
        {/if}
      </div>
      <div class="outages-label">{store.capitalizedOutagesLabel}</div>
    </div>
  </div>
</Card>

<style>
  :global(.card.forecast-card) {
    cursor: pointer;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .header {
    font-size: 1rem;
    margin-bottom: 0.125rem;

    display: flex;
    align-items: center;
  }

  .sub-header {
    font-size: 0.75rem;
    margin-bottom: 0.5rem;
  }

  .score-icon {
    color: var(--color);
    font-size: 1rem !important;
    margin-left: 0.25rem;
  }

  .range {
    color: var(--color-text);
    font-size: 0.75rem;
    margin-top: 8px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .outages-label {
    display: inline;
    border-bottom: 1px solid var(--color);
  }

  .event-level {
    border-radius: 50%;
    color: var(--color-text);
    font-size: 10px;
    line-height: 14px;
    height: 14px;
    width: 14px;
  }
</style>
