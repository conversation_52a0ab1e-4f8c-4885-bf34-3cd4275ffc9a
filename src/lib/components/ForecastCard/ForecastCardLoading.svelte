<script lang="ts">
  import { Skeleton } from '$lib/components/ui/skeleton';
  import { Card } from '$lib/components/ui/card';
  import store from '$lib/stores/app.svelte';

  interface Props {
    class?: string;
  }

  let { class: className = '' }: Props = $props();
</script>

<Card class="flex flex-col gap-xs {className}">
  <Skeleton class="h-[18px] w-[70px]" />
  <Skeleton class="h-[17px] w-[70px]" />
  <!--eslint-disable @typescript-eslint/no-magic-numbers -->
  <!--eslint-disable @typescript-eslint/no-unused-vars -->
  {#each store.eventLevels ?? Array(5) as _}
    <div class="flex items-center justify-center gap-sm mb-sm">
      <Skeleton class="overlow-hidden w-[20px] h-[15px]" />
      <Skeleton class="overlow-hidden w-full h-[15px]" />
    </div>
  {/each}
  <Skeleton class="ml-auto w-[80px]" />
</Card>
