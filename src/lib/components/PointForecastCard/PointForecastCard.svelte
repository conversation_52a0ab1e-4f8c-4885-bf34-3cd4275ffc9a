<script lang="ts">
  import { base } from '$app/paths';
  import { DateTime } from 'luxon';
  import { Components, type DailyEvent, type RegionalWeather, type ViewLevel } from '$lib/types';
  import { findEventLevel, getDailyWeatherIcon } from '$lib/config/utils';
  import { formatNumbers } from '$lib/utils';
  import { isDarkMode } from '$lib/stores/theme';
  import store from '$lib/stores/app.svelte';
  import { Tooltip } from '$lib/components';
  import { Card } from '$lib/components/ui/card';
  import { formatRemaining, MIN_PRECIP_THRESHOLD, MIN_THREAT_LEVEL } from './utils';

  interface Props {
    date: string;
    midnightCustomersAffectedSum?: number | null;
    midnightOutagesSum?: number | null;
    outageData: DailyEvent;
    regionName?: string;
    selected: boolean;
    viewLevel?: ViewLevel;
    weatherData: RegionalWeather;
    onClick: () => void;
    onKeydown: (e: KeyboardEvent) => void;
  }

  let {
    date,
    midnightCustomersAffectedSum = null,
    midnightOutagesSum = null,
    outageData,
    regionName = '',
    selected = false,
    viewLevel = 'system',
    weatherData,
    onClick,
    onKeydown
  }: Props = $props();

  let eventLevel = $derived(
    findEventLevel(outageData?.outages + (midnightOutagesSum ?? 0), viewLevel, regionName)
  );
  let eventData = $derived(store.componentsConfig?.[Components.POINT_FORECAST_CARDS]?.items ?? []);
  let icon = $derived(getDailyWeatherIcon(weatherData, +eventLevel.eventCode));
</script>

<Card
  class="forecast-card text-sm cursor-pointer !p-0"
  {selected}
  onclick={onClick}
  onkeydown={onKeydown}
>
  <div
    class="p-xs text-center border-b border-solid rounded-t-inner !border-borderColor header"
    style="--color: {eventLevel?.color?.[$isDarkMode ? 'dark' : 'light']}"
  >
    <Tooltip>
      {#snippet content()}
        <div class="tooltip-container">
          <div class="header !bg-transparent">
            <h3>
              Threat Level {eventLevel.eventCode}
              {eventLevel.category ? `- ${eventLevel.category}` : ''}
            </h3>
          </div>
          <div class="content">
            <span> {eventLevel.description}</span>
          </div>
        </div>
      {/snippet}
      {#snippet trigger()}
        <div class="flex justify-center items-center gap-md h-[36px]">
          {#if Number(eventLevel.eventCode) >= MIN_THREAT_LEVEL}
            <span class="material-icons !text-3xl"> warning </span>
          {/if}
          <div>
            <p class="text-xs font-semibold">
              {DateTime.fromISO(date).toFormat('cccc')}
            </p>
            <p class="text-xs forecast-date">{date}</p>
          </div>
        </div>
      {/snippet}
    </Tooltip>
  </div>

  <div class="flex flex-wrap gap-sm p-md justify-center">
    {#if eventData.length}
      {#each eventData as item}
        <div class="flex flex-col items-center w-[115px]">
          {#if item.field === 'outages' && midnightOutagesSum !== null}
            <span class="text-accentText">Rem. {item.label}</span>
            <span class="text-xs">
              {formatRemaining(outageData?.outages, midnightOutagesSum)}
            </span>
          {:else if item.field === 'customersAffected' && midnightCustomersAffectedSum !== null}
            <span class="text-accentText">Rem. {item.label}</span>
            <span class="text-xs">
              {formatRemaining(outageData?.customersAffected, midnightCustomersAffectedSum)}
            </span>
          {:else}
            <span class="text-accentText">{item.label}</span>
            <span class="text-xs">
              {formatNumbers(outageData?.[item.field])}
            </span>
          {/if}
        </div>
      {/each}
    {:else}
      <span class="text-accentText">No Data Available</span>
    {/if}
  </div>

  <div
    class="flex flex-wrap gap-md py-md px-sm rounded-md border-t border-solid border-borderColor rounded-t-none justify-evenly"
  >
    <Tooltip align="start">
      {#snippet content()}
        {icon.description}
      {/snippet}
      {#snippet trigger()}
        <img
          src={`${base}/images/weather/${icon.type}.svg`}
          class="weatherIcon mb-sm"
          alt={icon.type}
          width="48"
          height="48"
        />
      {/snippet}
    </Tooltip>

    <div class="flex gap-md">
      <div class="flex flex-col gap-sm items-center text-center">
        <span class="text-accentText">Temp.</span>
        <span class="text-xs"
          >{formatNumbers(weatherData.weatherAttributes.temperatureMin)} °F
          <span class="text-xs text-center block">min</span></span
        >
        <span class="mt-2 text-xs">
          {formatNumbers(weatherData.weatherAttributes.temperatureMax)} °F
          <span class="text-xs text-center block">max</span></span
        >
      </div>
      <div class="flex flex-col gap-sm items-center text-center">
        <span class="text-accentText">Wind</span>
        <span class="text-xs"
          >{formatNumbers(weatherData.weatherAttributes.windSustained)} mph
          <span class="text-xs text-center block">avg.</span></span
        >
        <span class="mt-2 text-xs">
          {formatNumbers(weatherData.weatherAttributes.windGustMax)} mph
          <span class="text-xs text-center block">max gusts</span>
        </span>
      </div>
      <div class="flex flex-col gap-sm items-center text-center">
        <span class="text-accentText">Prcp.</span>
        <span class="text-xs">
          {formatNumbers(
            weatherData?.weatherAttributes.precip < MIN_PRECIP_THRESHOLD
              ? 0
              : weatherData?.weatherAttributes.precip,
            1
          )} in
          <span class="text-xs text-center block">max</span>
        </span>
      </div>
    </div>
  </div>
</Card>

<style>
  .header {
    background-color: var(--color);
  }
</style>
