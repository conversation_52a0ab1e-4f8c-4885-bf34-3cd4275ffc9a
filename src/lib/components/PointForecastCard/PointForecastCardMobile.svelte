<script lang="ts">
  import { base } from '$app/paths';
  import { Tooltip } from '$lib/components';
  import { Card } from '$lib/components/ui/card';
  import { Button } from '$lib/components/ui/button';
  import { findEventLevel, getDailyWeatherIcon } from '$lib/config/utils';
  import { isDarkMode } from '$lib/stores/theme';
  import store from '$lib/stores/app.svelte';
  import { Components, type DailyEvent, type RegionalWeather, type ViewLevel } from '$lib/types';
  import { dayAbbrMap, formatNumbers } from '$lib/utils';
  import { formatRemaining, MIN_PRECIP_THRESHOLD, MIN_THREAT_LEVEL } from './utils';
  import { DateTime } from 'luxon';
  import type { Snippet } from 'svelte';

  interface Props {
    groupedSystemDailyEvents: Record<string, DailyEvent>;
    groupedSystemWeatherData: Record<string, RegionalWeather>;
    isCurrentDay?: boolean;
    isMapView?: boolean;
    midnightCustomersAffectedSum?: number | null;
    midnightOutagesSum?: number | null;
    regionName?: string;
    selectedDay: string;
    viewLevel?: ViewLevel;
    onDayChange: (date: string, index: number) => void;
    children?: Snippet;
  }

  let {
    groupedSystemDailyEvents = {},
    groupedSystemWeatherData = {},
    isCurrentDay = false,
    isMapView = $bindable(false),
    midnightCustomersAffectedSum = null,
    midnightOutagesSum = null,
    regionName = '',
    selectedDay,
    viewLevel = 'system',
    onDayChange,
    children
  }: Props = $props();

  let weatherData = $derived(groupedSystemWeatherData?.[selectedDay]);
  let outageData = $derived(groupedSystemDailyEvents?.[selectedDay]);
  let eventLevel = $derived(
    findEventLevel(
      outageData?.outages + (isCurrentDay && midnightOutagesSum ? midnightOutagesSum : 0),
      viewLevel,
      regionName
    )
  );
  let eventData = $derived(store.componentsConfig?.[Components.POINT_FORECAST_CARDS]?.items ?? []);
  let icon = $derived(getDailyWeatherIcon(weatherData, Number(eventLevel.eventCode)));
</script>

<div class="forecast-card-mobile flex flex-col gap-sm w-full">
  <div class="days flex flex-wrap gap-1 justify-center">
    {#each Object.entries(groupedSystemDailyEvents) as [date, dailyEvent], index}
      <Button
        class="day-button gap-0 !p-[3px] text-xs {selectedDay === date ? 'selected' : ''}"
        size="sm"
        onclick={() => onDayChange(date, index)}
        variant="outline"
      >
        {dayAbbrMap[DateTime.fromISO(date).weekday]}
        <span
          class="material-icons score-icon"
          style="--color: {findEventLevel(
            dailyEvent.outages + ((index === 0 && midnightOutagesSum) || 0),
            viewLevel,
            regionName
          ).color?.[$isDarkMode ? 'dark' : 'light']}"
        >
          warning
        </span>
      </Button>
    {/each}
    {#if children}
      <Button class="p-1 text-xs" size="sm" onclick={() => (isMapView = !isMapView)}>
        {isMapView ? 'Show Card' : 'Show Map'}
      </Button>
    {/if}
  </div>

  {#if isMapView}
    <div class="flex">
      {@render children?.()}
    </div>
  {:else}
    <Card class="!p-0 !cursor-default !w-full">
      <div
        class="p-sm text-center border-b border-solid rounded-t-inner !border-borderColor header"
        style="--color: {eventLevel?.color?.[$isDarkMode ? 'dark' : 'light']}"
      >
        <Tooltip>
          {#snippet content()}
            <div class="tooltip-container">
              <div class="header !bg-transparent">
                <h3>
                  Threat Level {eventLevel.eventCode}
                  {eventLevel.category ? `- ${eventLevel.category}` : ''}
                </h3>
              </div>
              <div class="content">
                <span> {eventLevel.description}</span>
              </div>
            </div>
          {/snippet}
          {#snippet trigger()}
            <div class="flex justify-center items-center gap-md">
              {#if Number(eventLevel.eventCode) >= MIN_THREAT_LEVEL}
                <span class="material-icons !text-3xl"> warning </span>
              {/if}
              <div>
                <p class="text-xs font-semibold"></p>
                <p>{selectedDay}</p>
                <p class="font-semibold">{DateTime.fromISO(selectedDay).toFormat('cccc')}</p>
              </div>
            </div>
          {/snippet}
        </Tooltip>
      </div>
      <div class="flex flex-wrap gap-md py-md px-sm justify-evenly">
        {#if eventData.length}
          {#each eventData as item}
            <div class="flex flex-col items-center w-[130px]">
              {#if item.field === 'outages' && isCurrentDay && midnightOutagesSum !== null}
                <span class="text-accentText">Rem. {item.label}</span>
                <span class="text-sm">
                  {formatRemaining(outageData?.outages, midnightOutagesSum)}
                </span>
              {:else if item.field === 'customersAffected' && isCurrentDay && midnightCustomersAffectedSum !== null}
                <span class="text-accentText">Rem. {item.label}</span>
                <span class="text-sm">
                  {formatRemaining(outageData?.customersAffected, midnightCustomersAffectedSum)}
                </span>
              {:else}
                <span class="text-accentText">{item.label}</span>
                <span class="text-sm">
                  {formatNumbers(outageData?.[item.field])}
                </span>
              {/if}
            </div>
          {/each}
        {:else}
          <span class="text-accentText">No Data Available</span>
        {/if}
      </div>
      <div
        class="flex flex-wrap gap-lg py-md px-sm rounded-md border-t border-solid !border-borderColor rounded-t-none justify-evenly items-center"
      >
        <Tooltip>
          {#snippet content()}
            {icon.description}
          {/snippet}
          {#snippet trigger()}
            <img
              src={`${base}/images/weather/${icon.type}.svg`}
              class="weatherIcon mb-sm"
              alt={icon.type}
              width="65"
              height="65"
            />
          {/snippet}
        </Tooltip>
        <div class="flex gap-lg">
          <div class="flex flex-col gap-sm items-center text-center">
            <span class="text-accentText">Temp.</span>
            <span class="text-sm">
              {formatNumbers(weatherData?.weatherAttributes.temperatureMin)} °F (min)
            </span>
            <span class="text-sm">
              {formatNumbers(weatherData?.weatherAttributes.temperatureMax)} °F (max)
            </span>
          </div>
          <div class="flex flex-col gap-sm items-center text-center">
            <span class="text-accentText">Wind</span>
            <span class="text-sm">
              {formatNumbers(weatherData?.weatherAttributes.windSustained)} mph avg.
            </span>
            <span class="text-sm">
              {formatNumbers(weatherData?.weatherAttributes.windGustMax)} mph gusts (max)
            </span>
          </div>
          <div class="flex flex-col gap-sm items-center text-center">
            <span class="text-accentText">Prcp.</span>
            <span class="text-sm">
              {formatNumbers(
                weatherData?.weatherAttributes.precip < MIN_PRECIP_THRESHOLD
                  ? 0
                  : weatherData?.weatherAttributes.precip,
                1
              )} in (max)
            </span>
          </div>
        </div>
      </div>
    </Card>
  {/if}
</div>

<style>
  .header {
    background-color: var(--color);
  }

  .score-icon {
    color: var(--color);
    font-size: 1rem !important;
    margin-left: 1px;
  }

  :global(.forecast-card-mobile .day-button) {
    background: var(--color-component);
    border: 1px solid var(--color-border);
    padding: 6px;
  }

  :global(.forecast-card-mobile .day-button.selected) {
    box-shadow: 0 0 0 2px var(--color-primary);
  }

  :global(.card-map-button) {
    background: var(--color-primary);
  }
</style>
