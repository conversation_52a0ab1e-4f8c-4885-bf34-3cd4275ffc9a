<script lang="ts">
  import { base } from '$app/paths';
  import store from '$lib/stores/app.svelte';
  import { Card } from '$lib/components/ui/card';

  interface Props {
    class?: string;
  }

  let { class: className = '' }: Props = $props();
</script>

<Card class="forecast-card !p-0 !cursor-pointer overflow-hidden w-full !h-[255px] {className}">
  <div class="p-2 text-center border-b border-solid !border-borderColor header">
    <p>Unknown Date</p>
  </div>
  <div class="flex flex-wrap gap-md py-md px-sm justify-evenly">
    <div class="flex flex-col items-center">
      <span class="text-xs text-accentText font-semibold">{store.capitalizedOutagesLabel}</span>
      <span>-</span>
    </div>
    <div class="flex flex-col items-center">
      <span class="text-xs text-accentText font-semibold">Customers</span>
      <span>-</span>
    </div>
  </div>
  <div
    class="flex flex-wrap gap-md py-md px-sm rounded-md border-t border-solid !border-borderColor rounded-t-none justify-evenly"
  >
    <img
      src={`${base}/images/weather/unknown.svg`}
      class="weatherIcon"
      alt="unknown"
      width="60"
      height="60"
    />
    <div class="flex gap-md">
      <div class="flex flex-col gap-sm items-center text-center">
        <span class="text-accentText">Temp.</span>
        <span class="text-xs"
          >- °F
          <span class="text-xs text-center block">min</span></span
        >
        <span class="mt-2 text-xs">
          - °F
          <span class="text-xs text-center block">max</span></span
        >
      </div>
      <div class="flex flex-col gap-sm items-center text-center">
        <span class="text-accentText">Wind</span>
        <span class="text-xs"
          >- mph
          <span class="text-xs text-center block">avg.</span></span
        >
        <span class="mt-2 text-xs">
          - mph
          <span class="text-xs text-center block">gusts</span>
        </span>
      </div>
      <div class="flex flex-col gap-sm items-center text-center">
        <span class="text-accentText">Prcp.</span>
        <span class="text-xs"> - in </span>
      </div>
    </div>
  </div>
</Card>
