<script lang="ts">
  import { Skeleton } from '$lib/components/ui/skeleton';
  import { Card } from '$lib/components/ui/card';

  interface Props {
    class?: string;
  }

  let { class: className = '' }: Props = $props();
</script>

<Card class="!h-[255px] flex flex-col gap-xs {className}">
  <div
    class="flex flex-col justify-center items-center gap-xs border-b border-solid !border-borderColor"
  >
    <Skeleton class="h-[22px] w-[120px]" />
    <Skeleton class="h-[16px] w-[70px] mb-sm" />
  </div>

  <div class="p-sm flex justify-evenly border-b border-solid !border-borderColor">
    <div class="flex flex-col justify-center items-center gap-xs">
      <Skeleton class="h-[14px] w-[80px]" />
      <Skeleton class="h-[16px] w-[40px]" />
    </div>
    <div class="flex flex-col justify-center items-center gap-xs">
      <Skeleton class="h-[14px] w-[80px]" />
      <Skeleton class="h-[16px] w-[40px]" />
    </div>
  </div>
  <div class="p-sm flex justify-evenly gap-sm mt-sm">
    <Skeleton class="w-[48px] h-[48px] self-start" />
    <div class="flex flex-col justify-center items-center gap-xs">
      <Skeleton class="h-[16px] w-[40px]" />
      <Skeleton class="h-[14px] w-[25px]" />

      <Skeleton class="h-[16px] w-[40px]" />
      <Skeleton class="h-[14px] w-[25px]" />
    </div>
    <div class="flex flex-col justify-center items-center gap-xs">
      <Skeleton class="h-[16px] w-[40px]" />
      <Skeleton class="h-[14px] w-[25px]" />

      <Skeleton class="h-[16px] w-[40px]" />
      <Skeleton class="h-[14px] w-[25px]" />
    </div>
    <div class="flex flex-col justify-center items-center gap-xs self-start">
      <Skeleton class="h-[16px] w-[40px]" />
      <Skeleton class="h-[14px] w-[25px]" />
    </div>
  </div>
</Card>
