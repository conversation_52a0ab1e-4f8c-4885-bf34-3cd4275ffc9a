<script lang="ts">
  import { cn } from '$lib/utils';
  import type { WithElementRef } from 'bits-ui';
  import type { HTMLAttributes } from 'svelte/elements';

  type TimeCellProps = WithElementRef<HTMLAttributes<HTMLDivElement>>;

  const { children, ...props }: TimeCellProps = $props();
</script>

<div
  role="button"
  tabindex="0"
  class={cn(
    'w-9 cursor-pointer rounded-md p-2 text-right font-normal focus-within:relative hover:bg-accent hover:text-accent-foreground data-[selected=true]:bg-primary data-[selected=true]:text-white m-[3px]'
  )}
  {...props}
>
  {@render children?.()}
</div>
