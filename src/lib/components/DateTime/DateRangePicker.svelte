<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import { Label } from '$lib/components/ui/label';
  import * as Popover from '$lib/components/ui/popover';
  import { RangeCalendar } from '$lib/components/ui/range-calendar';
  import { timezone } from '$lib/stores/constants';
  import { cn } from '$lib/utils';
  import { CalendarDate, DateFormatter, type DateValue } from '@internationalized/date';
  import { DateTime } from 'luxon';
  import { onMount } from 'svelte';

  interface DateRange {
    start: DateValue | undefined;
    end: DateValue | undefined;
  }

  interface Props {
    class?: string;
    format?: string;
    isInvalid?: boolean;
    invalidText?: string;
    label?: string;
    maxDate?: string | null;
    minDate?: string | null;
    valueFrom: string;
    valueTo: string;
    onChange: (value: { from: string; to: string }) => void;
  }

  const df = new DateFormatter('en-US', { dateStyle: 'short' });

  let {
    class: className = '',
    format = 'MM/dd/yyyy',
    isInvalid = false,
    invalidText = '',
    label = '',
    maxDate = '',
    minDate = '',
    valueFrom,
    valueTo,
    onChange
  }: Props = $props();

  let isOpen = $state<boolean>(false);
  let minDateValue = $state<DateValue | undefined>();
  let maxDateValue = $state<DateValue | undefined>();
  let selectedDateRange = $state<DateRange | undefined>();
  let startDateValue = $state<DateValue | undefined>();

  onMount(() => {
    if (valueFrom && valueTo) {
      const parsedFrom = DateTime.fromFormat(valueFrom, format);
      const parsedTo = DateTime.fromFormat(valueTo, format);
      const start = new CalendarDate(parsedFrom.year, parsedFrom.month, parsedFrom.day);
      const end = new CalendarDate(parsedTo.year, parsedTo.month, parsedTo.day);
      selectedDateRange = { start, end };
    }
    if (minDate) {
      const parsed = DateTime.fromFormat(minDate, format);
      minDateValue = new CalendarDate(parsed.year, parsed.month, parsed.day);
    }
    if (maxDate) {
      const parsed = DateTime.fromFormat(maxDate, format);
      maxDateValue = new CalendarDate(parsed.year, parsed.month, parsed.day);
    }
  });

  function handleValueChange(dateRange: DateRange) {
    selectedDateRange = dateRange;

    if (dateRange.start && dateRange.end) {
      const dtFrom = DateTime.fromISO(dateRange.start.toString(), { zone: timezone });
      const dtTo = DateTime.fromISO(dateRange.end.toString(), { zone: timezone });
      const value = { from: dtFrom.toFormat(format), to: dtTo.toFormat(format) };
      onChange(value);
      isOpen = false;
    }
  }
</script>

<div class="date-picker flex flex-col {className}">
  {#if label}
    <Label class="text-xs mb-2">{label}</Label>
  {/if}

  <Popover.Root bind:open={isOpen}>
    <Popover.Trigger>
      {#snippet child({ props })}
        <Button
          variant="outline"
          class={cn(
            'justify-start',
            !selectedDateRange && 'text-muted-foreground',
            isInvalid ? 'border-destructive' : ''
          )}
          {...props}
        >
          <span class="material-icons">calendar_today</span>
          {#if selectedDateRange && selectedDateRange.start}
            {#if selectedDateRange.end}
              {df.format(selectedDateRange.start.toDate(timezone))} -
              {df.format(selectedDateRange.end.toDate(timezone))}
            {:else}
              {df.format(selectedDateRange.start.toDate(timezone))}
            {/if}
          {:else if startDateValue}
            {df.format(startDateValue.toDate(timezone))}
          {:else}
            Select a date
          {/if}
        </Button>
      {/snippet}
    </Popover.Trigger>

    <Popover.Content class="w-auto p-0">
      <RangeCalendar
        maxValue={maxDateValue}
        minValue={minDateValue}
        value={selectedDateRange}
        onStartValueChange={(dateValue) => (startDateValue = dateValue)}
        onValueChange={handleValueChange}
      />
    </Popover.Content>
  </Popover.Root>

  {#if isInvalid && invalidText}
    <p class="text-xs px-2 text-destructive">{invalidText}</p>
  {/if}
</div>
