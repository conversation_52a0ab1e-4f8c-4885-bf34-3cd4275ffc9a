<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import { Calendar } from '$lib/components/ui/calendar';
  import { Label } from '$lib/components/ui/label';
  import * as Popover from '$lib/components/ui/popover';
  import { timezone } from '$lib/stores/constants';
  import { cn } from '$lib/utils';
  import { CalendarDate, DateFormatter, type DateValue } from '@internationalized/date';
  import { DateTime } from 'luxon';
  import { onMount } from 'svelte';

  interface Props {
    class?: string;
    format?: string;
    isInvalid?: boolean;
    invalidText?: string;
    label?: string;
    maxDate?: string | null;
    minDate?: string | null;
    value: string;
    onChange: (value: string) => void;
  }

  const df = new DateFormatter('en-US', { dateStyle: 'short' });

  let {
    class: className = '',
    format = 'MM/dd/yyyy',
    isInvalid = false,
    invalidText = '',
    label = '',
    maxDate = '',
    minDate = '',
    value,
    onChange
  }: Props = $props();

  let isOpen = $state<boolean>(false);
  let minDateValue = $state<DateValue | undefined>();
  let maxDateValue = $state<DateValue | undefined>();
  let selectedDateValue = $state<DateValue | undefined>();

  onMount(() => {
    if (value) {
      const parsed = DateTime.fromFormat(value, format);
      selectedDateValue = new CalendarDate(parsed.year, parsed.month, parsed.day);
    }
    if (minDate) {
      const parsed = DateTime.fromFormat(minDate, format);
      minDateValue = new CalendarDate(parsed.year, parsed.month, parsed.day);
    }
    if (maxDate) {
      const parsed = DateTime.fromFormat(maxDate, format);
      maxDateValue = new CalendarDate(parsed.year, parsed.month, parsed.day);
    }
  });

  function handleValueChange(dateValue: DateValue | undefined) {
    selectedDateValue = dateValue;

    if (dateValue) {
      const dt = DateTime.fromISO(dateValue.toString(), { zone: timezone });
      onChange(dt.toFormat(format));
      isOpen = false;
    }
  }
</script>

<div class="date-picker flex flex-col {className}">
  {#if label}
    <Label class="text-xs mb-2">{label}</Label>
  {/if}

  <Popover.Root bind:open={isOpen}>
    <Popover.Trigger>
      {#snippet child({ props })}
        <Button
          variant="outline"
          class={cn(
            'justify-start',
            !selectedDateValue && 'text-muted-foreground',
            isInvalid ? 'border-destructive' : ''
          )}
          {...props}
        >
          <span class="material-icons">calendar_today</span>
          {selectedDateValue ? df.format(selectedDateValue.toDate(timezone)) : 'Select a date'}
        </Button>
      {/snippet}
    </Popover.Trigger>

    <Popover.Content class="w-auto p-0">
      <Calendar
        initialFocus
        maxValue={maxDateValue}
        minValue={minDateValue}
        preventDeselect={true}
        type="single"
        value={selectedDateValue}
        onValueChange={handleValueChange}
      />
    </Popover.Content>
  </Popover.Root>

  {#if isInvalid && invalidText}
    <p class="text-xs px-2 text-destructive">{invalidText}</p>
  {/if}
</div>
