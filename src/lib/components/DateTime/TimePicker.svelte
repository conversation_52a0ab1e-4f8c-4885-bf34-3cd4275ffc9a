<script lang="ts">
  /* eslint-disable @typescript-eslint/no-magic-numbers */
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  import { Label } from '$lib/components/ui/label';
  import * as Popover from '$lib/components/ui/popover';
  import { cn } from '$lib/utils';
  import TimeCell from './TimeCell.svelte';
  import { onMount } from 'svelte';

  type Meridiem = 'AM' | 'PM';

  interface Props {
    class?: string;
    disabled?: boolean;
    isInvalid?: boolean;
    invalidText?: string;
    label?: string;
    value: string;
    onChange: (value: string) => void;
  }

  const hours = [...Array(13).keys()].slice(1);
  const minutes = [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55];

  let {
    class: className = '',
    disabled = false,
    isInvalid = false,
    invalidText = '',
    label = '',
    value,
    onChange
  }: Props = $props();

  let hour = $state<number>();
  let minute = $state<string>();
  let meridiem = $state<Meridiem>();

  let isOpen = $state<boolean>(false);
  let selectedTime = $state<string>('');

  onMount(() => {
    if (value) setTimeValues(value);
  });

  function setTimeValues(value: string) {
    const [hourStr, minuteStr] = value.split(':');
    const hour24 = parseInt(hourStr, 10);
    hour = hour24 % 12 || 12;
    minute = minuteStr;
    meridiem = hour24 >= 12 ? 'PM' : 'AM';
    selectedTime = value;
  }

  function leadingZeroFormat(n: number) {
    return n.toString().padStart(2, '0');
  }

  function isTimeValid() {
    return hour && minute && meridiem;
  }

  function setHour(hourSelected: number) {
    hour = hourSelected;
    if (isTimeValid()) setTimeValue();
  }

  function setMinute(minuteSelected: string) {
    minute = minuteSelected;
    if (isTimeValid()) setTimeValue();
  }

  function setMeridiem(meridiemSelected: string) {
    meridiem = meridiemSelected as Meridiem;
    if (isTimeValid()) setTimeValue();
  }

  function to24Hour() {
    if (meridiem === 'AM') {
      return hour === 12 ? '00' : leadingZeroFormat(hour as number);
    } else {
      return hour === 12 ? '12' : String((hour as number) + 12);
    }
  }

  function setTimeValue() {
    selectedTime = `${to24Hour()}:${minute}`;
    onChange(selectedTime);
  }
</script>

<div class="time-picker flex flex-col {className}">
  {#if label}
    <Label class="text-xs mb-2">{label}</Label>
  {/if}

  <Popover.Root bind:open={isOpen}>
    <Popover.Trigger>
      {#snippet child({ props })}
        <Button
          variant="outline"
          class={cn(
            'justify-start',
            !selectedTime && 'text-muted-foreground',
            isInvalid ? 'border-destructive' : ''
          )}
          {...props}
        >
          <span class="material-icons">schedule</span>
          <span>
            {selectedTime ? `${hour}:${minute} ${meridiem}` : 'Select a time'}
          </span>
        </Button>
      {/snippet}
    </Popover.Trigger>

    <Popover.Content class="w-auto p-0">
      <div class="my-2 p-1">
        <Input
          {disabled}
          value={selectedTime}
          type="time"
          onchange={({ currentTarget }) => {
            setTimeValues(currentTarget.value);
            onChange(selectedTime);
          }}
        />
        <div class="flex max-h-64 overflow-y-auto overflow-x-hidden py-2 text-sm">
          <div class="hours max-h-64 snap-y snap-start overflow-y-scroll custom-scroll">
            {#each hours as hourDisplay}
              <TimeCell
                onkeypress={() => setHour(hourDisplay)}
                onclick={() => setHour(hourDisplay)}
                data-selected={hour === hourDisplay || (hour ?? 0) + 12 === hourDisplay}
              >
                {leadingZeroFormat(hourDisplay)}
              </TimeCell>
            {/each}
          </div>
          <div class="minutes max-h-64 snap-y snap-start overflow-y-scroll custom-scroll">
            {#each minutes as minuteDisplay}
              <TimeCell
                onkeypress={() => setMinute(leadingZeroFormat(minuteDisplay))}
                onclick={() => setMinute(leadingZeroFormat(minuteDisplay))}
                data-selected={minute === leadingZeroFormat(minuteDisplay)}
              >
                {leadingZeroFormat(minuteDisplay)}
              </TimeCell>
            {/each}
          </div>
          <div class="meridiem">
            <TimeCell
              onkeypress={() => setMeridiem('AM')}
              onclick={() => setMeridiem('AM')}
              data-selected={meridiem === 'AM'}
            >
              AM
            </TimeCell>
            <TimeCell
              onkeypress={() => setMeridiem('PM')}
              onclick={() => setMeridiem('PM')}
              data-selected={meridiem === 'PM'}
            >
              PM
            </TimeCell>
          </div>
        </div>
      </div>
    </Popover.Content>
  </Popover.Root>

  {#if isInvalid && invalidText}
    <p class="text-xs px-2 text-destructive">{invalidText}</p>
  {/if}
</div>
