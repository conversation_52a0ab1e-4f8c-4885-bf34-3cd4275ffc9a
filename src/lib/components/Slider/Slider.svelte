<script lang="ts">
  import { Label } from '$lib/components/ui/label';
  import { Slider } from '$lib/components/ui/slider';
  import type { Slider as SliderPrimitive } from 'bits-ui';
  import { type WithoutChildrenOrChild } from 'bits-ui';

  interface Props {
    containerClass?: string;
    disabled?: boolean;
    label?: string;
    max: number;
    maxLabel?: number | string;
    min: number;
    minLabel?: number | string;
    orientation?: WithoutChildrenOrChild<SliderPrimitive.RootProps>['orientation'];
    ref?: HTMLElement | null;
    step?: number;
    type?: 'single';
    value: number;
    onChange?: (value: number) => void;
  }

  let {
    containerClass = '',
    disabled = false,
    label = '',
    maxLabel = '',
    minLabel = '',
    orientation = 'horizontal',
    ref = $bindable(null),
    type = 'single',
    value = $bindable(),
    onChange,
    ...restProps
  }: Props = $props();
</script>

<div class="flex flex-col w-full gap-xs {containerClass ? containerClass : ''}">
  {#if label}
    <Label class="text-xs">{label}</Label>
  {/if}
  <div class="flex items-center gap-sm">
    {#if minLabel !== undefined}
      <Label class="text-xs">{minLabel}</Label>
    {/if}

    <Slider
      {disabled}
      {orientation}
      bind:ref
      {type}
      bind:value
      onValueChange={onChange}
      {...restProps}
    />

    {#if maxLabel}
      <Label class="text-xs">{maxLabel}</Label>
    {/if}
  </div>
</div>
