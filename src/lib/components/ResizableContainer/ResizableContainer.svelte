<script lang="ts">
  import { onMount, onDestroy, type Snippet } from 'svelte';
  import { Button } from '$lib/components/ui/button';

  interface Props {
    height?: number | null;
    minHeight?: number;
    className?: string;
    container?: HTMLDivElement | null;
    isExpanded?: boolean;
    children?: Snippet;
  }

  /* eslint-disable @typescript-eslint/no-magic-numbers */
  let {
    height = null,
    minHeight = 350,
    className = '',
    container = null,
    isExpanded = false,
    children
  }: Props = $props();

  const COLLAPSED_HEIGHT = 25;

  let mainElement: HTMLDivElement | undefined = undefined;
  let dragElement: HTMLDivElement | null = null;
  let isDragging = $state(false);
  let isCollapsed = $derived(height <= COLLAPSED_HEIGHT);

  $effect(() => {
    height = isExpanded ? minHeight : COLLAPSED_HEIGHT;
  });

  function onMouseMove(e: MouseEvent) {
    if (!isDragging || !mainElement) return;

    const { bottom } = mainElement.getBoundingClientRect();
    let newHeight = bottom - e.clientY;
    newHeight = Math.max(newHeight, minHeight);

    if (container && newHeight > container.clientHeight) return;

    mainElement.style.height = `${newHeight}px`;
    height = newHeight;
  }

  function startDrag() {
    isDragging = true;
  }

  function stopDrag() {
    isDragging = false;
  }

  function expandHeight() {
    if (!mainElement) return;

    const containerHeight = container?.clientHeight;
    const currentHeight = parseFloat(mainElement.style.height || `${mainElement.offsetHeight}`);
    const isCollapsed = currentHeight <= COLLAPSED_HEIGHT;

    if (isCollapsed) {
      mainElement.style.height = `${minHeight}px`;
      height = minHeight;
    } else {
      mainElement.style.height = `${containerHeight}px`;
      height = containerHeight;
    }
  }

  function collapseHeight() {
    if (!mainElement) return;

    const containerHeight = container?.clientHeight;
    const currentHeight = parseFloat(mainElement.style.height || `${mainElement.offsetHeight}`);

    const isAtFullHeight = Math.abs(currentHeight - containerHeight) <= 1;
    const isAtMinHeight = Math.abs(currentHeight - minHeight) <= 1;
    const isCollapsed = currentHeight <= 1;

    if (isAtFullHeight) {
      mainElement.style.height = `${minHeight}px`;
      height = minHeight;
    } else if (isAtMinHeight) {
      mainElement.style.height = `${COLLAPSED_HEIGHT}px`;
      height = COLLAPSED_HEIGHT;
    } else if (!isCollapsed) {
      mainElement.style.height = `${minHeight}px`;
      height = minHeight;
    }
  }

  onMount(() => {
    dragElement?.addEventListener('mousedown', startDrag);
    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', stopDrag);
  });

  onDestroy(() => {
    dragElement?.removeEventListener('mousedown', startDrag);
    document.removeEventListener('mousemove', onMouseMove);
    document.removeEventListener('mouseup', stopDrag);
  });
</script>

<div class="relative w-full flex flex-col {className}">
  <div
    bind:this={mainElement}
    class="z-50 flex flex-col w-full bg-component {!isCollapsed
      ? 'absolute bottom-0 border border-border rounded-md'
      : 'border-b border-l border-r border-border rounded-b-md -mt-2'}"
    style="height: {height}px;"
    class:transition={!isDragging}
  >
    <div
      bind:this={dragElement}
      class="w-full flex justify-center items-stretch cursor-row-resize gap-lg"
      style="height: {COLLAPSED_HEIGHT}px;"
    >
      <Button
        variant="ghost"
        class={isCollapsed ? 'w-full rounded-t-none' : 'w-fit'}
        size="xs"
        tooltip="Expand"
        onclick={expandHeight}
      >
        <span class="material-icons">
          <span class="material-symbols-outlined">keyboard_arrow_up</span>
        </span>
      </Button>
      {#if !isCollapsed}
        <span class="material-icons">drag_handle</span>
        <Button variant="ghost" size="xs" tooltip="Collapse" onclick={collapseHeight}>
          <span class="material-icons">
            <span class="material-symbols-outlined">keyboard_arrow_down</span>
          </span>
        </Button>
      {/if}
    </div>
    {#if !isCollapsed}
      <div class="h-full flex overflow-hidden border-t border-border">
        {@render children?.()}
      </div>
    {/if}
  </div>
</div>

<style>
  .transition {
    transition: height 0.3s ease-in-out;
  }
</style>
