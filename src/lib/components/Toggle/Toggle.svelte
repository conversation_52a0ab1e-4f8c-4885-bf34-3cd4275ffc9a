<script lang="ts">
  import { Label } from '$lib/components/ui/label';
  import { Switch } from '$lib/components/ui/switch';

  interface Props {
    checked: boolean;
    disabled?: boolean;
    id?: string;
    label?: string;
    ref?: HTMLElement | null;
    onToggle?: (isToggled: boolean) => void;
    className?: string;
    size?: 'sm' | 'rg';
  }

  let {
    checked = $bindable(false),
    disabled = false,
    id = crypto.randomUUID(),
    label = '',
    ref = $bindable(null),
    onToggle,
    className,
    size = 'rg',
    ...restProps
  }: Props = $props();
</script>

<div class="flex items-center gap-2 {className}">
  <Switch bind:checked {disabled} {id} bind:ref onCheckedChange={onToggle} {size} {...restProps} />
  {#if label}
    <Label for={id}>{label}</Label>
  {/if}
</div>
