<script lang="ts">
  import { Skeleton } from '$lib/components/ui/skeleton';
  import { Card } from '$lib/components/ui/card';

  interface Props {
    class?: string;
  }

  let { class: className = '' }: Props = $props();
</script>

<Card class="flex flex-col gap-xs {className}">
  <Skeleton class="h-[40px] w-full" />
  <Skeleton class="h-[300px] w-full" />
  <Skeleton class="h-[30px] w-full" />
  <Skeleton class="h-[340px] w-full" />
</Card>
