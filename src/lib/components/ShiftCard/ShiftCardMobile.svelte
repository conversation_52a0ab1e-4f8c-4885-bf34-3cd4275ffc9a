<script lang="ts">
  import { AgChart } from '$lib/components';
  import { Button } from '$lib/components/ui/button';
  import { Card } from '$lib/components/ui/card';
  import * as Select from '$lib/components/ui/select';
  import {
    customHourFormatter,
    generateChartColorsScale,
    tooltipRenderer
  } from '$lib/config/chart';
  import { getRegionalImpactFillLayer, MAP_BOUNDS } from '$lib/config/map';
  import { getOutageColor } from '$lib/config/utils';
  import { isDarkMode } from '$lib/stores/theme';
  import { timezoneAbbr } from '$lib/stores/constants';
  import store from '$lib/stores/app.svelte';
  import {
    MapStyle,
    type ChartConfig,
    type HourlyShift,
    type LineChartTooltipParams,
    type Shift
  } from '$lib/types';
  import { fitMapToBounds } from '$lib/utils';
  import { DateTime } from 'luxon';
  import mapboxgl from 'mapbox-gl';
  import { onDestroy, onMount } from 'svelte';
  import MapLegend from '../Map/MapLegend.svelte';
  import type { AgAxisLabelFormatterParams } from 'ag-charts-enterprise';

  const regionPopup = new mapboxgl.Popup({
    closeButton: false,
    closeOnClick: false
  });

  interface Props {
    chartConfigs: ChartConfig[];
    hourlyShifts: HourlyShift[];
    shifts: Shift[];
  }

  let { chartConfigs, hourlyShifts, shifts }: Props = $props();

  let hoveredRegion: any = null;
  let mapContainer: HTMLDivElement;

  let map = $state<mapboxgl.Map | undefined>();
  let selectedChartConfig = $state<ChartConfig>({
    attributeName: '',
    displayName: '',
    isDefaultDisplay: false,
    isDisplayed: false,
    type: 'weather',
    yaxis: { range: { max: 0, min: 0 }, unit: '' }
  });
  let selectedHourlyShift = $state<HourlyShift | undefined>(undefined);
  let selectedShift = $state<Shift | undefined>(undefined);
  let shiftOutageRange = $state<string>('');
  let shiftPeriod = $state<string>('');
  let warningColor = $state<string>('var(--color-text)');

  let colorScale = $derived(
    generateChartColorsScale(
      store.regions.map((r) => r.displayName),
      $isDarkMode
    )
  );

  let chartData = $derived(
    selectedHourlyShift
      ? selectedHourlyShift.hourlyZones.map((z) => ({
          date: new Date(z.date),
          group: z.displayName,
          value:
            z.weatherAttributes[
              selectedChartConfig.attributeName as keyof typeof z.weatherAttributes
            ]
        }))
      : []
  );

  const onWatchThemeMode = isDarkMode.subscribe((isDark) => {
    if (map) {
      map.remove();
      // Must pass in the latest value into the function because when this runs,
      // $isDarkMode value has not yet been updated.
      initializeMap(isDark);
    }
  });

  onMount(() => {
    initializeMap($isDarkMode);
    initializeChart();
  });

  onDestroy(() => {
    onWatchThemeMode();
  });

  function initializeMap(isDark: boolean) {
    if (!store.appConfig) return;
    const mapSettings = {
      center: store.appConfig.center.value,
      container: mapContainer,
      style: `mapbox://styles/mapbox/${isDark ? MapStyle.dark : MapStyle.light}`,
      maxBounds: MAP_BOUNDS
    };
    selectedShift = shifts[0];
    selectedHourlyShift = hourlyShifts[0];
    const m = (map = new mapboxgl.Map(mapSettings));
    map.on('load', async () => {
      m.addSource('regions', {
        promoteId: 'name',
        type: 'vector',
        url: store.appConfig?.tilesetUrl.value
      });
      if (store.appConfig?.stateFillLayer) m.addLayer(store.appConfig?.stateFillLayer);
      m.addLayer(getRegionalImpactFillLayer());
      if (store.appConfig?.regionBorderLayer) m.addLayer(store.appConfig?.regionBorderLayer);
      setCardHeader();
      setRegionFeatureStates();
      fitMapToBounds(map, store.appConfig?.maxBounds?.value);
      m.on('mousemove', 'region-fill', onMousemoveRegion);
      m.on('mouseleave', 'region-fill', onMouseLeaveRegion);
      m.on('resize', () => fitMapToBounds(map, store.appConfig?.maxBounds?.value));
    });
  }

  function initializeChart() {
    const chartConfig = chartConfigs.find((c) => c.isDefaultDisplay);
    if (chartConfig) selectedChartConfig = chartConfig;
  }

  function setCardHeader() {
    if (!selectedShift) return;
    const { end, start, outages } = selectedShift;
    const startTime = DateTime.fromISO(start).toFormat('MMM d ha');
    const endTime = DateTime.fromISO(end).toFormat('ha');
    shiftPeriod = `${startTime} - ${endTime}`;
    shiftOutageRange = `${Math.round(outages.min)} - ${Math.round(outages.max)}`;
    const avgOutageCount = (outages.max + outages.min) / 2;
    warningColor = getOutageColor(avgOutageCount) as string;
  }

  function setRegionFeatureStates() {
    selectedShift?.regions.forEach((region: Shift['regions'][number]) => {
      map!.setFeatureState(
        { id: region.name, source: 'regions', sourceLayer: 'regions' },
        { ...region }
      );
    });
  }

  function onMousemoveRegion(e: any) {
    // 1. Highlight hovered territory
    const { sourceLayer, state } = e.features[0];

    if (hoveredRegion?.id) {
      map!.setFeatureState(
        { id: hoveredRegion.id, source: 'regions', sourceLayer },
        { hover: false }
      );
    }

    hoveredRegion = e.features[0];
    if (hoveredRegion?.id) {
      map!.setFeatureState(
        { id: hoveredRegion.id, source: 'regions', sourceLayer },
        { hover: true }
      );
      // 2. Enable popup display
      map!.getCanvas().style.cursor = 'pointer';
      regionPopup
        .setLngLat(e.lngLat)
        .setHTML(
          `<div class="popup-region-info">
            <p class="popup-feature-name">${state.displayName}</p>
            <div class="info-row">
              <span class="label">Impact:</span>
              <span class="value">${state.impact}</span>
            </div>
            <div class="info-row">
              <span class="label">${store.capitalizedOutagesLabel}:</span>
              <span class="value">${state.impactValue}</span>
            </div>
          </div>`
        )
        .addTo(map!);
    }
  }

  function onMouseLeaveRegion() {
    // 1. Clean-up hover layer highlight
    if (hoveredRegion?.id) {
      const { id, sourceLayer } = hoveredRegion;
      map!.setFeatureState({ id, source: 'regions', sourceLayer }, { hover: false });
    }
    hoveredRegion = null;

    // 2. Clean-up hover popup display
    map!.getCanvas().style.cursor = '';
    regionPopup.remove();
  }

  function getButtonLabel({ end, start }: Shift) {
    const startTime = DateTime.fromISO(start).toFormat('ha');
    const endTime = DateTime.fromISO(end).toFormat('ha');
    return `${startTime} - ${endTime}`;
  }

  function getWarningColor({ outages }: Shift) {
    const avgOutageCount = (outages.max + outages.min) / 2;
    return getOutageColor(avgOutageCount);
  }
</script>

<div class="shift-card-mobile w-full flex flex-col">
  <div class="shift-buttons flex p-3 justify-center">
    {#each shifts as shift, i}
      <Button
        class="shift-button gap-0 {selectedShift?.start === shift.start ? 'selected' : ''}"
        variant="outline"
        size="sm"
        onclick={() => {
          selectedShift = shift;
          selectedHourlyShift = hourlyShifts[i];
          setCardHeader();
          setRegionFeatureStates();
        }}
      >
        {getButtonLabel(shift)}
        <span class="material-icons score-icon ml-1 text-sm" style:color={getWarningColor(shift)}>
          warning
        </span>
      </Button>
    {/each}
  </div>

  <Card class="shift-card-mobile w-full">
    <div class="map-header flex rounded-sm" style:border-color={warningColor}>
      <span>{shiftPeriod}</span>
      <span>{shiftOutageRange} {store.capitalizedOutagesLabel}</span>
    </div>
    <div>
      <div bind:this={mapContainer} class="map-container"></div>

      <Select.Root
        type="single"
        value={selectedChartConfig.displayName}
        onValueChange={(value) => {
          const config = chartConfigs.find((c) => c.displayName === value) as ChartConfig;
          selectedChartConfig = config;
        }}
      >
        <Select.Trigger class="mb-1">
          {selectedChartConfig.displayName}
        </Select.Trigger>
        <Select.Content>
          {#each chartConfigs as config}
            <Select.Item value={config.displayName}>
              {config.displayName}
            </Select.Item>
          {/each}
        </Select.Content>
      </Select.Root>

      <MapLegend {map} />

      <AgChart
        canExpand={true}
        className="w-full"
        data={chartData}
        options={{
          height: 360,
          axes: [
            {
              position: 'bottom',
              type: 'time',
              label: {
                formatter: (value: AgAxisLabelFormatterParams) => customHourFormatter(value)
              },
              title: { text: `Hour (${timezoneAbbr})` }
            },
            {
              position: 'left',
              type: 'number',
              title: { text: selectedChartConfig.yaxis.unit }
            }
          ],
          padding: { right: 50 },
          series: Object.entries(colorScale).map(([name, color]) => ({
            type: 'line',
            xKey: 'date',
            yKey: 'value',
            yName: name,
            stroke: color,
            marker: {
              fill: color,
              stroke: color
            },
            data: chartData.filter(({ group }) => group === name),
            tooltip: {
              renderer: (data: LineChartTooltipParams) =>
                tooltipRenderer(data, 2, selectedChartConfig.yaxis.unit)
            }
          }))
        }}
      />
    </div>
  </Card>
</div>

<style>
  .map-header {
    align-items: center;
    border: 1px solid;
    margin-bottom: 0.5rem;
    justify-content: space-between;
    padding: 0.5rem;
  }

  .map-container {
    height: 300px;
    margin-bottom: 8px;
    overflow: hidden;
    position: relative;
    width: 100%;
  }

  .score-icon {
    font-size: 16px;
  }

  :global(.shift-card-mobile .mapboxgl-ctrl.mapboxgl-ctrl-legend) {
    min-width: 46px !important;
  }

  :global(.shift-card-mobile .shift-button) {
    background: var(--card-color);
    border: 1px solid var(--color-border);
    margin-right: 8px;
    padding: 6px;
  }

  :global(.shift-card-mobile .shift-button:last-child) {
    margin-right: 0;
  }

  :global(.shift-card-mobile .shift-button.selected) {
    box-shadow: 0 0 0 2px var(--color-primary);
  }
</style>
