<script lang="ts">
  import { AgChart } from '$lib/components';
  import { Card } from '$lib/components/ui/card';
  import * as Select from '$lib/components/ui/select';
  import {
    customHourFormatter,
    generateChartColorsScale,
    tooltipRenderer
  } from '$lib/config/chart';
  import { getRegionalImpactFillLayer, MAP_BOUNDS } from '$lib/config/map';
  import { getOutageColor } from '$lib/config/utils';
  import { isDarkMode } from '$lib/stores/theme';
  import store from '$lib/stores/app.svelte';
  import { timezoneAbbr } from '$lib/stores/constants';
  import {
    MapStyle,
    type ChartConfig,
    type HourlyShift,
    type LineChartTooltipParams,
    type Shift
  } from '$lib/types';
  import type { AgAxisLabelFormatterParams } from 'ag-charts-enterprise';
  import { DateTime } from 'luxon';
  import mapboxgl from 'mapbox-gl';
  import { onDestroy, onMount } from 'svelte';
  import MapLegend from '../Map/MapLegend.svelte';
  import { fitMapToBounds } from '$lib/utils';

  const regionPopup = new mapboxgl.Popup({
    closeButton: false,
    closeOnClick: false
  });

  interface Props {
    chartConfigs: ChartConfig[];
    hourlyShift: HourlyShift | undefined;
    shift: Shift | undefined;
    yAxisChartDomainMap: Record<string, { min: number; max: number }>;
  }

  let { chartConfigs, hourlyShift, shift, yAxisChartDomainMap }: Props = $props();

  let hoveredRegion: any = null;
  let mapContainer: HTMLDivElement;

  let map = $state<mapboxgl.Map | undefined>();
  let selectedChartConfig = $state<ChartConfig>({
    attributeName: '',
    displayName: '',
    isDefaultDisplay: false,
    isDisplayed: false,
    type: 'weather',
    yaxis: { range: { max: 0, min: 0 }, unit: '' }
  });
  let shiftOutageRange = $state<string>('');
  let shiftPeriod = $state<string>('');
  let warningColor = $state<string>('var(--color-text)');

  let colorScale = $derived(
    generateChartColorsScale(
      store.regions.map((r) => r.displayName),
      $isDarkMode
    )
  );

  let chartData = $derived(
    selectedChartConfig.attributeName && hourlyShift
      ? hourlyShift.hourlyZones.map((z) => ({
          date: new Date(z.date),
          group: z.displayName,
          value:
            z.weatherAttributes[
              selectedChartConfig.attributeName as keyof typeof z.weatherAttributes
            ]
        }))
      : []
  );

  let chartOptions = $derived.by(() => {
    return selectedChartConfig.attributeName === ''
      ? {}
      : getChartOptions(selectedChartConfig.attributeName, colorScale);
  });

  const onWatchThemeMode = isDarkMode.subscribe((isDark) => {
    if (map) {
      map.remove();
      // Must pass in the latest value into the function because when this runs,
      // $isDarkMode value has not yet been updated.
      initializeMap(isDark);
    }
  });

  onMount(() => {
    initializeMap($isDarkMode);
    initializeChart();
  });

  onDestroy(() => {
    onWatchThemeMode();
  });

  function initializeMap(isDark: boolean) {
    if (!store.appConfig) return;
    const mapSettings = {
      center: store.appConfig.center.value,
      container: mapContainer as HTMLDivElement,
      style: `mapbox://styles/mapbox/${isDark ? MapStyle.dark : MapStyle.light}`,
      maxBounds: MAP_BOUNDS
    };
    const m = (map = new mapboxgl.Map(mapSettings));
    map.on('load', async () => {
      m.addSource('regions', {
        promoteId: 'name',
        type: 'vector',
        url: store.appConfig?.tilesetUrl.value
      });
      if (store.appConfig?.stateFillLayer) m.addLayer(store.appConfig?.stateFillLayer);
      m.addLayer(getRegionalImpactFillLayer());
      if (store.appConfig?.regionBorderLayer) m.addLayer(store.appConfig?.regionBorderLayer);
      setCardHeader();
      setRegionFeatureStates();
      fitMapToBounds(map, store.appConfig?.maxBounds?.value);
      m.on('mousemove', 'region-fill', onMousemoveRegion);
      m.on('mouseleave', 'region-fill', onMouseLeaveRegion);
      m.on('resize', () => fitMapToBounds(map, store.appConfig?.maxBounds?.value));
    });
  }

  function initializeChart() {
    const chartConfig = chartConfigs.find((c) => c.isDefaultDisplay);
    if (chartConfig) selectedChartConfig = chartConfig;
  }

  function setCardHeader() {
    if (!shift) return;
    const { end, start, outages } = shift;
    const startTime = DateTime.fromISO(start).toFormat('MMM d ha');
    const endTime = DateTime.fromISO(end).toFormat('ha');
    shiftPeriod = `${startTime} - ${endTime}`;
    shiftOutageRange = `${Math.round(outages.min)} - ${Math.round(outages.max)}`;
    const avgOutageCount = (outages.max + outages.min) / 2;
    warningColor = getOutageColor(avgOutageCount) as string;
  }

  function setRegionFeatureStates() {
    shift?.regions.forEach((region: Shift['regions'][number]) => {
      map!.setFeatureState(
        { id: region.name, source: 'regions', sourceLayer: 'regions' },
        { ...region }
      );
    });
  }

  function onMousemoveRegion(e: any) {
    // 1. Highlight hovered territory
    const { sourceLayer, state } = e.features[0];

    if (hoveredRegion?.id) {
      map!.setFeatureState(
        { id: hoveredRegion.id, source: 'regions', sourceLayer },
        { hover: false }
      );
    }

    hoveredRegion = e.features[0];
    if (hoveredRegion?.id) {
      map!.setFeatureState(
        { id: hoveredRegion.id, source: 'regions', sourceLayer },
        { hover: true }
      );
      // 2. Enable popup display
      map!.getCanvas().style.cursor = 'pointer';
      regionPopup
        .setLngLat(e.lngLat)
        .setHTML(
          `<div class="popup-region-info">
            <p class="popup-feature-name">${state.displayName}</p>
            <div class="info-row">
              <span class="label">Impact:</span>
              <span class="value">${state.impact}</span>
            </div>
            <div class="info-row">
              <span class="label">${store.capitalizedOutagesLabel}:</span>
              <span class="value">${state.impactValue}</span>
            </div>
          </div>`
        )
        .addTo(map!);
    }
  }

  function onMouseLeaveRegion() {
    // 1. Clean-up hover layer highlight
    if (hoveredRegion?.id) {
      const { id, sourceLayer } = hoveredRegion;
      map!.setFeatureState({ id, source: 'regions', sourceLayer }, { hover: false });
    }
    hoveredRegion = null;

    // 2. Clean-up hover popup display
    map!.getCanvas().style.cursor = '';
    regionPopup.remove();
  }

  function getChartOptions(key: string, data: Record<string, string>) {
    const { min, max } = yAxisChartDomainMap[key];
    return {
      height: 330,
      axes: [
        {
          position: 'bottom',
          type: 'time',
          label: {
            formatter: (value: AgAxisLabelFormatterParams) => customHourFormatter(value)
          },
          title: { text: `Hour (${timezoneAbbr})` }
        },
        {
          min, // Ordering matters (AG Charts internals) - min before max
          max, // Ordering matters (AG Charts internals) - max after min
          position: 'left',
          type: 'number',
          title: { text: selectedChartConfig.yaxis.unit }
        }
      ],
      series: Object.entries(data).map(([name, color]) => ({
        type: 'line',
        xKey: 'date',
        yKey: 'value',
        yName: name,
        stroke: color,
        marker: {
          fill: color,
          stroke: color
        },
        data: chartData.filter(({ group }) => group === name),
        tooltip: {
          renderer: (data: LineChartTooltipParams) =>
            tooltipRenderer(data, 2, selectedChartConfig.yaxis.unit)
        }
      }))
    };
  }
</script>

<Card class="shift-card">
  <div class="map-header flex rounded-sm" style:border-color={warningColor}>
    <span>{shiftPeriod}</span>
    <span>{shiftOutageRange} {store.capitalizedOutagesLabel}</span>
  </div>
  <div>
    <div bind:this={mapContainer} class="map-container"></div>

    <Select.Root
      type="single"
      value={selectedChartConfig.displayName}
      onValueChange={(value) => {
        const config = chartConfigs.find((c) => c.displayName === value) as ChartConfig;
        selectedChartConfig = config;
      }}
    >
      <Select.Trigger class="mb-1">
        {selectedChartConfig.displayName}
      </Select.Trigger>
      <Select.Content>
        {#each chartConfigs as config}
          <Select.Item value={config.displayName}>
            {config.displayName}
          </Select.Item>
        {/each}
      </Select.Content>
    </Select.Root>

    <MapLegend {map} />

    <AgChart canExpand={true} className="w-full" data={chartData} options={chartOptions} />
  </div>
</Card>

<style>
  .map-header {
    align-items: center;
    border: 1px solid;
    margin-bottom: 0.5rem;
    justify-content: space-between;
    padding: 0.5rem;
  }

  .map-container {
    height: 300px;
    margin-bottom: 8px;
    overflow: hidden;
    position: relative;
    width: 100%;
  }

  :global(.shift-card .mapboxgl-ctrl.mapboxgl-ctrl-legend) {
    min-width: 46px !important;
  }

  :global(.shift-card .select-chart-config) {
    margin-bottom: 0.5rem;
  }
</style>
