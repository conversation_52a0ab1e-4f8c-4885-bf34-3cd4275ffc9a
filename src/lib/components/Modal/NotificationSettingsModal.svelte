<script lang="ts">
  import {
    getNotificationSettings,
    getUserNotificationSettings,
    saveNewNotificationSettings
  } from '$lib/api';
  import { Checkbox, Loader } from '$lib/components';
  import * as Alert from '$lib/components/ui/alert';
  import { Button } from '$lib/components/ui/button';
  import * as Dialog from '$lib/components/ui/dialog';
  import * as Select from '$lib/components/ui/select';
  import store from '$lib/stores/app.svelte';
  import type { Subscription } from '$lib/types';
  import { getUserData } from '$lib/authentication';

  interface UserData {
    email: string | null;
    userId: string | null;
  }

  interface Props {
    onClose?: () => void;
  }

  let { onClose = () => {} }: Props = $props();

  let isLoading = $state<boolean>(true);
  let isOpen = $state<boolean>(false);
  let showError = $state<boolean>(false);
  let userData: UserData = getUserData();

  async function initialize() {
    isLoading = true;
    let configResponse = null;
    let userSettingsResponse = null;

    if (!userData) {
      console.error('userData is undefined');
      showError = true;
      isLoading = false;
      return;
    }

    try {
      const tempConfigResponse = await getNotificationSettings();
      if (tempConfigResponse) {
        configResponse = tempConfigResponse;
      } else {
        console.error('configResponse is null or undefined');
        showError = true;
      }
    } catch (error) {
      console.error('Error fetching notification settings:', error);
      showError = true;
    }

    try {
      const tempUserSettingsResponse = await getUserNotificationSettings(userData.email as string);
      if (tempUserSettingsResponse) {
        userSettingsResponse = tempUserSettingsResponse;
      } else {
        console.error('userSettingsResponse is null or undefined');
        showError = true;
      }
    } catch (error) {
      console.error('Error fetching user notification settings:', error);
      showError = true;
    }

    if (configResponse && userSettingsResponse) {
      const userSettingsMap = new Map(
        userSettingsResponse.map((setting) => [setting.notification.id, setting])
      );

      const combinedConfigs = configResponse
        .sort((a, b) => a.displayOrder - b.displayOrder)
        .map((config) => {
          const userSetting = userSettingsMap.get(config.id);
          return {
            ...config,
            isSelected: !!userSetting,
            selectedLevel: userSetting?.metadata?.level || 'Level 0',
            hasOptions: !!config.options
          };
        });

      store.notificationConfigs = combinedConfigs;
      showError = false;
    } else {
      showError = true;
    }

    isLoading = false;
  }

  async function saveNewSettings(
    notificationId: number,
    isSelected: boolean,
    selectedLevel?: string,
    hasOptions: boolean = false
  ) {
    try {
      const body: Subscription = {
        email: userData!.email as string,
        notificationId,
        options: isSelected && selectedLevel && hasOptions ? { level: selectedLevel } : {},
        subscribed: isSelected
      };

      await saveNewNotificationSettings(body);
    } catch (error) {
      showError = true;
      console.error('Error saving settings:', error);
    }
  }
</script>

<Dialog.Root
  bind:open={isOpen}
  onOpenChange={(isOpening) => {
    if (isOpening) {
      initialize();
    } else {
      onClose();
    }
  }}
>
  <Dialog.Trigger class="w-full">
    <Button class="!justify-start w-full" variant="ghost">
      <span class="material-icons">tune</span>
      Notification Settings
    </Button>
  </Dialog.Trigger>

  <Dialog.Content class="max-w-[500px]">
    <Dialog.Header>
      <Dialog.Title>Notification Settings</Dialog.Title>
    </Dialog.Header>

    <div class="flex flex-col">
      {#if showError}
        <Alert.Root variant="error">
          <Alert.Title>Error:</Alert.Title>
          <Alert.Description>An error occurred while fetching settings.</Alert.Description>
        </Alert.Root>
      {/if}

      {#if isLoading}
        <Loader />
      {:else}
        <span class="mb-2 text-base">Email Notifications</span>
        {#each store.notificationConfigs as { id, displayName, isSelected, options, selectedLevel }, i}
          <div class="mb-2">
            <Checkbox
              bind:checked={store.notificationConfigs[i].isSelected}
              label={displayName}
              onCheck={(isChecked) => {
                const hasLevels = !!options && !!options.levels;
                saveNewSettings(id, isChecked, selectedLevel, hasLevels);
              }}
            />

            {#if options?.levels}
              <Select.Root
                disabled={!isSelected}
                type="single"
                bind:value={store.notificationConfigs[i].selectedLevel}
                onValueChange={() => {
                  const hasLevels = !!options && !!options.levels;
                  saveNewSettings(id, isSelected, selectedLevel, hasLevels);
                }}
              >
                <Select.Trigger class="w-[120px] ml-[27px] mt-2">
                  {selectedLevel}
                </Select.Trigger>
                <Select.Content>
                  {#each options.levels as level}
                    <Select.Item value={level}>{level}</Select.Item>
                  {/each}
                </Select.Content>
              </Select.Root>
            {/if}
          </div>
        {/each}
      {/if}
    </div>

    <Dialog.Footer>
      <Button
        onclick={() => {
          isOpen = false;
          onClose();
        }}
      >
        Done
      </Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
