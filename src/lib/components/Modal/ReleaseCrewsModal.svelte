<script lang="ts">
  import { setCrewReleaseTime } from '$lib/api';
  import { DatePicker, Input, TimePicker } from '$lib/components';
  import * as Alert from '$lib/components/ui/alert';
  import { Button } from '$lib/components/ui/button';
  import * as Dialog from '$lib/components/ui/dialog';
  import { ErrorStatusCodes } from '$lib/config/main';
  import type { ActiveCrew } from '$lib/types';
  import { formatDuration } from '$lib/utils';
  import { DateTime } from 'luxon';

  interface Props {
    crew: ActiveCrew;
    stormStartDate: Date;
    stormEndDate: Date;
    onClose: () => void;
    onSave: () => void;
  }

  let { crew, stormStartDate, stormEndDate, onClose, onSave }: Props = $props();

  let errorMessage = $state<string>('');
  let isSaving = $state<boolean>(false);

  let releaseDate = $state<DateTime>(DateTime.fromJSDate(stormEndDate));
  let releaseTime = $state<string>('');

  let remainingDuration = $state(0);
  let releaseDateTime = $state<DateTime | undefined>();

  let isTimeValid = $derived(validateTime(releaseTime, releaseDateTime));
  let isDateValid = $derived(validateDate(releaseDate));
  let duration = $derived(formatDuration(remainingDuration));

  function onDateTimeChange() {
    if (releaseTime) {
      releaseDateTime = DateTime.fromFormat(
        `${releaseDate.toFormat('MM/dd/yyyy')} ${releaseTime}`,
        'MM/dd/yyyy HH:mm'
      );
      remainingDuration = releaseDateTime.diffNow().milliseconds;
    }
  }

  async function moveResources() {
    isSaving = true;

    try {
      await setCrewReleaseTime(crew.id, releaseDateTime!.toUTC().toISO()!);
      onSave();
      onClose();
    } catch (e) {
      const err = e as { response?: { data?: { message?: string }; status: string } };
      errorMessage =
        err.response?.status === ErrorStatusCodes.CONFLICT.toString()
          ? 'A model is currently running. Please wait until it is finished.'
          : (err?.response?.data?.message ?? 'An unexpected error occurred. Please try again.');
    }

    isSaving = false;
  }

  function validateDate(date: DateTime): boolean {
    return (
      date &&
      date.isValid &&
      date >= DateTime.fromJSDate(stormStartDate).startOf('day') &&
      date <= DateTime.fromJSDate(stormEndDate).endOf('day').plus({ days: 3 })
    );
  }

  function validateTime(
    time: string,
    releaseDate: DateTime | undefined
  ): { valid: boolean; message: string } {
    if (!time) {
      return { valid: true, message: '' };
    }

    const arrivalTime = DateTime.fromISO(crew.arrivalTime);

    if (releaseDate && releaseDate <= arrivalTime) {
      return { valid: false, message: "Release time must be after the crew's arrival time" };
    }

    return { valid: true, message: '' };
  }
</script>

<Dialog.Root
  open={true}
  onOpenChange={(isOpening) => {
    if (!isOpening) onClose();
  }}
>
  <Dialog.Content class="add-resources max-w-[800px]">
    <Dialog.Title>Release Crew</Dialog.Title>

    <div class="flex flex-col gap-lg">
      {#if errorMessage}
        <Alert.Root variant="error">
          <Alert.Title>Error:</Alert.Title>
          <Alert.Description>{errorMessage}</Alert.Description>
        </Alert.Root>
      {/if}

      <div class="flex gap-sm flex-wrap">
        <div class="flex-1">
          <Input
            label="Arrival Time"
            disabled
            value={DateTime.fromISO(crew.arrivalTime).toFormat('MM/dd/yyyy h:mm a')}
          />
        </div>

        <div class="flex-1">
          <DatePicker
            isInvalid={!isDateValid}
            invalidText="Date must between storm start and end dates"
            label="Release Date"
            maxDate={DateTime.fromJSDate(stormEndDate).plus({ days: 3 }).toFormat('MM/dd/yyyy')}
            minDate={DateTime.fromJSDate(stormStartDate).toFormat('MM/dd/yyyy')}
            value={releaseDate?.isValid ? releaseDate.toFormat('MM/dd/yyyy') : ''}
            onChange={(value) => {
              releaseDate = DateTime.fromFormat(value, 'MM/dd/yyyy');
              onDateTimeChange();
            }}
          />
        </div>

        <div class="flex-1">
          <TimePicker
            invalidText={isTimeValid.message}
            isInvalid={!isTimeValid.valid}
            label="Release Time"
            value={releaseTime}
            onChange={(value) => {
              releaseTime = value;
              onDateTimeChange();
            }}
          />
        </div>
      </div>

      {#if releaseTime && isTimeValid.valid && isDateValid}
        <div class="text-center h-full flex justify-center items-center text-accentText italic">
          <p>
            Crews will be released from {crew.toTerritoryId}
            {duration ? `in ${duration}` : 'immediately'}.
          </p>
        </div>
      {/if}
    </div>

    <Dialog.Footer>
      <Button variant="tertiary" onclick={onClose}>Cancel</Button>
      <Button
        disabled={!releaseTime || !isTimeValid.valid || !isDateValid || isSaving}
        onclick={moveResources}
      >
        Save
      </Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
