<script lang="ts">
  import { AutoComplete, DatePicker, Input, TimePicker } from '$lib/components';
  import * as Accordion from '$lib/components/ui/accordion';
  import * as Alert from '$lib/components/ui/alert';
  import { Button } from '$lib/components/ui/button';
  import * as Dialog from '$lib/components/ui/dialog';
  import * as Select from '$lib/components/ui/select';
  import type { CrewEntry, Territory } from '$lib/types';
  import { toCapitalizedWords } from '$lib/utils';
  import { object, string, date, array, number } from 'yup';
  import { createBatchCrews, getInitialTerritories, getResourceRoles } from '$lib/api';
  import { DateTime } from 'luxon';

  const DEFAULT_CREW_RESOURCE = { role: '', quantity: 0 };
  const DEFAULT_TOUCHED = {
    initialTerritory: false,
    assignedTerritory: false,
    resourceType: false,
    arrivalDate: false,
    roleAllocations: [{ role: false, quantity: false }]
  };

  interface Props {
    disabled: boolean;
    resourceFields: string[];
    stormId: string;
    stormEndDate: Date;
    stormStartDate: Date;
    territories?: Territory[];
    onSave: () => void;
  }

  let {
    disabled = false,
    resourceFields = [],
    stormId,
    stormEndDate,
    stormStartDate,
    territories = [],
    onSave
  }: Props = $props();

  let crews = $state<CrewEntry[]>([]);
  let errorMessage = $state<string>('');
  let fromLocations = $state<string[]>([]);
  let isFormSubmitted = $state<boolean>(false);
  let isOpen = $state<boolean>(false);
  let isSaving = $state<boolean>(false);
  let ref = $state<HTMLButtonElement | null>(null);
  let resourceRoles = $state<string[]>([]);
  let touched = $state([structuredClone(DEFAULT_TOUCHED)]);

  $effect(() => {
    if (ref) ref.focus();
  });

  function cloneDefaultCrew(): CrewEntry {
    return {
      arrivalDate: DateTime.now(),
      arrivalTime: DateTime.now().toFormat('HH:mm'),
      assignedTerritory: territories?.[0]?.territoryId,
      initialTerritory: '',
      resourceType: resourceFields?.[0],
      roleAllocations: [{ ...DEFAULT_CREW_RESOURCE }]
    };
  }

  async function fetchResourceRoles() {
    try {
      resourceRoles = await getResourceRoles();
    } catch {
      console.error('Failed to fetch resource roles');
      resourceRoles = [];
    }
  }

  async function fetchFromLocations() {
    try {
      fromLocations = await getInitialTerritories();
    } catch {
      console.error('Failed to fetch initial territories');
      fromLocations = [];
    }
  }

  const schema = array().of(
    object({
      resourceType: string().required('Resource type is required'),
      initialTerritory: string().required('From location is required'),
      assignedTerritory: string().required('To location is required'),
      arrivalDate: date().required('Arrival date is required'),
      roleAllocations: array()
        .of(
          object({
            role: string().required('Role is required'),
            quantity: number()
              .min(1, 'Resource quantity must be at least 1')
              .required('Resource quantity is required')
          })
        )
        .min(1, 'At least one role allocation is required')
        .test('unique-roles', 'Each role must be unique', (roleAllocations) => {
          if (!roleAllocations) return false;
          const roles = roleAllocations.map((r) => r.role);
          return new Set(roles).size === roles.length;
        })
    })
  );

  const validateForm = async () => {
    try {
      await schema.validate(crews, { abortEarly: false });
      return true;
    } catch {
      return false;
    }
  };

  async function onSubmit(e: Event) {
    e.preventDefault();
    isFormSubmitted = true;

    const isValid = await validateForm();

    if (!isValid) {
      return;
    }

    try {
      isSaving = true;
      const formattedData = crews.map((crew) => {
        const arrivalTime = DateTime.fromFormat(
          `${crew.arrivalDate.toFormat('MM/dd/yyyy')} ${crew.arrivalTime}`,
          'MM/dd/yyyy HH:mm'
        );

        return {
          initialTerritory: crew.initialTerritory,
          assignedTerritoryId: crew.assignedTerritory,
          roleAllocations: crew.roleAllocations,
          resourceType: crew.resourceType,
          arrivalTime: arrivalTime.toISO()!
        };
      });

      await createBatchCrews(stormId, formattedData);
      onSave();
      isOpen = false;
    } catch (e) {
      errorMessage =
        ((e as { response?: { data?: { message?: string } } })?.response?.data
          ?.message as string) ?? 'An unexpected error occurred. Please try again.';
    } finally {
      isSaving = false;
    }
  }

  function addCrewResource(crewIndex: number) {
    crews[crewIndex].roleAllocations = [
      ...crews[crewIndex].roleAllocations,
      { ...DEFAULT_CREW_RESOURCE }
    ];
    touched[crewIndex].roleAllocations = [
      ...touched[crewIndex].roleAllocations,
      { role: false, quantity: false }
    ];

    setTimeout(() => {
      const input = document.querySelector<HTMLInputElement>(
        `input[data-crew-index="${crewIndex}"][data-resource-index="${crews[crewIndex].roleAllocations.length - 1}"]`
      );
      input?.focus();
    }, 0);
  }

  function removeCrewResource(crewIndex: number, resourceIndex: number) {
    crews[crewIndex].roleAllocations = crews[crewIndex].roleAllocations.filter(
      (_, i) => i !== resourceIndex
    );
    touched[crewIndex].roleAllocations = touched[crewIndex].roleAllocations.filter(
      (_, i) => i !== resourceIndex
    );

    setTimeout(() => {
      const input = document.querySelector<HTMLInputElement>(
        `input[data-crew-index="${crewIndex}"][data-resource-index="${resourceIndex - 1}"]`
      );
      input?.focus();
    }, 0);
  }

  function removeCrew(index: number) {
    crews = crews.filter((_, i) => i !== index);
    touched = touched.filter((_, i) => i !== index);

    setTimeout(() => {
      const input = document.querySelector<HTMLInputElement>(`input[data-crew-index="${index}"]`);
      input?.focus();
    }, 0);
  }

  function addCrew() {
    const lastResources = crews[crews.length - 1]?.roleAllocations ?? [
      { ...DEFAULT_CREW_RESOURCE }
    ];
    const newResources = lastResources.map((resource) => ({
      role: resource.role,
      quantity: 0
    }));

    crews = [...crews, { ...crews[crews.length - 1], roleAllocations: newResources }];
    touched = [
      ...touched,
      {
        initialTerritory: false,
        assignedTerritory: false,
        resourceType: false,
        arrivalDate: false,
        roleAllocations: newResources.map(() => ({ role: false, quantity: false }))
      }
    ];

    setTimeout(() => {
      const input = document.querySelector<HTMLInputElement>(
        `input[data-crew-index="${crews.length - 1}"][data-resource-index="0"]`
      );
      input?.focus();
    }, 0);
  }

  function handleBlur(crewIndex: number, resourceIndex: number, field: 'role' | 'quantity') {
    touched[crewIndex].roleAllocations[resourceIndex][field] = true;
  }

  function validateDate(date: DateTime): boolean {
    return (
      date &&
      date.isValid &&
      date >= DateTime.fromJSDate(stormStartDate).startOf('day') &&
      date <= DateTime.fromJSDate(stormEndDate).endOf('day').plus({ days: 3 })
    );
  }

  function validateRole(role: string, crewIndex: number, resourceIndex: number) {
    const isTouched = touched[crewIndex]?.roleAllocations?.[resourceIndex]?.role;

    if (!isFormSubmitted && !isTouched) {
      return { isInvalid: false, invalidText: '' };
    }

    if (!role) {
      return { isInvalid: true, invalidText: 'Role is required' };
    }

    const isDuplicate = crews[crewIndex].roleAllocations.some(
      (r, i) => r.role === role && i !== resourceIndex
    );

    return isDuplicate
      ? { isInvalid: true, invalidText: 'Each role must be unique' }
      : { isInvalid: false, invalidText: '' };
  }
</script>

<Dialog.Root
  bind:open={isOpen}
  onOpenChange={async (isOpening) => {
    if (isOpening) {
      crews = [];
      touched = [structuredClone(DEFAULT_TOUCHED)];
      isFormSubmitted = false;
      errorMessage = '';
      await Promise.allSettled([fetchResourceRoles(), fetchFromLocations()]);
      crews = [cloneDefaultCrew()];
    }
  }}
>
  <Dialog.Trigger>
    <Button {disabled} size="sm">
      <span class="material-icons">add</span>
      Add Crews
    </Button>
  </Dialog.Trigger>

  <Dialog.Content class="add-resources max-w-[1400px] min-h-[400px]">
    <Dialog.Title>Add Crews</Dialog.Title>

    <div class="flex flex-col gap-lg">
      {#if errorMessage}
        <Alert.Root variant="error">
          <Alert.Title>Error:</Alert.Title>
          <Alert.Description>{errorMessage}</Alert.Description>
        </Alert.Root>
      {/if}

      <div class="flex flex-col">
        <Accordion.Root type="multiple" value={crews.map((_, i) => String(i))}>
          {#each crews as crew, crewIndex}
            <Accordion.Item class="mb-3" value={String(crewIndex)}>
              <Accordion.Trigger>
                <div class="flex gap-sm justify-between items-center w-full">
                  <span class="text-lg">Crew {crewIndex + 1}</span>
                  <span class="text-accentText mr-md">
                    {crew.roleAllocations.length}
                    {crew.roleAllocations.length === 1 ? 'Resource Type' : 'Resource Types'} •
                    {crew.roleAllocations.reduce((acc, resource) => acc + resource.quantity, 0)} FTE
                  </span>
                </div>
              </Accordion.Trigger>

              <Accordion.Content>
                <div class="flex gap-sm flex-wrap">
                  <div class="flex-1 min-w-[225px]">
                    <div class="flex flex-col">
                      <Select.Label class="mb-2 text-xs">Resource Type</Select.Label>
                      <Select.Root
                        type="single"
                        value={crew.resourceType}
                        onValueChange={(value) => (crews[crewIndex].resourceType = value)}
                      >
                        <Select.Trigger
                          class="min-w-[225px]"
                          bind:ref
                          onblur={() => (touched[crewIndex].resourceType = true)}
                        >
                          {toCapitalizedWords(crew.resourceType)}
                        </Select.Trigger>
                        <Select.Content>
                          {#each resourceFields as type}
                            <Select.Item value={type}>
                              {toCapitalizedWords(type)}
                            </Select.Item>
                          {/each}
                        </Select.Content>
                      </Select.Root>
                    </div>
                  </div>

                  <div class="flex-1 min-w-[225px]">
                    <AutoComplete
                      invalidText="From location is required"
                      isInvalid={(isFormSubmitted || touched[crewIndex].initialTerritory) &&
                        !crew.initialTerritory}
                      items={fromLocations}
                      label="From"
                      placeholder="Enter from location"
                      bind:value={crew.initialTerritory}
                      onblur={() => (touched[crewIndex].initialTerritory = true)}
                    />
                  </div>

                  <div class="flex-1 min-w-[225px]">
                    <div class="flex flex-col">
                      <Select.Label class="mb-2 text-xs">To</Select.Label>
                      <Select.Root
                        type="single"
                        value={crew.assignedTerritory}
                        onValueChange={(value) => (crew.assignedTerritory = value)}
                      >
                        <Select.Trigger
                          onblur={() => (touched[crewIndex].assignedTerritory = true)}
                        >
                          {territories.find((t) => t.territoryId === crew.assignedTerritory)
                            ?.territoryName}
                        </Select.Trigger>
                        <Select.Content>
                          {#each territories as territory}
                            <Select.Item value={territory.territoryId}>
                              {territory.territoryName}
                            </Select.Item>
                          {/each}
                        </Select.Content>
                      </Select.Root>
                    </div>
                  </div>

                  <div class="flex-1 min-w-[225px]">
                    <DatePicker
                      isInvalid={!validateDate(crew.arrivalDate)}
                      invalidText="Date must be after the storm start date"
                      label="Arrival Date"
                      maxDate={DateTime.fromJSDate(stormEndDate)
                        .plus({ days: 3 })
                        .toFormat('MM/dd/yyyy')}
                      minDate={DateTime.fromJSDate(stormStartDate).toFormat('MM/dd/yyyy')}
                      value={crew.arrivalDate.isValid
                        ? crew.arrivalDate.toFormat('MM/dd/yyyy')
                        : ''}
                      onChange={(value) => {
                        crew.arrivalDate = DateTime.fromFormat(value, 'MM/dd/yyyy');
                      }}
                    />
                  </div>

                  <div class="flex-1">
                    <TimePicker
                      invalidText="Invalid time"
                      isInvalid={crew.arrivalTime === ''}
                      label="Arrival Time"
                      value={crew.arrivalTime}
                      onChange={(value) => (crew.arrivalTime = value)}
                    />
                  </div>
                </div>

                <div class="mt-md">
                  <p class="text-sm">Resources</p>
                  {#each crew.roleAllocations as resource, resourceIndex}
                    <div class="flex gap-sm items-start mt-sm flex-wrap">
                      {#if crew.roleAllocations.length > 1}
                        <Button
                          class="{resourceIndex === 0 ? 'mt-lg' : ''} !text-danger"
                          size="icon"
                          variant="outline"
                          onclick={() => removeCrewResource(crewIndex, resourceIndex)}
                        >
                          <span class="material-icons">clear</span>
                        </Button>
                      {/if}

                      <AutoComplete
                        className="w-[225px]"
                        items={resourceRoles}
                        label={resourceIndex > 0 ? '' : 'Role'}
                        placeholder="Enter role"
                        bind:value={resource.role}
                        onblur={() => handleBlur(crewIndex, resourceIndex, 'role')}
                        {...validateRole(resource.role, crewIndex, resourceIndex)}
                      />

                      <Input
                        class="w-[135px]"
                        invalidText="Quantity is required"
                        isInvalid={(isFormSubmitted ||
                          touched[crewIndex].roleAllocations?.[resourceIndex]?.quantity) &&
                          (resource.quantity === null || resource.quantity <= 0)}
                        label={resourceIndex > 0 ? '' : 'Quanity'}
                        type="number"
                        min={0}
                        required
                        bind:value={resource.quantity}
                        onblur={() => handleBlur(crewIndex, resourceIndex, 'quantity')}
                      />

                      {#if resourceIndex === crew.roleAllocations.length - 1}
                        <Button
                          class={resourceIndex === 0 ? 'mt-lg' : ''}
                          size="icon"
                          variant="outline"
                          onclick={() => addCrewResource(crewIndex)}
                          onkeydown={(event) => {
                            if (event.key === 'Enter') {
                              event.stopPropagation();
                              addCrewResource(crewIndex);
                            }
                          }}
                        >
                          <span class="material-icons">add</span>
                        </Button>
                      {/if}
                      {#if crews.length > 1 && resourceIndex === crew.roleAllocations.length - 1}
                        <Button
                          class="{resourceIndex === 0 ? 'mt-lg' : ''} ml-auto !bg-danger"
                          size="sm"
                          onclick={() => removeCrew(crewIndex)}
                        >
                          <span class="material-icons">clear</span>
                          Remove Crew
                        </Button>
                      {/if}
                    </div>
                  {/each}
                </div>
              </Accordion.Content>
            </Accordion.Item>
          {/each}
        </Accordion.Root>

        <Button variant="outline" onclick={addCrew}>
          <span class="material-icons">add</span>
          Add Crew
        </Button>
      </div>
    </div>

    <Dialog.Footer>
      <Button variant="tertiary" onclick={() => (isOpen = false)}>Cancel</Button>
      <Button disabled={isSaving} variant="default" onclick={onSubmit}>Save</Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
