<script lang="ts">
  import { base } from '$app/paths';
  import { Button } from '$lib/components/ui/button';
  import * as Dialog from '$lib/components/ui/dialog';
  import { isDarkMode } from '$lib/stores/theme';
  import type { InfoBoxConfig } from '$lib/types';

  interface Props {
    buttonClass?: string;
    config: InfoBoxConfig;
  }

  let { buttonClass = '', config }: Props = $props();
</script>

<Dialog.Root>
  <Dialog.Trigger>
    <Button class={buttonClass} size="icon" variant="ghost" tooltip={config.label}>
      <span class="material-icons">info</span>
    </Button>
  </Dialog.Trigger>
  <Dialog.Content class="max-w-[1400px]">
    <img
      class="object-contain"
      src="{base}/images/{$isDarkMode ? config.image.dark : config.image.light}"
      alt={config.image.label}
      width="100%"
      height="100%"
    />
  </Dialog.Content>
</Dialog.Root>
