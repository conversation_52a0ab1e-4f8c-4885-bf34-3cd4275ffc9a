<script lang="ts">
  import { getActiveCrews, postResourceOperation } from '$lib/api';
  import { type ActiveCrew, type ResourceData, type Territory } from '$lib/types';
  import { DatePicker, Input, Loader, TimePicker } from '$lib/components';
  import * as Alert from '$lib/components/ui/alert';
  import { Button } from '$lib/components/ui/button';
  import * as Dialog from '$lib/components/ui/dialog';
  import * as Select from '$lib/components/ui/select';
  import { Skeleton } from '$lib/components/ui/skeleton';
  import { ErrorStatusCodes } from '$lib/config/main';
  import { toCapitalizedWords } from '$lib/utils';
  import { object, date } from 'yup';
  import { DateTime } from 'luxon';

  const SKELETON_COUNT = 5;

  interface Props {
    currentResourcesRowData: ResourceData[];
    disabled: boolean;
    resourceFields: string[];
    stormId: string;
    stormStartDate: Date;
    territories: Territory[];
    onSave: () => void;
  }

  let {
    currentResourcesRowData = [],
    disabled = false,
    resourceFields = [],
    stormId,
    stormStartDate,
    territories = [],
    onSave
  }: Props = $props();

  const territoryMap = territories.reduce(
    (acc, territory) => {
      acc[territory.territoryId as string] = territory.territoryName;
      return acc;
    },
    {} as Record<string, string>
  );

  let isOpen = $state<boolean>(false);
  let isSaving = $state<boolean>(false);
  let isLoadingCrews = $state<boolean>(true);
  let errorMessage = $state<string>('');

  let crews = $state<ActiveCrew[]>([]);
  let arrivalDate = $state<DateTime>(DateTime.now());
  let arrivalTime = $state<string>(DateTime.now().toFormat('HH:mm'));

  let selectedCrew = $derived(crews[0]);

  let selectedTerritory = $derived(
    territories.find((t) => t.territoryId === selectedCrew?.assignedTerritoryId)
  );

  let selectedTerritoryTo = $derived(
    territories.find((t) => t.territoryName !== selectedTerritory?.territoryName)
  );

  let fromTerritoryData = $derived(
    currentResourcesRowData.find((row) => row.territoryName === selectedTerritory?.territoryName)
  );

  let toTerritoryData = $derived(
    currentResourcesRowData.find((row) => row.territoryName === selectedTerritoryTo?.territoryName)
  );

  const schema = object({
    arrivalDate: date().required('Arrival date is required')
  });

  const validateForm = async () => {
    try {
      await schema.validate({ arrivalDate, arrivalTime }, { abortEarly: false });
      return true;
    } catch {
      return false;
    }
  };

  async function onSubmit(e: Event) {
    e.preventDefault();
    const isValid = await validateForm();
    if (!isValid) return;

    isSaving = true;
    const operationType = 'MOVE';

    const executionTime = DateTime.fromFormat(
      `${arrivalDate.toFormat('MM/dd/yyyy')} ${arrivalTime}`,
      'MM/dd/yyyy HH:mm'
    );

    const body = {
      linkedPendingOperationId: null,
      stormId: stormId,
      type: operationType,
      executionTime: executionTime.toISO(),
      crewId: selectedCrew.id,
      specificMetadata: {
        fromTerritoryId: selectedTerritory?.territoryId,
        toTerritoryId: selectedTerritoryTo?.territoryId
      },
      resources: {
        [selectedCrew.resources.type]: selectedCrew.totalResources
      }
    };

    try {
      await postResourceOperation(body);
      onSave();
      isOpen = false;
    } catch (e) {
      const err = e as { response?: { data?: { message?: string }; status: string } };
      errorMessage =
        err.response?.status === ErrorStatusCodes.CONFLICT.toString()
          ? 'A model is currently running. Please wait until it is finished.'
          : (err?.response?.data?.message ?? 'An error occurred while moving crews.');
    } finally {
      isSaving = false;
    }
  }

  async function fetchActiveCrews() {
    try {
      isLoadingCrews = true;
      crews = await getActiveCrews(stormId, false);
    } catch (e) {
      errorMessage = 'Could not fetch active crews.';
    } finally {
      isLoadingCrews = false;
    }
  }

  function validateDate(date: DateTime): boolean {
    return date.isValid && date >= DateTime.fromJSDate(stormStartDate).startOf('day');
  }
</script>

<Dialog.Root
  bind:open={isOpen}
  onOpenChange={async (isOpening) => {
    if (isOpening) {
      arrivalDate = DateTime.now();
      arrivalTime = DateTime.now().toFormat('HH:mm');
      errorMessage = '';
      await fetchActiveCrews();
      selectedCrew = crews[0];
    }
  }}
>
  <Dialog.Trigger>
    <Button {disabled} size="sm">
      <span class="material-icons">swap_calls</span>
      Move Crews
    </Button>
  </Dialog.Trigger>

  <Dialog.Content class="add-resources max-w-[1400px]">
    <Dialog.Title>Move Crews</Dialog.Title>

    <div class="flex flex-col h-full">
      {#if errorMessage}
        <Alert.Root variant="error">
          <Alert.Title>Error:</Alert.Title>
          <Alert.Description>{errorMessage}</Alert.Description>
        </Alert.Root>
      {/if}

      {#if isLoadingCrews}
        <Loader />
      {:else if crews.length === 0}
        <div class="text-center h-full flex justify-center items-center text-accentText italic">
          <p>No crews available for move.</p>
        </div>
      {:else}
        <div class="flex gap-md flex-wrap">
          <div class="flex-[2] min-w-[325px]">
            <div class="flex flex-col">
              <Select.Label class="mb-2 text-xs">Crew</Select.Label>
              <Select.Root
                type="single"
                value={selectedCrew?.id}
                onValueChange={(value) => {
                  selectedCrew = crews.find((crew) => crew.id === value) as ActiveCrew;
                }}
              >
                <Select.Trigger>
                  {territoryMap[selectedCrew.currentTerritoryId] ?? 'Out of System'} - {selectedCrew?.id}
                </Select.Trigger>
                <Select.Content>
                  {#each crews as crew}
                    <Select.Item value={crew.id}>
                      {territoryMap[crew.currentTerritoryId] ?? 'Out of System'} - {crew.id}
                    </Select.Item>
                  {/each}
                </Select.Content>
              </Select.Root>
            </div>

            {#if selectedCrew}
              <ul class="flex flex-col gap-sm mt-md text-accentText list-disc ml-md">
                <li>Current Location: {territoryMap[selectedCrew.currentTerritoryId]}</li>
                <li>Resource Type: {toCapitalizedWords(selectedCrew.resources.type)}</li>
                <li>{selectedCrew.totalResources} Full-Time Equivalent</li>
                <li>
                  {selectedCrew?.resources?.roleAllocations.length} Line
                  {selectedCrew?.resources?.roleAllocations.length === 1 ? ' Item' : ' Items'}:
                </li>
                <ul class="flex flex-col gap-sm ml-md">
                  {#each selectedCrew?.resources?.roleAllocations ?? [] as { role, quantity }}
                    <li class="flex gap-xs items-center">
                      <span>{role} - </span>
                      <span>{quantity}</span>
                    </li>
                  {/each}
                </ul>
              </ul>
            {:else}
              <div class="flex flex-col gap-xs">
                <!--eslint-disable @typescript-eslint/no-unused-vars -->
                {#each Array(SKELETON_COUNT) as _}
                  <Skeleton />
                {/each}
              </div>
            {/if}
          </div>
          <div class="flex-1 min-w-[225px]">
            <Input
              label="From"
              disabled
              value={selectedTerritory?.territoryName ?? 'Out of System'}
            />

            {#if isLoadingCrews}
              <div class="flex flex-col gap-xs">
                <!--eslint-disable @typescript-eslint/no-unused-vars -->
                {#each Array(SKELETON_COUNT) as _}
                  <Skeleton />
                {/each}
              </div>
            {:else if selectedCrew && selectedTerritory}
              <div class="mt-md flex flex-col gap-sm text-accentText">
                <span class="line-through">
                  Current Count: {fromTerritoryData?.total}
                </span>
                <span
                  >New Count: {(fromTerritoryData?.total ?? 0) -
                    (selectedCrew?.totalResources ?? 0)}
                </span>
                <span>Resource Breakdown:</span>
                <ul class="flex flex-col gap-sm ml-md">
                  {#each resourceFields as field}
                    {#if field === selectedCrew.resources.type}
                      <li>
                        <span>{toCapitalizedWords(field)}:</span>
                        <span>
                          {fromTerritoryData?.[field]} → {Number(fromTerritoryData?.[field] ?? 0) -
                            selectedCrew.totalResources}
                        </span>
                      </li>
                    {:else}
                      <li>
                        <span>{toCapitalizedWords(field)}:</span>
                        <span>
                          {fromTerritoryData?.[field]}
                        </span>
                      </li>
                    {/if}
                  {/each}
                </ul>
              </div>
            {/if}
          </div>

          <div class="flex-1 min-w-[225px]">
            <div class="flex flex-col">
              <Select.Label class="mb-2 text-xs">To</Select.Label>
              <Select.Root
                type="single"
                value={selectedTerritoryTo?.territoryId}
                onValueChange={(value) => {
                  selectedTerritoryTo = territories.find((i) => i.territoryId === value);
                }}
              >
                <Select.Trigger>
                  {selectedTerritoryTo?.territoryName}
                </Select.Trigger>
                <Select.Content>
                  {#each territories.filter((i) => i.territoryId !== selectedCrew?.assignedTerritoryId) as territory}
                    <Select.Item value={territory.territoryId}>
                      {territory.territoryName}
                    </Select.Item>
                  {/each}
                </Select.Content>
              </Select.Root>
            </div>

            {#if selectedCrew}
              <div class="mt-md flex flex-col gap-sm text-accentText">
                <span class="line-through">
                  Current Count: {toTerritoryData?.total}
                </span>
                <span
                  >New Count: {(toTerritoryData?.total ?? 0) + (selectedCrew?.totalResources ?? 0)}
                </span>
                <span>Resource Breakdown:</span>
                <ul class="flex flex-col gap-sm ml-md">
                  {#each resourceFields as field}
                    {#if field === selectedCrew.resources.type}
                      <li>
                        <span>{toCapitalizedWords(field)}:</span>
                        <span>
                          {toTerritoryData?.[field]} → {Number(toTerritoryData?.[field] ?? 0) +
                            selectedCrew.totalResources}
                        </span>
                      </li>
                    {:else}
                      <li>
                        <span>{toCapitalizedWords(field)}:</span>
                        <span>
                          {toTerritoryData?.[field]}
                        </span>
                      </li>
                    {/if}
                  {/each}
                </ul>
              </div>
            {:else}
              <div class="flex flex-col gap-xs">
                <!--eslint-disable @typescript-eslint/no-unused-vars -->
                {#each Array(SKELETON_COUNT) as _}
                  <Skeleton />
                {/each}
              </div>
            {/if}
          </div>

          <div class="flex-1 min-w-[225px]">
            <DatePicker
              isInvalid={!validateDate(arrivalDate)}
              invalidText="Date must be between storm start and end dates"
              label="Arrival Date"
              minDate={DateTime.fromJSDate(stormStartDate).toFormat('MM/dd/yyyy')}
              value={arrivalDate.isValid ? arrivalDate.toFormat('MM/dd/yyyy') : ''}
              onChange={(value) => {
                arrivalDate = DateTime.fromFormat(value, 'MM/dd/yyyy');
              }}
            />
          </div>

          <div class="flex-1">
            <TimePicker
              label="Arrival Time"
              value={arrivalTime}
              onChange={(value) => {
                arrivalTime = value;
              }}
            />
          </div>
        </div>
      {/if}
    </div>

    <Dialog.Footer>
      <Button variant="tertiary" onclick={() => (isOpen = false)}>Cancel</Button>
      <Button disabled={isSaving} variant="default" onclick={onSubmit}>Save</Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
