<script lang="ts">
  import type { CellValueChangedEvent, GridApi } from 'ag-grid-enterprise';
  import { Table } from '$lib/components';
  import { Card } from '$lib/components/ui/card';
  import * as Alert from '$lib/components/ui/alert';
  import { Button } from '$lib/components/ui/button';
  import * as Dialog from '$lib/components/ui/dialog';
  import { getDefaultResourcesColumns } from '$lib/config/table';
  import { Components, type RegionalResourceState, type ResourceData } from '$lib/types';
  import { getDefaultResourcesV2, postDefaultResourcesV2 } from '$lib/api';
  import store from '$lib/stores/app.svelte';

  const resourceFields: string[] =
    store.componentsConfig?.[Components.RESOURCE_VALIDATION]?.validations || [];

  interface Props {
    pinResourcesSummaryRow: (api: GridApi) => void;
    stormId: string | null;
    onSave: () => void;
  }

  let { pinResourcesSummaryRow, stormId, onSave }: Props = $props();

  let isLoadingDefaultResources = $state<boolean>(true);
  let isOpen = $state<boolean>(false);
  let isSaving = $state<boolean>(false);
  let errorMessage = $state<string>('');
  let stormDefaultResources: RegionalResourceState = {
    regionalResourceStates: [],
    stormId: ''
  };
  let stormDefaultResourcesRowData = $state<ResourceData[]>([]);
  let isTableTouched = $state<boolean>(false);

  async function onSubmit(e: Event) {
    e.preventDefault();

    if (!stormId || !isTableTouched) return;
    stormDefaultResources.stormId = stormId;

    try {
      isSaving = true;
      await postDefaultResourcesV2(stormDefaultResources);
      onSave();
      isOpen = false;
    } catch (err) {
      errorMessage = 'Could not save default resources';
    } finally {
      isSaving = false;
    }
  }

  async function fetchDefaultResourcesV2() {
    if (!stormId) return;

    try {
      const resources = await getDefaultResourcesV2(stormId);
      setDefaultResourcesV2(resources);
    } catch (err) {
      errorMessage = 'Could not load default resources';
    } finally {
      isLoadingDefaultResources = false;
    }
  }

  function setDefaultResourcesV2(rs: RegionalResourceState) {
    stormDefaultResources = rs;
    stormDefaultResourcesRowData = rs.regionalResourceStates
      .sort((a, b) => a.territoryName.localeCompare(b.territoryName))
      .map((r, i) => {
        const total = Object.keys(r.resources).reduce((acc, key) => acc + r.resources[key], 0);
        return {
          ...r.resources,
          id: i + 1,
          territoryId: r.territoryId,
          territoryName: r.territoryName,
          total
        };
      });
  }

  function updateDefaultResourcesV2(event: CellValueChangedEvent<Record<string, any>>) {
    if (!event.colDef.field || !resourceFields.includes(event.colDef.field)) return;

    const value = Number(event.value);
    if (isNaN(value) || value < 0) {
      errorMessage = 'Enter non-negative numeric values.';
      return;
    }

    errorMessage = '';
    isTableTouched = true;

    const body: RegionalResourceState = {
      regionalResourceStates: []
    };

    body.regionalResourceStates = stormDefaultResourcesRowData.map((d) => {
      const resources: Record<string, number> = {};
      resourceFields.forEach((field) => (resources[field] = Number(d[field])));
      return {
        territoryId: d.territoryId?.toString() || '',
        territoryName: d.territoryName,
        resources
      };
    });
    setDefaultResourcesV2(body);
  }
</script>

<Dialog.Root
  bind:open={isOpen}
  onOpenChange={async (isOpening) => {
    if (isOpening) {
      isLoadingDefaultResources = true;
      isTableTouched = false;
      errorMessage = '';
      await fetchDefaultResourcesV2();
    }
  }}
>
  <Dialog.Trigger>
    <Button size="sm"><span class="material-icons">edit</span>Edit</Button>
  </Dialog.Trigger>

  <Dialog.Content class="add-resources max-w-[800px]">
    <Dialog.Title>Edit Storm Event Defaults</Dialog.Title>

    <div class="flex flex-col">
      {#if errorMessage}
        <Alert.Root variant="error">
          <Alert.Title>Error:</Alert.Title>
          <Alert.Description>{errorMessage}</Alert.Description>
        </Alert.Root>
      {/if}

      <div class="flex mb-sm text-accentText text-xs items-center gap-xs">
        <span class="material-icons !text-sm">info</span>
        <span class="text-accentText">Select cell to edit</span>
      </div>

      <Card>
        <Table
          columnDefs={getDefaultResourcesColumns()}
          csvTitle="DefaultResources"
          domLayout="autoHeight"
          isLoading={isLoadingDefaultResources}
          rowData={stormDefaultResourcesRowData}
          onCellValueChanged={updateDefaultResourcesV2}
          onSetGridData={(gridApi) => pinResourcesSummaryRow(gridApi)}
        />
      </Card>
    </div>

    <Dialog.Footer>
      <Button variant="tertiary" onclick={() => (isOpen = false)}>Cancel</Button>
      <Button disabled={isSaving || !isTableTouched} onclick={onSubmit}>Save</Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
