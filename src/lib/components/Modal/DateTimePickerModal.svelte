<script lang="ts">
  import { DatePicker, TimePicker } from '$lib/components';
  import { Button } from '$lib/components/ui/button';
  import * as Dialog from '$lib/components/ui/dialog';
  import * as Select from '$lib/components/ui/select';
  import { timezoneAbbr } from '$lib/stores/constants';
  import { DateTime } from 'luxon';
  import type { Snippet } from 'svelte';

  interface Props {
    maxDate?: string | null; // mm/dd/yyyy
    minDate?: string | null; // mm/dd/yyyy
    presetDate?: string | null; // ISO date string
    title?: string;
    onReset: () => void;
    onSave: (date: DateTime) => void;
    children?: Snippet;
  }

  let {
    maxDate = null,
    minDate = null,
    presetDate = null,
    title = 'Select Date & Time',
    onReset,
    onSave,
    children
  }: Props = $props();

  let date = $state<string>(DateTime.now().toFormat('MM/dd/yyyy'));
  let isOpen = $state<boolean>(false);
  let time = $state<string>('12:00'); // HH:mm

  function save() {
    const dateStr = `${date} ${time}`;
    const luxonDate = DateTime.fromFormat(dateStr, 'MM/dd/yyyy HH:mm');
    onSave(luxonDate);
    isOpen = false;
  }
</script>

<Dialog.Root
  bind:open={isOpen}
  onOpenChange={(isOpening) => {
    if (isOpening) {
      if (presetDate) {
        const luxonDate = DateTime.fromISO(presetDate);
        date = luxonDate.toFormat('MM/dd/yyyy');
        time = luxonDate.toFormat('HH:mm');
      } else {
        date = DateTime.now().toFormat('MM/dd/yyyy');
        time = '12:00';
      }
    }
  }}
>
  <Dialog.Trigger>
    {@render children?.()}
  </Dialog.Trigger>

  <Dialog.Content class="date-time-picker-modal max-w-[500px]">
    <Dialog.Title>{title}</Dialog.Title>

    <div class="flex flex-col gap-md">
      <DatePicker
        class="w-[170px]"
        label="Date"
        {maxDate}
        {minDate}
        value={date}
        onChange={(value) => (date = value)}
      />

      <div class="flex gap-xs">
        <TimePicker class="w-[170px]" value={time} onChange={(value) => (time = value)} />

        <Select.Root type="single" value={timezoneAbbr}>
          <Select.Trigger class="w-[85px]" disabled={true}>
            {timezoneAbbr}
          </Select.Trigger>
        </Select.Root>
      </div>
    </div>

    <Dialog.Footer>
      <Button variant="tertiary" onclick={() => (isOpen = false)}>Cancel</Button>
      <Button
        variant="destructive"
        onclick={() => {
          onReset();
          isOpen = false;
        }}
      >
        Reset
      </Button>
      <Button variant="default" onclick={save}>Save</Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
