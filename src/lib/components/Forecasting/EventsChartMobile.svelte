<script lang="ts">
  import { AgChart, ButtonGroup } from '$lib/components';
  import { Button } from '$lib/components/ui/button';
  import { EVENT_TYPE_DONUT_CHART_COLOR_MAP } from '$lib/config/chart';
  import type { DailyEvent } from '$lib/types';
  import { getDonutSumAmount, getPercentage, formatNumbers } from '$lib/utils';
  import type {
    AgChartLabelFormatterParams,
    AgSeriesTooltipRendererParams
  } from 'ag-charts-enterprise';

  interface Props {
    dailyEvents: DailyEvent[];
  }

  let { dailyEvents }: Props = $props();

  let selectedRegion = $state<string>('');
  let selectedView = $state<number>(0);

  let showSystemView = $derived(selectedView === 0);

  let filteredDailyEvents = $derived(
    dailyEvents
      .filter((e) => (showSystemView ? e.territory === 'System' : e.territory !== 'System'))
      .sort((a, b) => a.territory.localeCompare(b.territory))
  );

  let chartData = $derived(
    dailyEvents
      .filter((e) => {
        return showSystemView ? e.territory === 'System' : e.territory === selectedRegion;
      })
      .map((e) => ({
        eventTypes: Object.entries(e.eventTypes).map(([group, value]) => ({ group, value })),
        group: e.territory
      }))[0]
  );

  $effect(() => {
    if (dailyEvents.length) {
      const regionalTerritories = dailyEvents.filter((e) => e.territory !== 'System');
      if (regionalTerritories.length)
        selectedRegion = regionalTerritories.sort((a, b) =>
          a.territory.localeCompare(b.territory)
        )[0].territory;
    }
  });
</script>

<div class="w-full">
  <div class="mb-4 flex justify-between">
    <span class="text-lg">Forecasting Event Types</span>
    <ButtonGroup bind:value={selectedView}>
      <Button value={0}>System</Button>
      <Button value={1}>Region</Button>
    </ButtonGroup>
  </div>
  <div class="w-full flex flex-col gap-sm">
    <div class="regions flex gap-sm flex-wrap justify-center">
      {#each filteredDailyEvents as event}
        {#if !showSystemView}
          <Button
            class="region-button {selectedRegion === event.territory ? 'selected' : ''}"
            size="sm"
            variant="outline"
            onclick={() => (selectedRegion = event.territory)}
          >
            {event.territory}
          </Button>
        {/if}
      {/each}
    </div>

    {#if chartData}
      <div class="flex-1">
        <AgChart
          className="outage-donut-chart"
          data={chartData.eventTypes}
          palette={{
            fills: Object.values(EVENT_TYPE_DONUT_CHART_COLOR_MAP),
            strokes: Object.values(EVENT_TYPE_DONUT_CHART_COLOR_MAP)
          }}
          options={{
            height: 250,
            legend: {
              position: 'bottom',
              enabled: true
            },
            series: [
              {
                sectorSpacing: 2,
                angleKey: 'value',
                calloutLabelKey: 'group',
                legendItemKey: 'group',
                strokeOpacity: 0,
                calloutLabel: {
                  formatter: ({ datum }: AgChartLabelFormatterParams<{ value: number }>) =>
                    getPercentage(datum, chartData.eventTypes)
                },
                tooltip: {
                  renderer: ({
                    datum
                  }: AgSeriesTooltipRendererParams<{ value: number; group: string }>) =>
                    `<div class="ag-chart-tooltip-content">${datum.group}: ${formatNumbers(
                      datum.value,
                      2
                    )}</div>`
                },
                innerLabels: [
                  {
                    text: formatNumbers(getDonutSumAmount(chartData.eventTypes), 2),
                    fontWeight: 'bold'
                  }
                ],
                innerRadiusRatio: 0.7,
                type: 'donut'
              }
            ]
          }}
        />
      </div>
    {/if}
  </div>
</div>

<style>
  :global(.region-button) {
    background: var(--color-component);
    border: 1px solid var(--color-border);
    padding: 6px;
  }

  :global(.region-button:last-child) {
    margin: 0;
  }

  :global(.region-button.selected) {
    box-shadow: 0 0 0 2px var(--color-primary);
  }
</style>
