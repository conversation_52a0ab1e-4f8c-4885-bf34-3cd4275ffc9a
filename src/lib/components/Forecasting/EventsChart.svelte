<script lang="ts">
  import { AgChart, ButtonGroup } from '$lib/components';
  import { Card } from '$lib/components/ui/card';
  import { Button } from '$lib/components/ui/button';
  import { EVENT_TYPE_DONUT_CHART_COLOR_MAP } from '$lib/config/chart';
  import { getDonutSumAmount, getPercentage, formatNumbers } from '$lib/utils';
  import type { DailyEvent } from '$lib/types';
  import type {
    AgChartLabelFormatterParams,
    AgSeriesTooltipRendererParams
  } from 'ag-charts-enterprise';

  interface Props {
    dailyEvents: DailyEvent[];
  }

  let { dailyEvents }: Props = $props();

  let selectedView = $state<number>(0);

  let showSystemView = $derived(selectedView === 0);

  let chartData = $derived(
    dailyEvents
      .filter((e) => (showSystemView ? e.territory === 'System' : e.territory !== 'System'))
      .sort((a, b) => a.territory.localeCompare(b.territory))
      .map((e) => ({
        eventTypes: Object.entries(e.eventTypes).map(([group, value]) => ({ group, value })),
        group: e.territory
      }))
  );
</script>

<div class="event-chart w-full">
  <div class="mb-4 flex justify-between">
    <h2>Forecasting Event Types</h2>
    <ButtonGroup bind:value={selectedView}>
      <Button value={0}>System</Button>
      <Button value={1}>Region</Button>
    </ButtonGroup>
  </div>

  <div
    class="daily-event-card-container flex items-center gap-sm {showSystemView
      ? 'center'
      : ''} flex-wrap"
  >
    {#each chartData as event}
      <Card class="daily-event-card !p-1">
        <AgChart
          className="outage-donut-chart"
          data={event.eventTypes}
          palette={{
            fills: Object.values(EVENT_TYPE_DONUT_CHART_COLOR_MAP),
            strokes: Object.values(EVENT_TYPE_DONUT_CHART_COLOR_MAP)
          }}
          options={{
            title: {
              text: event.group
            },
            height: 200,
            legend: {
              position: 'right',
              enabled: true
            },
            series: [
              {
                sectorSpacing: 2,
                angleKey: 'value',
                calloutLabelKey: 'group',
                legendItemKey: 'group',
                strokeOpacity: 0,
                calloutLabel: {
                  formatter: ({ datum }: AgChartLabelFormatterParams<{ value: number }>) =>
                    getPercentage(datum, event.eventTypes)
                },
                tooltip: {
                  renderer: ({
                    datum
                  }: AgSeriesTooltipRendererParams<{ value: number; group: string }>) =>
                    `<div class="ag-chart-tooltip-content">${datum.group}: ${formatNumbers(
                      datum.value,
                      2
                    )}</div>`
                },
                innerLabels: [
                  {
                    text: formatNumbers(getDonutSumAmount(event.eventTypes), 2),
                    fontWeight: 'bold'
                  }
                ],
                innerRadiusRatio: 0.7,
                type: 'donut'
              }
            ]
          }}
        />
      </Card>
    {/each}
  </div>
</div>

<style>
  .daily-event-card-container {
    justify-content: flex-start;
  }

  .daily-event-card-container.center {
    justify-content: center;
  }

  :global(.daily-event-card) {
    width: calc(20% - 0.5rem);
  }
  @media (max-width: 2000px) {
    :global(.daily-event-card) {
      width: calc(25% - 0.5rem);
    }
  }

  @media (max-width: 1400px) {
    :global(.daily-event-card) {
      width: calc(33.33% - 0.5rem);
    }
  }
  @media (max-width: 1150px) {
    :global(.daily-event-card) {
      width: calc(50% - 0.5rem);
    }
  }
</style>
