<script lang="ts">
  import { Tooltip } from '$lib/components';
  import { isDarkMode } from '$lib/stores/theme';
  import store from '$lib/stores/app.svelte';
  import type { EventLevel } from '$lib/types';

  interface Props {
    className?: string;
    currentEventLevel: EventLevel | null;
    viewLevel?: 'system' | 'region';
  }

  let { className = '', currentEventLevel = null, viewLevel = 'system' }: Props = $props();

  let filteredEventLevels = $derived(
    store.eventLevels.filter((level) => level.view === viewLevel && level.range?.length > 0)
  );

  function formatEventRange(range: number[], index: number) {
    if (index === filteredEventLevels.length - 1) {
      return `${range[0].toLocaleString()}+`;
    }
    return range.map((r) => r.toLocaleString()).join(' - ');
  }
</script>

<div class="events-legend {className}">
  <div class="flex border rounded-sm border-solid border-borderColor">
    {#each filteredEventLevels as level, index}
      <Tooltip disableHoverableContent>
        {#snippet content()}
          {level.description}
        {/snippet}
        {#snippet trigger()}
          <div
            class="flex flex-col text-xs items-center justify-center py-xs px-sm event relative w-18 h-8"
            style="background-color: {$isDarkMode ? level.color?.dark : level.color?.light}"
            class:dim={currentEventLevel !== null &&
              currentEventLevel.eventCode !== index.toString()}
            class:highlighted={currentEventLevel !== null &&
              currentEventLevel.eventCode === index.toString()}
            class:first-child={index === 0}
            class:last-child={index === filteredEventLevels.length - 1}
          >
            <span class="font-semibold text-primaryText whitespace-nowrap category"
              >{level.category ?? `L${index}`}</span
            >
            <span class="text-primaryText whitespace-nowrap eventRange">
              {formatEventRange(level.range, index)}
            </span>
          </div>
        {/snippet}
      </Tooltip>
    {/each}
  </div>
</div>

<style>
  .event {
    position: relative;
  }

  .highlighted {
    box-shadow: inset 0 0 0 1px var(--color-text);
  }

  .event.first-child {
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
  }

  .event.last-child {
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
  }

  .dim {
    opacity: 0.6;
  }

  .highlighted::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid var(--color-text);
  }

  @media (max-width: 575px) {
    .event {
      padding-left: 0.4rem;
      padding-right: 0.4rem;
    }

    .category {
      font-size: 0.7rem;
    }
    .eventRange {
      display: none;
    }
  }
</style>
