<script lang="ts">
  import { Card } from '$lib/components/ui/card';
  import { Skeleton } from '$lib/components/ui/skeleton';
  import store from '$lib/stores/app.svelte';
  import { isSystemic } from '$lib/stores/constants';
</script>

<Card class="flex flex-col w-full !p-0">
  {#if !isSystemic}
    <div class="flex p-md border-b border-solid border-borderColor">
      <div class="flex-1 flex flex-col items-center">
        <p class="text-center">Sys. {store.capitalizedOutagesLabel}</p>
        <Skeleton class="h-[15px] w-[70px]" />
      </div>
      <div class="flex-1 flex flex-col items-center">
        <p class="text-center">Sys. Customers</p>
        <Skeleton class="h-[15px] w-[70px]" />
      </div>
    </div>
  {/if}
  <div class="header flex gap-lg flex-wrap justify-evenly p-md">
    <Skeleton class="p-sm h-[80px] w-[80px] self-center" />
    <div class="flex gap-lg flex-wrap justify-center">
      <div class="flex flex-col gap-sm items-center min-w-[80px]">
        <p class="text-accentText">Temp.</p>
        <div class="flex flex-col items-center text-center">
          <Skeleton class="h-[15px] w-[70px]" />
          <Skeleton class="h-[15px] w-[105px]" />
        </div>
      </div>
      <div class="flex flex-col gap-sm items-center min-w-[80px]">
        <p class="text-accentText">Wind</p>
        <div class="flex flex-col items-center text-center">
          <Skeleton class="h-[15px] w-[70px]" />
          <Skeleton class="h-[15px] w-[105px]" />
        </div>
      </div>
      <div class="flex flex-col gap-sm items-center min-w-[80px]">
        <p class="text-accentText">Prcp.</p>
        <Skeleton class="h-[15px] w-[70px]" />
      </div>
    </div>
  </div>
</Card>
