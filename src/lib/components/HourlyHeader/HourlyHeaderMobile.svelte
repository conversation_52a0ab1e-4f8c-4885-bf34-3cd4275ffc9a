<script lang="ts">
  import { base } from '$app/paths';
  import type { HourlyWeatherAttributes, HourlyOutageAttributes } from '$lib/types';
  import { getHourlyWeatherIcon } from '$lib/config/utils';
  import { formatNumbers, formatRange } from '$lib/utils';
  import store from '$lib/stores/app.svelte';
  import { isSystemic } from '$lib/stores/constants';
  import { Tooltip } from '$lib/components';
  import { Card } from '$lib/components/ui/card';

  interface Props {
    outageData: HourlyOutageAttributes | undefined;
    weatherData: HourlyWeatherAttributes | undefined;
  }

  let { outageData, weatherData }: Props = $props();

  let icon = $derived(getHourlyWeatherIcon(weatherData));
</script>

<Card class="flex flex-col w-full !p-0">
  <div>
    {#if !isSystemic}
      <div class="flex p-md border-b border-solid border-borderColor">
        <div class="flex-1 flex flex-col items-center">
          <p class="text-center text-accentText font-semibold">
            Sys. {store.capitalizedOutagesLabel}
          </p>
          <span class="text-sm">
            {formatRange(outageData?.cumulativeMinOutages, outageData?.cumulativeMaxOutages)}
          </span>
        </div>
        <div class="flex-1 flex flex-col items-center">
          <p class="text-center text-accentText font-semibold">Sys. Customers</p>
          <span class="text-sm">
            {formatRange(
              outageData?.cumulativeMinCustomersAffected,
              outageData?.cumulativeMaxCustomersAffected
            )}
          </span>
        </div>
      </div>
    {/if}
    <div class="header flex gap-lg flex-wrap justify-evenly p-md">
      <Tooltip>
        {#snippet content()}
          {icon.description}
        {/snippet}
        {#snippet trigger()}
          <img
            src={`${base}/images/weather/${icon.type}.svg`}
            class="weatherIcon w-[60px] h-[60px] sm:w-[80px] sm:h-[80px]"
            alt={icon.type}
          />
        {/snippet}
      </Tooltip>

      <div class="flex gap-lg flex-wrap justify-evenly w-full">
        <div class="flex flex-col gap-sm items-center">
          <p class="text-center text-accentText">Temp.</p>
          <div class="flex flex-col gap-xs text-center">
            <span class="text-sm">
              {formatNumbers(weatherData?.temperatureLow, 0)} °F (min)
            </span>
            <span class="text-sm">
              {formatNumbers(weatherData?.temperatureHigh, 0)} °F (max)
            </span>
          </div>
        </div>
        <div class="flex flex-col gap-sm items-center">
          <p class="text-center text-accentText">Wind</p>
          <div class="flex flex-col gap-xs text-center">
            <span class="text-sm">
              {formatNumbers(weatherData?.averageWindSustained, 1)} mph avg.
            </span>
            <span class="text-sm">
              {formatNumbers(weatherData?.windGustMaxHigh, 1)} mph gusts
            </span>
          </div>
        </div>
        <div class="flex flex-col gap-sm items-center">
          <p class="text-center text-accentText">Prcp.</p>
          <span class="text-sm">
            {formatRange(weatherData?.precipMin, weatherData?.precipMax, 1)} in
          </span>
        </div>
      </div>
    </div>
  </div>
</Card>

<style>
  .header {
    background-color: var(--color);
  }
</style>
