<script lang="ts">
  import { base } from '$app/paths';
  import { Tooltip } from '$lib/components';
  import { Card } from '$lib/components/ui/card';
  import { getHourlyWeatherIcon } from '$lib/config/utils';
  import type { HourlyWeatherAttributes, HourlyOutageAttributes } from '$lib/types';
  import store from '$lib/stores/app.svelte';
  import { isSystemic } from '$lib/stores/constants';
  import { formatNumbers, formatRange } from '$lib/utils';

  interface Props {
    outageData: HourlyOutageAttributes | undefined;
    weatherData: HourlyWeatherAttributes | undefined;
  }

  let { outageData, weatherData }: Props = $props();

  let icon = $derived(getHourlyWeatherIcon(weatherData));
</script>

<Card class="!p-0 w-full">
  <div class="header p-md flex flex-wrap gap-lg justify-around">
    {#if !isSystemic}
      <div class="flex gap-md items-center">
        <span class="material-icons text-slate-500"> settings_alert </span>
        <div>
          <p class="text-center text-accentText">Sys. {store.capitalizedOutagesLabel}</p>
          <span class="text-sm">
            {formatRange(outageData?.cumulativeMinOutages, outageData?.cumulativeMaxOutages)}
          </span>
        </div>
      </div>
      <div class="divider"></div>
      <div class="flex gap-md items-center">
        <span class="material-icons text-gray-500"> group_remove </span>
        <div>
          <p class="text-center text-accentText">Sys. Customers</p>
          <span class="text-sm">
            {formatRange(
              outageData?.cumulativeMinCustomersAffected,
              outageData?.cumulativeMaxCustomersAffected
            )}
          </span>
        </div>
      </div>
      <div class="divider"></div>
    {/if}
    <div class="flex gap-md items-center">
      <Tooltip>
        {#snippet content()}
          {icon.description}
        {/snippet}
        {#snippet trigger()}
          <img
            src={`${base}/images/weather/${icon.type}.svg`}
            class="weatherIcon mb-sm"
            alt={icon.type}
            width="28"
            height="28"
          />
        {/snippet}
      </Tooltip>
      <div>
        <p class="text-center text-accentText">Temperature</p>
        <span class="text-sm">
          {formatRange(weatherData?.temperatureLow, weatherData?.temperatureHigh)} °F
        </span>
      </div>
    </div>
    <div class="divider"></div>
    <div class="flex gap-md items-center">
      <span class="material-icons text-blue-500"> rainy </span>
      <div>
        <p class="text-center text-accentText">Precipitation</p>
        <span class="text-sm">
          {formatRange(weatherData?.precipMin, weatherData?.precipMax, 1)} in
        </span>
      </div>
    </div>
    <div class="divider"></div>
    <div class="flex gap-md items-center">
      <span class="material-icons text-slate-400"> air </span>
      <div>
        <p class="text-center text-accentText">Wind Gusts</p>
        <span class="text-sm">
          {formatRange(weatherData?.windGustMaxLow, weatherData?.windGustMaxHigh, 1)} mph
        </span>
      </div>
    </div>
    <div class="divider"></div>
    <div class="flex gap-md items-center">
      <span class="material-icons text-slate-500"> airwave </span>
      <div>
        <p class="text-center text-accentText">Avg. Sustained Wind</p>
        <span class="text-sm">
          {formatNumbers(weatherData?.averageWindSustained, 1)} mph
        </span>
      </div>
    </div>
  </div>
</Card>

<style>
  .divider {
    border-left: 2px solid var(--color-border);
  }
</style>
