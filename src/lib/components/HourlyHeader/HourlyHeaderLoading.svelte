<script lang="ts">
  import { base } from '$app/paths';
  import { Card } from '$lib/components/ui/card';
  import { Skeleton } from '$lib/components/ui/skeleton';
  import store from '$lib/stores/app.svelte';
  import { isSystemic } from '$lib/stores/constants';
</script>

<Card class="!p-0 w-full">
  <div class="!p-0 w-full">
    <div class="header p-md flex flex-wrap gap-lg justify-around">
      {#if !isSystemic}
        <div class="flex gap-md items-center">
          <span class="material-icons text-slate-500"> settings_alert </span>
          <div class="flex flex-col gap-sm">
            <p class="text-center text-accentText">Sys. {store.capitalizedOutagesLabel}</p>
            <Skeleton class="h-[14px]" />
          </div>
        </div>
        <div class="divider"></div>
        <div class="flex gap-md items-center">
          <span class="material-icons text-gray-500">group_remove </span>
          <div class="flex flex-col gap-sm">
            <p class="text-center text-accentText">Sys. Customers</p>
            <Skeleton class="h-[14px]" />
          </div>
        </div>
        <div class="divider"></div>
      {/if}
      <div class="flex gap-md items-center">
        <img
          src={`${base}/images/weather/unknown.svg`}
          class="weatherIcon mb-sm"
          alt={'unkown'}
          width="28"
          height="28"
        />
        <div class="flex flex-col gap-sm">
          <p class="text-center text-accentText">Temperature</p>
          <Skeleton class="h-[14px]" />
        </div>
      </div>
      <div class="divider"></div>
      <div class="flex gap-md items-center">
        <span class="material-icons text-blue-500"> rainy </span>
        <div class="flex flex-col gap-sm">
          <p class="text-center text-accentText">Precipitation</p>
          <Skeleton class="h-[14px]" />
        </div>
      </div>
      <div class="divider"></div>
      <div class="flex gap-md items-center">
        <span class="material-icons text-slate-500"> air </span>
        <div class="flex flex-col gap-sm">
          <p class="text-center text-accentText">Wind Gusts</p>
          <Skeleton class="h-[14px]" />
        </div>
      </div>
      <div class="divider"></div>
      <div class="flex gap-sm items-center">
        <span class="material-icons text-slate-500"> airwave </span>
        <div class="flex flex-col gap-sm">
          <p class="text-center text-accentText">Avg. Sustained Wind</p>
          <Skeleton class="h-[14px]" />
        </div>
      </div>
    </div>
  </div>
</Card>

<style>
  .divider {
    border-left: 2px solid var(--color-border);
  }
</style>
