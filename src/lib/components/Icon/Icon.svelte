<script lang="ts">
  /* eslint-disable svelte/no-at-html-tags */
  import type { Icon } from '$lib/types';
  import { ICONS } from './icons';

  interface Props {
    class?: string;
    focusable?: boolean;
    height?: string;
    name: keyof typeof ICONS;
    width?: string;
  }

  let {
    class: className = '',
    focusable = false,
    height = '1rem',
    name,
    width = '1rem'
  }: Props = $props();

  let displayIcon: Icon = $derived(ICONS[name] || ICONS.__default);
</script>

<svg
  class={className}
  fill="currentColor"
  focusable={focusable.toString()}
  {height}
  viewBox="0 0 {displayIcon.boxW || displayIcon.box} {displayIcon.boxH || displayIcon.box}"
  {width}
>
  {@html displayIcon.svg}
</svg>
