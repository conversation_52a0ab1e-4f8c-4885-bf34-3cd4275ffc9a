<script lang="ts">
  import Toast from './Toast.svelte';
  import toastService from '$lib/stores/toast.svelte';
</script>

{#if toastService.toasts}
  <section
    class="fixed top-4 left-1/2 transform -translate-x-1/2 flex flex-col gap-sm items-center z-[1000]"
  >
    {#each toastService.toasts as toast (toast.id)}
      <Toast variant={toast.type} onDismiss={() => toastService.dismissToast(toast.id)}>
        {toast.message}
      </Toast>
    {/each}
  </section>
{/if}
