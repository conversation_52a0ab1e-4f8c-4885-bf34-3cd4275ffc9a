<script lang="ts" module>
  import { Button } from '$lib/components/ui/button';
  import type { Snippet } from 'svelte';
  import { type VariantProps, tv } from 'tailwind-variants';

  export const toastVariants = tv({
    base: 'flex items-center justify-between w-96 rounded-md border p-md [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg~*]:pl-7 z-50 text-sm bg-component shadow-lg relative toast-before',
    variants: {
      variant: {
        error: 'error border-danger',
        warning: 'warning border-warning',
        info: 'info border-info',
        success: 'success border-success'
      }
    },
    defaultVariants: {
      variant: 'info'
    }
  });
</script>

<script lang="ts">
  interface Props {
    variant?: VariantProps<typeof toastVariants>['variant'];
    onDismiss: () => void;
    children?: Snippet;
  }

  let { variant = 'info', onDismiss = () => {}, children }: Props = $props();
</script>

<div class={toastVariants({ variant })} role="alert">
  <div class="flex gap-sm">
    {#if variant === 'error'}
      <span class="material-icons text-danger">error</span>
    {/if}
    {#if variant === 'success'}
      <span class="material-icons text-success">check_circle</span>
    {/if}
    {#if variant === 'warning'}
      <span class="material-icons text-warning">warning</span>
    {/if}
    {#if variant === 'info'}
      <span class="material-icons text-info">info</span>
    {/if}
    <div class="flex flex-wrap items-center gap-1">
      {@render children?.()}
    </div>
  </div>

  {#if onDismiss}
    <Button size="icon" variant="ghost" onclick={onDismiss}>
      <span class="material-icons">close</span>
    </Button>
  {/if}
</div>

<style>
  .toast-before::before {
    content: '';
    position: absolute;
    inset: 0;
    opacity: 0.25;
    z-index: -1;
  }

  .error::before {
    background-color: var(--color-danger);
  }

  .success::before {
    background-color: var(--color-success);
  }

  .info::before {
    background-color: var(--color-info);
  }

  .warning::before {
    background-color: var(--color-warning);
  }
</style>
