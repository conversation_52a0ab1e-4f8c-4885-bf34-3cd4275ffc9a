<script lang="ts">
  import { env } from '$env/dynamic/public';
  import { DEFAULT_GRID_OPTIONS } from '$lib/config/table';
  import { formatDate } from '$lib/config/utils';
  import { isDarkMode } from '$lib/stores/theme';
  import type { IColDef } from '$lib/types';
  import { LicenseManager, createGrid } from 'ag-grid-enterprise';
  import type {
    CellClickedEvent,
    CellKeyDownEvent,
    CellValueChangedEvent,
    DomLayoutType,
    GridApi,
    GridOptions,
    RowDoubleClickedEvent,
    TabToNextCellParams
  } from 'ag-grid-enterprise';

  interface Props {
    class?: string;
    columnDefs: IColDef[];
    csvTitle?: string;
    domLayout?: DomLayoutType;
    gridOptions?: GridOptions;
    isLoading?: boolean;
    rowData: Record<string, any>[];
    onCellClicked?: ((event: CellClickedEvent) => void) | undefined;
    onCellKeyDown?: ((event: CellKeyDownEvent) => void) | undefined;
    onCellValueChanged?:
      | ((event: CellValueChangedEvent<Record<string, string | number>>) => void)
      | undefined;
    onRowDoubleClicked?: ((event: RowDoubleClickedEvent) => void) | undefined;
    onSetGridData?: ((api: GridApi) => void) | undefined;
    tabToNextCell?: ((params: TabToNextCellParams) => void) | undefined;
  }

  let {
    class: className = '',
    columnDefs,
    csvTitle = 'tableData',
    domLayout = 'normal',
    gridOptions = {
      ...DEFAULT_GRID_OPTIONS,
      domLayout,
      onCellClicked: (event: CellClickedEvent) => {
        onCellClicked?.(event);
      },
      onCellKeyDown: (event: CellKeyDownEvent) => {
        onCellKeyDown?.(event);
      },
      onCellValueChanged: (event: CellValueChangedEvent<Record<string, string | number>>) => {
        onCellValueChanged?.(event);
      },
      onRowDoubleClicked: (event: RowDoubleClickedEvent) => {
        onRowDoubleClicked?.(event);
      },
      tabToNextCell: (params: TabToNextCellParams) => {
        if (tabToNextCell) {
          const nextCell = tabToNextCell(params);

          if (nextCell === null || nextCell === undefined) {
            return false;
          }

          return nextCell;
        }

        return params.nextCellPosition ?? false;
      }
    },
    isLoading = false,
    rowData,
    onCellClicked = undefined,
    onCellKeyDown = undefined,
    onCellValueChanged = undefined,
    onRowDoubleClicked = undefined,
    onSetGridData = undefined,
    tabToNextCell = undefined
  }: Props = $props();

  let gridApi = $state<GridApi | undefined>();
  let gridContainer = $state<HTMLDivElement | undefined>();
  let isInitialGrid = $state<boolean>(true);

  LicenseManager.setLicenseKey(env.PUBLIC_AG_CHARTS_ENTERPRISE_KEY);

  $effect(() => {
    if (!isInitialGrid && columnDefs) {
      gridApi!.setGridOption('columnDefs', columnDefs);
    }
  });

  $effect(() => {
    if (rowData && gridContainer) setGridData();
  });

  $effect(() => {
    if (gridApi) gridApi.setGridOption('loading', isLoading);
  });

  async function setGridData() {
    if (isInitialGrid) {
      isInitialGrid = false;
      gridOptions.columnDefs = columnDefs;
      gridOptions.defaultCsvExportParams = {
        fileName: `${csvTitle}_${formatDate(Date.now())}`,
        columnKeys: columnDefs
          .filter(({ preventExport }) => !preventExport)
          .map(({ field }) => field!)
      };
      gridOptions.rowData = rowData;
      gridApi = createGrid(gridContainer!, gridOptions);
    } else {
      gridApi!.setGridOption('rowData', rowData);
      gridApi!.refreshCells({ force: true });
    }

    if (onSetGridData) onSetGridData(gridApi!);
  }
</script>

<div
  class="table {$isDarkMode ? 'ag-theme-alpine-dark' : 'ag-theme-alpine'} {className}"
  bind:this={gridContainer}
></div>

<style>
  .table {
    height: 100%;
    width: 100%;
    display: contents;
  }

  :global(.ag-theme-alpine-dark .ag-root-wrapper) {
    border: none;
    min-height: 0;
  }

  :global(.ag-theme-alpine .highlighted) {
    color: white;
  }

  :global(.ag-theme-alpine .ag-root-wrapper) {
    border: 1px solid #babfc7;
  }

  :global(.ag-theme-alpine-dark .ag-row),
  :global(.ag-theme-alpine .ag-row) {
    border: none;
  }

  :global(.table .material-icons.edit) {
    color: var(--color-primary);
    font-size: 16px;
    margin-left: 8px;
    position: relative;
    top: 3px;
  }

  :global(.table .highlight) {
    background: var(--color-accentGray);
  }

  :global(.table .highlight:hover) {
    background: var(--color-accentGray) !important;
  }

  /* Table Row Height */
  .ag-theme-alpine,
  .ag-theme-alpine-dark {
    --ag-grid-size: 5px;
  }

  :global(
    .ag-theme-alpine .ag-cell,
    .ag-theme-alpine .ag-header-cell,
    .ag-theme-alpine-dark .ag-cell,
    .ag-theme-alpine-dark .ag-header-cell
  ) {
    font-size: 12px;
  }
</style>
