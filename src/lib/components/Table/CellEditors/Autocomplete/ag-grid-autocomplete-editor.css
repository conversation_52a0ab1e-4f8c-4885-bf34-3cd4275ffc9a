.ag-cell-editor-autocomplete {
  background-color: var(--color-component);
  z-index: 1000;
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
  border: 1px solid rgba(50, 50, 50, 0.6);
  max-height: 25% !important;
  max-width: 25% !important;
}

.ag-cell-editor-autocomplete * {
  font: inherit;
}

.ag-cell-editor-autocomplete > div {
  margin: 0;
}

.ag-cell-editor-autocomplete > div.group {
  background-color: #32353f;
}

.ag-cell-editor-autocomplete > div:hover:not(.group),
.ag-cell-editor-autocomplete > div.selected {
  background: #393939;
  cursor: pointer;
}

.ag-cell-editor-autocomplete > div > * > strong {
  font-weight: bold !important;
}

.ag-wrapper.ag-input-wrapper.ag-text-field-input-wrapper.ag-cell-editor-autocomplete-wrapper,
.ag-input-field-input.ag-text-field-input.ag-cell-editor-autocomplete-input {
  height: 100%;
}
