<script lang="ts">
  import Check from 'lucide-svelte/icons/check';
  import ChevronsUpDown from 'lucide-svelte/icons/chevrons-up-down';
  import { tick } from 'svelte';
  import * as Command from '$lib/components/ui/command';
  import * as Popover from '$lib/components/ui/popover';
  import { Label } from '$lib/components/ui/label';
  import { Button } from '$lib/components/ui/button';
  import { cn } from '$lib/utils';

  interface ComboboxProps {
    items: string[];
    placeholder?: string;
    value?: string;
    onSelect?: (value: string) => void;
    onblur?: () => void;
    className?: string;
    invalidText?: string;
    isInvalid?: boolean;
    label?: string;
  }

  let {
    items,
    placeholder = 'Select an item...',
    value = $bindable(''),
    onSelect = () => {},
    onblur = () => {},
    invalidText = '',
    isInvalid = false,
    label = '',
    className = '',
    ...rest
  }: ComboboxProps = $props();

  let open = $state(false);
  let triggerRef = $state<HTMLButtonElement>(null!);
  let selectedIndex = $state(-1);
  let filteredItems = $derived(
    items.filter((item) => item.toLowerCase().includes(value.toLowerCase()))
  );
  let triggerWidth = $derived(`w-[${triggerRef.offsetWidth}px]`);

  // We want to refocus the trigger button when the user selects
  // an item from the list so users can continue navigating the
  // rest of the form with the keyboard.
  function closeAndFocusTrigger() {
    open = false;
    tick().then(() => {
      triggerRef.focus();
    });
  }

  function handleKeyDown(event: KeyboardEvent) {
    if (filteredItems.length === 0) return;
    if (event.key === 'ArrowDown') {
      event.preventDefault();
      selectedIndex = (selectedIndex + 1) % filteredItems.length;
      focusListItem();
    } else if (event.key === 'ArrowUp') {
      event.preventDefault();
      selectedIndex = (selectedIndex - 1 + filteredItems.length) % filteredItems.length;
      focusListItem();
    } else if (event.key === 'Tab') {
      if (selectedIndex >= 0) {
        handleItemClick(filteredItems[selectedIndex]);
      } else {
        selectedIndex++;
        focusListItem();
      }
    } else if (event.key === 'Enter') {
      event.preventDefault();
      event.stopPropagation();
      if (selectedIndex >= 0) {
        handleItemClick(filteredItems[selectedIndex]);
      }
    }
  }

  function focusListItem() {
    setTimeout(() => {
      document.querySelectorAll<HTMLLIElement>('.combobox-item')[selectedIndex]?.focus();
    }, 0);
  }

  function handleItemClick(item: string) {
    value = item;
    selectedIndex = 0;
    open = false;
  }
</script>

<Popover.Root bind:open>
  <div class="flex flex-col">
    {#if label}
      <Label class="text-xs mb-2">{label}</Label>
    {/if}
    <Popover.Trigger bind:ref={triggerRef}>
      {#snippet child({ props })}
        <Button
          variant="outline"
          class="w-full justify-between truncate {className}"
          {...props}
          role="combobox"
          aria-expanded={open}
        >
          {#if value}
            <span class="truncate">
              {value}
            </span>
          {:else}
            <span class="text-muted-foreground">
              {placeholder}
            </span>
          {/if}
          <ChevronsUpDown class="ml-2 size-4 shrink-0 opacity-50" />
        </Button>
      {/snippet}
    </Popover.Trigger>
    {#if isInvalid && invalidText}
      <p class="text-xs px-2 text-destructive">{invalidText}</p>
    {/if}
  </div>
  <Popover.Content class="p-0 {triggerWidth}">
    <Command.Root>
      <Command.Input
        placeholder="Search..."
        bind:value
        {...rest}
        onkeydown={handleKeyDown}
        {onblur}
      />
      <Command.List>
        <Command.Empty>No results found.</Command.Empty>
        <Command.Group>
          {#each items as item}
            <Command.Item
              class="combobox-item"
              value={item}
              onSelect={() => {
                value = item;
                closeAndFocusTrigger();
                onSelect?.(value);
              }}
            >
              <Check class={cn('mr-2 size-4', value !== item && 'text-transparent')} />
              <span class="truncate">
                {item}
              </span>
            </Command.Item>
          {/each}
        </Command.Group>
      </Command.List>
    </Command.Root>
  </Popover.Content>
</Popover.Root>
