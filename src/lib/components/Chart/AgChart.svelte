<script lang="ts">
  import { env } from '$env/dynamic/public';
  import { Loader } from '$lib/components';
  import { Button } from '$lib/components/ui/button';
  import * as Dialog from '$lib/components/ui/dialog';
  import { CHART_COLORS, CHART_COLORS_LIGHT } from '$lib/config/chart';
  import { isDarkMode } from '$lib/stores/theme';
  import store from '$lib/stores/app.svelte';
  import { AgCharts } from 'ag-charts-enterprise';
  import type { AgChartOptions, AgChartInstance } from 'ag-charts-enterprise';
  import { onDestroy } from 'svelte';

  interface Props {
    canExpand?: boolean;
    className?: string;
    data: Record<string, any>[];
    loading?: boolean;
    options: Record<string, any>;
    palette?: { fills?: string[]; strokes?: string[] };
    onMouseleave?: () => void;
  }

  let {
    canExpand = false,
    className = '',
    data = [],
    loading = false,
    options = {},
    palette = {},
    onMouseleave = () => {}
  }: Props = $props();

  let chart = $state<AgChartInstance | null>();
  let chartContainer = $state<HTMLDivElement>();
  let isOpen = $state<boolean>(false);

  AgCharts.setLicenseKey(env.PUBLIC_AG_CHARTS_ENTERPRISE_KEY);

  onDestroy(() => {
    chart?.destroy();
  });

  function getChartColors(): string[] {
    if (
      store.themeConfig &&
      store.themeConfig?.dark?.chartColors?.length &&
      store.themeConfig?.light?.chartColors?.length
    ) {
      return store.themeConfig[$isDarkMode ? 'dark' : 'light'].chartColors!;
    }

    return $isDarkMode ? CHART_COLORS : CHART_COLORS_LIGHT;
  }

  async function setupChart(isDarkMode: boolean) {
    const chartOptions: AgChartOptions = {
      background: { fill: isDarkMode ? '#161616' : '#fff' },
      container: chartContainer,
      data: data?.sort((a, b) => a?.region?.localeCompare(b?.region)),
      minHeight: 0,
      theme: {
        baseTheme: isDarkMode ? 'ag-default-dark' : 'ag-default',
        palette: {
          fills: getChartColors(),
          strokes: getChartColors(),
          ...palette
        },
        overrides: {
          line: {
            series: {
              highlightStyle: {
                series: {
                  dimOpacity: 0.3
                }
              },
              interpolation: {
                type: 'smooth'
              },
              marker: {
                itemStyler: () => ({
                  size: 4
                })
              }
            }
          },
          bar: {
            series: {
              highlightStyle: {
                series: {
                  dimOpacity: 0.3
                }
              }
            }
          }
        }
      },
      ...options
    };

    // clear data when series is empty so that stale data doesn't persist
    if (!chartOptions?.series?.length) {
      chartOptions.series = [];
      chartOptions.data = [];
    }

    if (chart) {
      chart.update({ ...chartOptions });
    } else {
      chart = AgCharts.create(chartOptions);
    }
  }

  $effect(() => {
    if (loading && chart) {
      chart.destroy();
      chart = null;
    }
  });

  $effect(() => {
    if (chartContainer && !loading && data && options && palette) {
      setupChart($isDarkMode);
    }
  });
</script>

<div class="chart flex bg-component {loading ? 'static' : 'relative'} {className}">
  {#if canExpand}
    <Dialog.Root onOpenChange={(isOpening) => (isOpen = isOpening)}>
      <Dialog.Trigger>
        <Button class="btn-expand" size="icon" variant="ghost">
          <span class="material-icons">settings_overscan</span>
        </Button>
      </Dialog.Trigger>

      <Dialog.Content class="max-w-[1000px]">
        <div bind:this={chartContainer} class="h-[600px]"></div>
      </Dialog.Content>
    </Dialog.Root>
  {/if}

  {#if loading}
    <div class="absolute inset-0 bg-component rounded-md flex">
      <Loader />
    </div>
  {/if}

  {#if !loading && !isOpen}
    <div
      bind:this={chartContainer}
      class="chart-container"
      role="region"
      onmouseleave={onMouseleave}
    ></div>
  {/if}
</div>

<style>
  .chart {
    height: 100%;
    overflow: hidden;
  }

  .chart-container {
    width: 100%;
    height: 100%;
  }

  :global(.chart .ag-chart-wrapper) {
    display: flex;
    flex: 1;
  }

  :global(.chart .btn-expand) {
    left: 6px;
    position: absolute;
    top: 6px;
    z-index: 1;
  }

  :global(.chart .ag-chart-no-data-overlay) {
    text-align: center;
  }
</style>
