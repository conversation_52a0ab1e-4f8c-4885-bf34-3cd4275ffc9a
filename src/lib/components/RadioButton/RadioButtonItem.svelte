<script lang="ts">
  import * as RadioGroup from '$lib/components/ui/radio-group';
  import { Label } from '$lib/components/ui/label';

  interface Props {
    id?: string;
    label?: string;
    ref?: HTMLElement | null;
    value: string;
  }

  let {
    id = crypto.randomUUID(),
    label = '',
    ref = $bindable(null),
    ...restProps
  }: Props = $props();
</script>

<div class="flex items-center gap-2">
  <RadioGroup.Item {id} bind:ref {...restProps} />
  {#if label}
    <Label for={id} onclick={(e) => e.stopPropagation()}>{label}</Label>
  {/if}
</div>
