<script lang="ts" module>
  import { getContext, setContext, type Snippet } from 'svelte';
  import { type VariantProps } from 'tailwind-variants';
  import type { buttonVariants } from '../ui/button';

  interface ButtonGroupCtx {
    size: VariantProps<typeof buttonVariants>['size'];
    variant: VariantProps<typeof buttonVariants>['variant'];
    onClick?: (e: MouseEvent) => void;
    getValue: () => string | number;
    setValue: (val: string | number) => void;
    disabled?: () => boolean;
  }

  export function setButtonGroupCtx(props: ButtonGroupCtx) {
    setContext('buttonGroup', props);
  }

  export function getButtonGroupCtx() {
    return getContext<ButtonGroupCtx>('buttonGroup');
  }
</script>

<script lang="ts">
  interface Props {
    class?: string;
    size?: VariantProps<typeof buttonVariants>['size'];
    variant?: VariantProps<typeof buttonVariants>['variant'];
    onClick?: (e: MouseEvent) => void;
    children: Snippet;
    value?: string | number;
    disabled?: boolean;
  }

  let {
    class: className,
    size = 'sm',
    variant = 'outline',
    onClick,
    children,
    value = $bindable(''),
    disabled = false
  }: Props = $props();

  setButtonGroupCtx({
    size,
    variant,
    onClick,
    getValue: () => value,
    setValue: (val) => (value = val),
    disabled: () => disabled
  });
</script>

<div class="button-group inline-flex gap-1 {className}" role="group">
  {@render children()}
</div>
