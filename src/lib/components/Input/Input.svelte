<script lang="ts">
  import { Input } from '$lib/components/ui/input';
  import type { InputProps } from '$lib/components/ui/input';
  import { Label } from '$lib/components/ui/label';
  import { cn } from '$lib/utils';

  interface Props {
    invalidText?: string;
    isInvalid?: boolean;
    label?: string;
    onChange?: (e: Event) => void;
  }

  let {
    class: className,
    disabled = false,
    id = crypto.randomUUID(),
    invalidText = '',
    isInvalid = false,
    label = '',
    ref = $bindable(null),
    type = 'text',
    value = $bindable(),
    onChange,
    ...restProps
  }: Props & InputProps = $props();
</script>

<div class="flex flex-col">
  {#if label}
    <Label class="text-xs mb-2" for={id}>{label}</Label>
  {/if}

  <Input
    aria-invalid={isInvalid}
    class={cn(isInvalid ? 'border-destructive' : '', className)}
    {disabled}
    bind:ref
    bind:value
    {type}
    onchange={onChange}
    {...restProps as InputProps}
  />

  {#if isInvalid && invalidText}
    <p class="text-xs px-2 text-destructive">{invalidText}</p>
  {/if}
</div>
