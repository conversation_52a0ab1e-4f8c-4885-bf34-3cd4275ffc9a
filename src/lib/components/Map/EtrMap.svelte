<script lang="ts">
  import { onDestroy, onMount } from 'svelte';
  import { Loader, MapStyleToggle, ResourceToggle, WindToggle } from '$lib/components';
  import {
    formatRegionId,
    getOutageFillLayer,
    DEFAULT_RADAR_CONFIG,
    MAP_BOUNDS,
    DEFAULT_WIND_CONFIG
  } from '$lib/config/map';
  import { isDarkMode } from '$lib/stores/theme';
  import store from '$lib/stores/app.svelte';
  import {
    showActiveResources,
    showEtrMapWindAnimation,
    showMapWeatherComponents
  } from '$lib/stores/constants';
  import type {
    RadarData,
    WeatherData,
    AggregateMetricRecordWithRegion,
    MapItem,
    WeatherStation,
    Region,
    MapOutageConfig,
    WindSpeed
  } from '$lib/types';
  import { Visibility, OutageSource } from '$lib/types';
  import { MapStyle } from '$lib/types';
  import { fitMapToBounds, formatNumbers, getClosestTimestamp } from '$lib/utils';
  import mapboxgl, {
    type GeoJSONSource,
    type ExpressionSpecification,
    type Map,
    type MapMouseEvent,
    type Popup
  } from 'mapbox-gl';
  import { getLatestWeatherData } from '$lib/api/metric';
  import MapVisibilityToggle from './MapVisibilityToggle.svelte';
  import RadarToggle from './RadarToggle.svelte';
  import PlayPause from './PlayPause.svelte';
  import SettingsPane from './SettingsPane.svelte';
  import WindAnimation from './WindAnimation.svelte';
  import WindAnimationToggle from './WindAnimationToggle.svelte';
  import MapLegend from './MapLegend.svelte';
  import WeatherPane from './WeatherPane.svelte';
  import OutagesSource from './OutagesSource.svelte';
  import LayersPane from './LayersPane.svelte';
  import type { Snippet } from 'svelte';

  type ClickHandler = (() => void) | ((regionName: string) => Promise<void>);

  const REGION_CIRCLE_TEXT_LAYER_ID = 'region-circle-text';

  const regionPopup = new mapboxgl.Popup({
    closeButton: false,
    closeOnClick: false
  });

  interface Props {
    class?: string;
    collapseLegend?: boolean;
    config?: MapItem[];
    loading?: boolean;
    outagesSource?: MapOutageConfig[];
    records?: Record<string, any>[];
    restrictMapBounds?: boolean;
    selectedConfig?: MapItem | null; // Either passed in or used internally
    showCentroids?: boolean;
    showPlayback?: boolean;
    showResourcesOnLoad?: boolean;
    showResources?: boolean;
    showWindAnimation?: boolean;
    showWindGustsOnLoad?: boolean;
    showWindGusts?: boolean;
    source?: OutageSource;
    systemOverviewData?: Record<string, AggregateMetricRecordWithRegion[]>;
    customPopup?: ((popup: Popup, record: Record<string, any>, map: Map) => void) | null;
    onClick?: ClickHandler | ((regionName: string) => void) | null;
    onLayerSelect?: ((config: MapItem) => void) | null;
    children?: Snippet;
  }

  let {
    class: className = '',
    collapseLegend = false,
    config = [],
    loading = false,
    outagesSource = [],
    records = $bindable([]),
    restrictMapBounds = false,
    selectedConfig = $bindable(null),
    showCentroids = $bindable(false),
    showPlayback = false,
    showResourcesOnLoad = false,
    showResources = showResourcesOnLoad,
    showWindAnimation = false,
    showWindGustsOnLoad = false,
    showWindGusts = showWindGustsOnLoad,
    source = $bindable(OutageSource.refined),
    systemOverviewData = {},
    customPopup = null,
    onClick = null,
    onLayerSelect = null,
    children
  }: Props = $props();

  let apiData: WeatherData | null = null;
  let radarTileCache = new Set();
  let selectedRegion: string | number | null = null;

  let hoveredRegion: string | null = $state(null);
  let activeRegion: string | null = $state(null);
  let mapContainer = $state<HTMLDivElement | undefined>();
  let map = $state<Map | undefined>();
  let isMapLoaded = $state<boolean>(false);
  let mapStyle = $state<MapStyle>($isDarkMode ? MapStyle.dark : MapStyle.light);
  let attribute = $state<Visibility | undefined>();
  let isRadarVisible = $state<boolean>(showPlayback);
  let isResourcesVisible = $state<boolean>(showResourcesOnLoad);
  let isWindVisible = $state<boolean>(showWindGustsOnLoad);
  let mapFrames = $state<RadarData[]>([]);
  let colorScheme = $state<string>(DEFAULT_RADAR_CONFIG.colorScheme);
  let frameIndex = $state<number>(0);
  let windSpeed = $state<WindSpeed>('20');
  let opacity = $state<number>(DEFAULT_RADAR_CONFIG.opacity);

  let weatherStations = $state<WeatherStation[]>([]);

  let isLoadingWeather = $state<boolean>(false);
  let isWindAnimationVisible = $state<boolean>(false);
  let particleCount = $state<number>(DEFAULT_WIND_CONFIG.particleCount);
  let particleSize = $state<number>(DEFAULT_WIND_CONFIG.particleSize);
  let particleLifespan = $state<number>(DEFAULT_WIND_CONFIG.particleLifespan);
  let speedFactor = $state<number>(DEFAULT_WIND_CONFIG.speedFactor);
  let overallOpacity = $state<number>(DEFAULT_WIND_CONFIG.overallOpacity);

  let mapFeatures = $derived(config.map((c) => c.attributeName));
  let etrMapId = $derived(store.appConfig?.etrMapId?.value ?? '');
  let showWind = $derived(
    showEtrMapWindAnimation && showWindAnimation && weatherStations?.length && !isLoadingWeather
  );

  $effect(() => {
    if (systemOverviewData && mapFrames.length) {
      updateSystemRecord(mapFrames[frameIndex].time);
    }
  });

  $effect(() => {
    if (store.activeMapRegion?.key) {
      updateMapSegment();
    }
  });

  $effect(() => {
    if (activeRegion && !store.activeMapRegion?.key) {
      clearMapSegment();
    }
  });

  $effect(() => {
    if (map && isMapLoaded && records) {
      onSetFeatureStates();
      setRegionTextDataToSource();

      if (isWindVisible || isResourcesVisible) setCentroidDataToSource();
      if (hoveredRegion) {
        const updatedRecord = records.find(
          (record: Record<string, any>) => formatRegionId(record.region) === hoveredRegion
        );
        if (updatedRecord) updatePopupContent(updatedRecord);
      }
    }
  });

  $effect(() => {
    if (selectedConfig) {
      updateLayer(selectedConfig.attributeName);
    }
  });

  const onWatchThemeMode = isDarkMode.subscribe((isDark) => {
    if (map && isMapLoaded) {
      mapStyle = isDark ? MapStyle.dark : MapStyle.light;
      changeMapStyle();
    }
  });

  onMount(() => {
    initialize();
  });

  onDestroy(() => {
    map?.off('load', onMapLoad);
    map?.off('click', 'region-fill', onRegionClick);
    map?.off('mousemove', 'region-fill', onMousemoveRegion);
    map?.off('mouseleave', 'region-fill', onMouseLeaveRegion);
    map?.off('resize', () => fitMapToBounds(map, store.appConfig?.maxBounds?.value));
    onWatchThemeMode();
  });

  function initialize() {
    if (!store.appConfig) return;
    map = new mapboxgl.Map({
      center: store.appConfig.center.value,
      container: mapContainer as HTMLDivElement,
      style: `mapbox://styles/mapbox/${mapStyle}`,
      maxBounds: MAP_BOUNDS
    });

    if (restrictMapBounds) {
      map.dragPan.disable();
      map.scrollZoom.disable();
    }

    map.on('load', onMapLoad);
    map.on('render', onMapResize);
    map.on('resize', () => fitMapToBounds(map, store.appConfig?.maxBounds?.value));
    if (onClick) map.on('click', 'region-fill', onRegionClick);
    map.on('mousemove', 'region-fill', onMousemoveRegion);
    map.on('mouseleave', 'region-fill', onMouseLeaveRegion);
  }

  function onMapResize() {
    map!.resize();
  }

  function onMapLoad() {
    if (!store.appConfig) return;
    addMapSource();

    if (showPlayback) {
      fetchRadarData();
    }

    addRegionTextSource();
    setRegionTextDataToSource();

    fitMapToBounds(map, store.appConfig.maxBounds?.value);

    // Layer Ordering (Important)
    addRegionFillLayer();
    addRegionBorderLayer();
    addRegionCentroidSource(); // always added

    if (showWindGustsOnLoad) addCentroidDisplayLayersToMap('regionWindSpeedTextLayer');
    if (showResourcesOnLoad) addCentroidDisplayLayersToMap('regionResourceCountTextLayer');
    if (showCentroids) addRegionTextLayer();

    if (showWindAnimation) fetchWeatherData();
    isMapLoaded = true;
  }

  function addMapSource() {
    if (!store.appConfig) return;
    map!.addSource(etrMapId, {
      promoteId: 'name',
      type: 'vector',
      url: store.appConfig.tilesetUrl.value
    });
  }

  function addRegionFillLayer() {
    const item = selectedConfig || config.find((i) => i.default);
    if (item) {
      attribute = item.attributeName as Visibility;
      if (!onLayerSelect) selectedConfig = item;
      map!.addLayer(getOutageFillLayer(item, etrMapId));
    }
  }

  function addRegionBorderLayer() {
    if (!store.appConfig) return;
    map!.addLayer({
      ...store.appConfig.regionBorderLayer,
      type: 'line',
      source: etrMapId,
      'source-layer': etrMapId
    });
  }

  function addRegionCentroidSource() {
    map!.addSource('region-centroids', {
      type: 'geojson',
      data: { type: 'FeatureCollection', features: [] },
      cluster: true,
      clusterRadius: 30,
      clusterProperties: {
        totalResources: ['+', ['get', 'totalResources']],
        wind20: ['+', ['get', 'wind20']],
        wind30: ['+', ['get', 'wind30']],
        wind40: ['+', ['get', 'wind40']]
      }
    });
  }

  function addCentroidDisplayLayers(
    configLayer: 'regionWindSpeedTextLayer' | 'regionResourceCountTextLayer'
  ) {
    if (!store.appConfig) return;
    map!.addLayer({
      ...store.appConfig.regionCircleLayer,
      type: 'circle'
    });
    map!.addLayer({
      ...store.appConfig[configLayer],
      type: 'symbol',
      layout: { ...store.appConfig[configLayer].layout }
    });
  }

  function setCentroidDataToSource() {
    const type = 'FeatureCollection';

    const regionsMap = store.regions.reduce(
      (acc, region) => {
        acc[region.displayName] = region;
        return acc;
      },
      {} as Record<string, Region>
    );

    const features = records.map((record) => {
      const region = regionsMap[record.region] ?? {};
      return {
        type: 'Feature' as const,
        geometry: { type: 'Point' as const, coordinates: region.centroid || [0, 0] },
        properties: {
          region: region.displayName,
          totalResources: record.totalResources ?? 0,
          wind20: record.windGustThresholds?.over20mph ?? 0,
          wind30: record.windGustThresholds?.over30mph ?? 0,
          wind40: record.windGustThresholds?.over40mph ?? 0
        }
      };
    });

    (map!.getSource('region-centroids') as GeoJSONSource)?.setData({ type, features });
  }

  function onChangeTheme() {
    addMapSource();
    addRegionBorderLayer();
    radarTileCache.clear();
    if (showPlayback && isRadarVisible) {
      apiData ? showFrame() : fetchRadarData();
    }

    addRegionTextSource();
    setRegionTextDataToSource();

    const item = config.find((i) => i.attributeName === attribute);
    if (item) {
      map!.addLayer(getOutageFillLayer(item, etrMapId));
    }

    onSetFeatureStates();
    addRegionCentroidSource();
    if (isWindVisible) addCentroidDisplayLayersToMap('regionWindSpeedTextLayer');
    if (isResourcesVisible) addCentroidDisplayLayersToMap('regionResourceCountTextLayer');
    if (showCentroids) addRegionTextLayer();
  }

  function onRegionClick(e: MapMouseEvent) {
    const { sourceLayer = '', state = {} } = e?.features?.[0] ?? {};

    if (selectedRegion) {
      map!.setFeatureState({ id: selectedRegion, source: etrMapId, sourceLayer }, { hover: false });
    }

    selectedRegion = e?.features?.[0]?.id ?? null;

    if (selectedRegion) {
      map!.setFeatureState({ id: selectedRegion, source: etrMapId, sourceLayer }, { hover: true });
    }

    onClick?.(state.region as string);
  }

  function changeMapStyle() {
    if (!map?.getStyle()?.sprite?.includes(`/${mapStyle}`)) {
      map!.once('style.load', onChangeTheme);
      map!.setStyle(`mapbox://styles/mapbox/${mapStyle}`);
    }
  }

  function onSetFeatureStates() {
    if (records.length === 0) {
      store.regions.forEach((region) => {
        mapFeatures.forEach((feature) => {
          map!.removeFeatureState(
            { id: region.name, source: etrMapId, sourceLayer: etrMapId },
            feature
          );
        });
      });
    }

    records.forEach((record: Record<string, any>) => {
      map!.setFeatureState(
        {
          id: formatRegionId(record.region),
          source: etrMapId,
          sourceLayer: etrMapId
        },
        {
          ...record
        }
      );
    });
  }

  function onUpdateRegionLayer() {
    if (!store.appConfig || !map) return;
    const item = config?.find((i) => i.attributeName === attribute);

    if (item) {
      map.removeLayer('region-fill');
      if (map.getLayer('region-text')) {
        map.removeLayer('region-text');
      }
      map.addLayer(getOutageFillLayer(item, etrMapId), store.appConfig.regionBorderLayer.id);
      selectedConfig = item;
      onLayerSelect?.(item);
      if (showCentroids) addRegionTextLayer();
    }
  }

  function onMousemoveRegion(e: any) {
    const { sourceLayer } = e.features[0];

    if (hoveredRegion) {
      map!.setFeatureState({ id: hoveredRegion, source: etrMapId, sourceLayer }, { hover: false });
    }
    hoveredRegion = e.features[0].id;

    if (hoveredRegion) {
      map!.setFeatureState({ id: hoveredRegion, source: etrMapId, sourceLayer }, { hover: true });
      map!.getCanvas().style.cursor = 'pointer';

      const hoveredRegionRecords = records?.find(
        (record: Record<string, any>) => formatRegionId(record.region) === hoveredRegion
      );

      if (hoveredRegionRecords) {
        if (customPopup) {
          customPopup(regionPopup, hoveredRegionRecords, map!);
        } else {
          updatePopupContent(hoveredRegionRecords);
        }

        regionPopup.setLngLat(e.lngLat);
      }
    }
  }

  function updatePopupContent(record: Record<string, any>) {
    regionPopup
      .setHTML(
        `<div class="popup-region-info">
            <p class="popup-feature-name">${record?.region}</p>
            <div class="info-row">
              <span class="label">${selectedConfig?.legendName}:</span>
              <span class="value">${formatNumbers(record?.[attribute as Visibility])}</span>
            </div>
          </div>`
      )
      .addTo(map!);
  }

  function onMouseLeaveRegion() {
    if (hoveredRegion) {
      map!.setFeatureState(
        { id: hoveredRegion, source: etrMapId, sourceLayer: etrMapId },
        { hover: false }
      );
    }
    hoveredRegion = null;

    map!.getCanvas().style.cursor = '';
    regionPopup.remove();
  }

  function fetchRadarData() {
    fetch(DEFAULT_RADAR_CONFIG.radarUrl)
      .then((response) => {
        if (!response.ok) {
          throw new Error('Network response error');
        }
        return response.json();
      })
      .then((data) => {
        if (data) {
          apiData = data;
          mapFrames = [...(data.radar?.past ?? [])];
          frameIndex = data?.radar?.past?.length - 1;
          showFrame();
        }
      })
      .catch(() => {
        console.error('Failed to fetch radar data');
      });
  }

  function updateSystemRecord(currentRadarTimestamp: number) {
    const closestTimestamp = getClosestTimestamp(systemOverviewData, currentRadarTimestamp);

    if (closestTimestamp) {
      records = systemOverviewData?.[closestTimestamp].filter(({ region }) => region !== 'System');
    }
  }

  function showFrame() {
    addRadarLayer(mapFrames[frameIndex].path);

    // show next frame with preloaded data
    if (frameIndex + 1 < mapFrames.length) {
      addRadarLayer(mapFrames[frameIndex + 1].path, true);
    }
  }

  function addRadarLayer(path: string, preload: boolean = false) {
    if (apiData && !radarTileCache.has(path)) {
      const url = `${apiData.host}${path}/256/{z}/{x}/{y}/${colorScheme}/1_1.png`;
      radarTileCache.add(path);
      map!.addSource(path, {
        type: 'raster',
        tiles: [url]
      });
      map!.addLayer({
        id: path,
        type: 'raster',
        source: path,
        paint: {
          'raster-opacity': preload ? 0 : opacity
        }
      });
    }
  }

  function updateRadarPosition(index: number, previousIndex: number) {
    if (radarTileCache.size === 0) return;

    if (radarTileCache.has(mapFrames[previousIndex].path)) {
      map!.setPaintProperty(mapFrames[previousIndex].path, 'raster-opacity', 0);
    }
    if (radarTileCache.has(mapFrames[index].path)) {
      map!.setPaintProperty(mapFrames[index].path, 'raster-opacity', opacity);
    }

    frameIndex = index;

    showFrame();
  }

  function onUpdateRadarLayer() {
    if (radarTileCache.size === 0) {
      showFrame();
      return;
    }

    if (isRadarVisible) {
      map!.setPaintProperty(mapFrames[frameIndex].path, 'raster-opacity', opacity);
      updateRadarPosition(frameIndex, (frameIndex - 1 + mapFrames.length) % mapFrames.length);
    } else {
      map!.setPaintProperty(mapFrames[frameIndex].path, 'raster-opacity', 0);
    }
  }

  function updateLayer(layer: Visibility) {
    attribute = layer;
    onUpdateRegionLayer();
  }

  function updateWindDisplayText() {
    const value = ['get', `wind${windSpeed}`] as ExpressionSpecification;
    map!.setLayoutProperty(REGION_CIRCLE_TEXT_LAYER_ID, 'text-field', value);
  }

  function updateMapSegment() {
    if (store.activeMapRegion && map) {
      const { region, layer, value } = store.activeMapRegion ?? {};

      if (activeRegion) {
        map.setFeatureState(
          { id: activeRegion, source: etrMapId, sourceLayer: etrMapId },
          { hover: false }
        );
      }

      if (region) {
        activeRegion = formatRegionId(region);
        map.setFeatureState(
          { id: activeRegion, source: etrMapId, sourceLayer: etrMapId },
          { hover: true }
        );
      }

      if (layer && attribute !== layer) {
        updateLayer(layer);
      }

      const centroid = store.regions.find((i) => i.displayName === region)?.centroid;

      if (centroid) {
        regionPopup
          .setLngLat(centroid as [number, number])
          .setHTML(
            `<div class="popup-region-info">
              <p class="popup-feature-name">${region}</p>
              <div class="info-row">
                <span class="label">${selectedConfig?.legendName}:</span>
                <span class="value">${formatNumbers(value)}</span>
              </div>
            </div>`
          )
          .addTo(map);
      }
    }
  }

  function clearMapSegment() {
    if (activeRegion) {
      map!.setFeatureState(
        { id: activeRegion, source: etrMapId, sourceLayer: etrMapId },
        { hover: false }
      );
      regionPopup.remove();
      activeRegion = null;
    }
  }

  function onIncrementStep() {
    frameIndex = frameIndex < mapFrames.length - 1 ? frameIndex + 1 : 0;
  }

  function onDecrementStep() {
    frameIndex = frameIndex > 0 ? frameIndex - 1 : mapFrames.length - 1;
  }

  function onRewindRadar() {
    onDecrementStep();
    updateRadarPosition(frameIndex, (frameIndex + 1) % mapFrames.length);
  }

  function onForwardRadar() {
    onIncrementStep();
    updateRadarPosition(frameIndex, (frameIndex - 1 + mapFrames.length) % mapFrames.length);
  }

  function onUpdateColor(value: string) {
    colorScheme = value;
    mapFrames.forEach((i) => {
      if (map!.getSource(i.path)) {
        map!.removeLayer(i.path);
        map!.removeSource(i.path);
      }
    });
    radarTileCache.clear();
    showFrame();
  }

  function addCentroidDisplayLayersToMap(
    configLayer: 'regionWindSpeedTextLayer' | 'regionResourceCountTextLayer'
  ) {
    if (showCentroids && map!.getLayer('region-text')) {
      showCentroids = false;
      map!.removeLayer('region-text');
    }

    addCentroidDisplayLayers(configLayer);
    setCentroidDataToSource();
  }

  function removeCentroidTextLayersFromMap() {
    if (map!.getLayer('region-circle')) {
      map!.removeLayer('region-circle');
    }

    if (map!.getLayer(REGION_CIRCLE_TEXT_LAYER_ID)) {
      map!.removeLayer(REGION_CIRCLE_TEXT_LAYER_ID);
    }
  }

  async function fetchWeatherData() {
    if (weatherStations.length > 0 || !showEtrMapWindAnimation) return;
    isLoadingWeather = true;
    try {
      const weatherData = await getLatestWeatherData();
      weatherStations = Object.entries(weatherData.weatherStations).map(([id, station]) => ({
        id,
        ...station,
        windBearing: station.weatherAttributes.windBearingDeg,
        windSpeed: station.weatherAttributes.windSpeedMph
      }));
    } catch (error) {
      console.error('Failed to fetch weather data:', error);
    } finally {
      isLoadingWeather = false;
    }
  }

  function onToggleWindAnimation(value: boolean) {
    isWindAnimationVisible = value;
  }

  function addRegionTextSource() {
    map!.addSource('region-text-source', {
      type: 'geojson',
      data: { type: 'FeatureCollection', features: [] }
    });
  }

  function setRegionTextDataToSource() {
    if (!map!.getSource('region-text-source') || !store.regions.length) return;

    const centroidMap = store.regions.reduce(
      (acc, region) => {
        acc[region.name] = region.centroid;
        return acc;
      },
      {} as Record<string, [number, number] | undefined>
    );

    const features = records.map((record) => ({
      type: 'Feature' as const,
      properties: {
        region: formatRegionId(record.region),
        ...config.reduce(
          (acc, item: MapItem) => {
            acc[item.attributeName] = formatNumbers(record[item.attributeName]);
            return acc;
          },
          {} as Record<Visibility, string>
        )
      },
      geometry: {
        type: 'Point' as const,
        coordinates: centroidMap[formatRegionId(record.region)] || [0, 0]
      }
    }));

    (map!.getSource('region-text-source') as GeoJSONSource)?.setData({
      type: 'FeatureCollection',
      features
    });
  }

  function addRegionTextLayer() {
    if (!map!.getSource('region-text-source') || !attribute) return;

    if (isResourcesVisible || isWindVisible) {
      removeCentroidTextLayersFromMap();
      isResourcesVisible = false;
      isWindVisible = false;
    }

    map!.addLayer({
      id: 'region-text',
      type: 'symbol',
      source: 'region-text-source',
      layout: {
        'text-field': ['coalesce', ['get', attribute], '-'],
        'text-size': 12,
        'text-allow-overlap': true
      },
      paint: {
        'text-color': mapStyle === MapStyle.light || mapStyle === MapStyle.street ? '#222' : '#fff',
        'text-halo-blur': 4
      }
    });
  }
</script>

<div
  class="map relative w-full h-full rounded-md overflow-hidden border-solid border !border-borderColor shadow-md {className}"
>
  {#if isMapLoaded}
    {#if showPlayback}
      {#if isRadarVisible}
        <PlayPause
          currentFrameTime={mapFrames?.[frameIndex]?.time}
          onRewind={onRewindRadar}
          onForward={onForwardRadar}
          maxStep={mapFrames?.length - 1}
          index={frameIndex}
          onChange={(e) => {
            updateRadarPosition(e, frameIndex);
          }}
        />
      {:else}
        <PlayPause
          currentFrameTime={mapFrames?.[frameIndex]?.time}
          onRewind={onDecrementStep}
          onForward={onIncrementStep}
          maxStep={mapFrames?.length - 1}
          index={frameIndex}
        />
      {/if}
    {/if}

    <SettingsPane>
      <MapStyleToggle
        {mapStyle}
        onSelect={(value) => {
          mapStyle = value;
          changeMapStyle();
        }}
      />
    </SettingsPane>

    <LayersPane>
      <MapVisibilityToggle
        onSelect={updateLayer}
        visibility={attribute as Visibility}
        visibilityOptions={config}
        show={showCentroids}
        {source}
        onToggle={(value) => {
          showCentroids = value;
          if (showCentroids) {
            addRegionTextLayer();
            return;
          }

          if (map!.getLayer('region-text')) {
            map!.removeLayer('region-text');
          }
        }}
      />

      {#if showActiveResources && showResources}
        <ResourceToggle
          show={isResourcesVisible}
          onToggle={(value) => {
            isResourcesVisible = value;
            isResourcesVisible
              ? addCentroidDisplayLayersToMap('regionResourceCountTextLayer')
              : removeCentroidTextLayersFromMap();
          }}
        />
      {/if}

      {#if showMapWeatherComponents && showWindGusts}
        <WindToggle
          show={isWindVisible}
          {windSpeed}
          onRadioSelect={(value) => {
            windSpeed = value as WindSpeed;
            updateWindDisplayText();
          }}
          onToggle={(value) => {
            isWindVisible = value;
            isWindVisible
              ? addCentroidDisplayLayersToMap('regionWindSpeedTextLayer')
              : removeCentroidTextLayersFromMap();
          }}
        />
      {/if}

      {#if outagesSource.length}
        <OutagesSource
          {outagesSource}
          {source}
          onSelect={(value) => {
            source = value;
            if (source === OutageSource.raw) {
              updateLayer(
                attribute === Visibility.outages ? Visibility.rawOutages : Visibility.rawCustomers
              );
            } else {
              updateLayer(
                attribute === Visibility.rawOutages ? Visibility.outages : Visibility.customers
              );
            }
          }}
        />
      {/if}
    </LayersPane>

    {#if showWind || showPlayback}
      <WeatherPane>
        {#if showPlayback}
          <RadarToggle
            bind:show={isRadarVisible}
            onSelect={onUpdateRadarLayer}
            {colorScheme}
            bind:opacity
            {onUpdateColor}
            onSliderChange={() => {
              map!.setPaintProperty(mapFrames[frameIndex].path, 'raster-opacity', opacity);
            }}
          />
        {/if}
        {#if showWind}
          <WindAnimationToggle
            show={isWindAnimationVisible}
            {onToggleWindAnimation}
            bind:particleCount
            bind:particleSize
            bind:particleLifespan
            bind:speedFactor
            bind:overallOpacity
          />
        {/if}
      </WeatherPane>
    {/if}
  {/if}

  {#if typeof window !== 'undefined'}
    <div bind:this={mapContainer} class="mapbox-map w-full h-full absolute top-0 bottom-0"></div>
  {/if}
  {@render children?.()}

  {#if isWindAnimationVisible && map}
    <WindAnimation
      {map}
      {weatherStations}
      {particleCount}
      {particleSize}
      {particleLifespan}
      {speedFactor}
      {overallOpacity}
    />
  {/if}

  <MapLegend {map} {collapseLegend} />

  <div
    class="absolute inset-0 flex items-center rounded-md justify-center bg-component !w-full {loading
      ? 'flex'
      : 'hidden'}"
  >
    <Loader />
  </div>
</div>
