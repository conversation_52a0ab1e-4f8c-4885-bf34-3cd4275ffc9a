<script lang="ts">
  import { Slider, Toggle } from '$lib/components';
  import { slide } from 'svelte/transition';

  interface Props {
    overallOpacity: number;
    particleCount: number;
    particleLifespan: number;
    particleSize: number;
    show: boolean;
    speedFactor: number;
    onToggleWindAnimation: (value: boolean) => void;
  }

  let {
    overallOpacity = $bindable(),
    particleCount = $bindable(),
    particleLifespan = $bindable(),
    particleSize = $bindable(),
    show,
    speedFactor = $bindable(),
    onToggleWindAnimation
  }: Props = $props();

  const sliderConfig = {
    particleCount: { min: 100, max: 10000, step: 100 },
    particleSize: { min: 0.1, max: 5.0, step: 0.1 },
    particleLifespan: { min: 50, max: 3000, step: 10 },
    speedFactor: { min: 0.001, max: 0.25, step: 0.001 },
    overallOpacity: { min: 0, max: 1, step: 0.01 }
  };
</script>

<div class="flex justify-between gap-md">
  <div class="flex gap-sm items-center">
    <span class="material-icons !text-sm">air</span>
    <span>Wind</span>
  </div>
  <Toggle size="sm" checked={show} onToggle={onToggleWindAnimation} />
</div>

{#if show}
  <div
    transition:slide
    class="flex flex-col gap-sm ml-md bg-muted/50 p-sm rounded-md border border-border"
  >
    <Slider
      label="Particle Count ({particleCount})"
      max={sliderConfig.particleCount.max}
      maxLabel={sliderConfig.particleCount.max}
      min={sliderConfig.particleCount.min}
      minLabel={sliderConfig.particleCount.min}
      step={sliderConfig.particleCount.step}
      bind:value={particleCount}
    />

    <Slider
      label="Particle Size ({particleSize.toFixed(1)})"
      max={sliderConfig.particleSize.max}
      maxLabel={sliderConfig.particleSize.max}
      min={sliderConfig.particleSize.min}
      minLabel={sliderConfig.particleSize.min}
      step={sliderConfig.particleSize.step}
      bind:value={particleSize}
    />

    <Slider
      label="Lifespan Size ({particleLifespan})"
      max={sliderConfig.particleLifespan.max}
      maxLabel={sliderConfig.particleLifespan.max}
      min={sliderConfig.particleLifespan.min}
      minLabel={sliderConfig.particleLifespan.min}
      step={sliderConfig.particleLifespan.step}
      bind:value={particleLifespan}
    />

    <Slider
      label="Speed Factor ({speedFactor.toFixed(2)})"
      max={sliderConfig.speedFactor.max}
      maxLabel={sliderConfig.speedFactor.max}
      min={sliderConfig.speedFactor.min}
      minLabel={sliderConfig.speedFactor.min}
      step={sliderConfig.speedFactor.step}
      bind:value={speedFactor}
    />

    <Slider
      label="Opacity ({(overallOpacity * 100).toFixed(0)})"
      max={sliderConfig.overallOpacity.max}
      maxLabel={sliderConfig.overallOpacity.max * 100}
      min={sliderConfig.overallOpacity.min}
      minLabel={sliderConfig.overallOpacity.min}
      step={sliderConfig.overallOpacity.step}
      bind:value={overallOpacity}
    />
  </div>
{/if}
