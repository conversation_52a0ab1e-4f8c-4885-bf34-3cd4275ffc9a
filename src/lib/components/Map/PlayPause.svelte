<script lang="ts">
  import { onDestroy } from 'svelte';
  import { <PERSON>lider } from '$lib/components';
  import * as Select from '$lib/components/ui/select';
  import { Skeleton } from '$lib/components/ui/skeleton';
  import { Button } from '$lib/components/ui/button';
  import { formatDateTime } from '$lib/config/utils';

  /* eslint-disable @typescript-eslint/no-magic-numbers */
  const SPEED_FACTORS = ['1', '2', '5', '10', '30'];
  const BASE_SPEED = 1000;
  let speedInterval = 1000;

  interface Props {
    currentFrameTime?: number | null;
    disablePlayback?: boolean;
    index?: number;
    maxStep?: number;
    onChange?: (value: number) => void;
    onForward: () => void;
    onRewind: () => void;
  }

  let {
    currentFrameTime = null,
    disablePlayback = false,
    index = $bindable(0),
    maxStep = 0,
    onChange = () => {},
    onForward,
    onRewind
  }: Props = $props();

  let animationTimer: ReturnType<typeof setInterval> | null = null;

  let isPlaying = $state<boolean>(false);
  let selectedSpeed = $state<string>(SPEED_FACTORS[0]);

  let formattedFrameTime = $derived(
    currentFrameTime ? formatDateTime(new Date(currentFrameTime * 1000)) : '-'
  );

  function togglePlay() {
    isPlaying = !isPlaying;

    if (isPlaying) {
      startAnimation();
    } else {
      stopAnimation();
    }
  }

  function startAnimation() {
    if (animationTimer) {
      clearInterval(animationTimer);
    }
    animationTimer = setInterval(() => {
      onForward();
    }, speedInterval);
  }

  function stopAnimation() {
    clearInterval(animationTimer!);
    animationTimer = null;
  }

  function increaseSpeed(factor: string) {
    speedInterval = BASE_SPEED / Number(factor);

    if (isPlaying) {
      startAnimation();
    }
  }

  onDestroy(() => {
    if (animationTimer) {
      stopAnimation();
    }
  });
</script>

<div class="play-pause">
  <div class="flex w-full flex-wrap justify-center items-center gap-sm">
    <div class="mt-1">
      <Button
        disabled={disablePlayback && index === 0}
        onclick={onRewind}
        size="xs"
        variant="ghost"
      >
        <span class="material-icons">fast_rewind</span>
      </Button>
      <Button onclick={togglePlay} size="xs" variant="ghost">
        {#if isPlaying}
          <span class="material-icons">pause</span>
        {:else}
          <span class="material-icons">play_arrow</span>
        {/if}
      </Button>
      <Button onclick={onForward} size="xs" variant="ghost">
        <span class="material-icons">fast_forward</span>
      </Button>
    </div>
    {#if currentFrameTime}
      <span class="text-xs text-center text-accentText w-[140px]">
        {formattedFrameTime}
      </span>
      {#if disablePlayback}
        <Button size="xs" variant="ghost" disabled={index === 0} onclick={() => (index = 0)}>
          <span class="material-icons">restart_alt</span>
        </Button>
      {/if}
      <Select.Root type="single" bind:value={selectedSpeed} onValueChange={increaseSpeed}>
        <Select.Trigger class="w-[60px] h-[28px] bg-inherit">
          {selectedSpeed}x
        </Select.Trigger>
        <Select.Content>
          {#each SPEED_FACTORS as factor}
            <Select.Item value={factor}>{factor}x</Select.Item>
          {/each}
        </Select.Content>
      </Select.Root>
    {:else}
      <div class="flex gap-md items-center mt-sm">
        <Skeleton class="w-[150px]" />
        <Skeleton class="w-[30px]" />
      </div>
    {/if}
  </div>

  <Slider
    containerClass="!w-[85%]"
    disabled={disablePlayback}
    max={Math.max(maxStep, 0)}
    maxLabel="End"
    min={0}
    minLabel="Start"
    step={1}
    bind:value={index}
    {onChange}
  />
</div>

<style>
  .play-pause {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: absolute;
    bottom: 8px;
    left: 50%;
    gap: 8px;
    transform: translateX(-50%);
    z-index: 3;
    background: var(--color-component);
    width: 370px;
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    padding: 0.5rem 1rem;
    justify-content: center;
  }

  :global(.play-pause .material-icons) {
    font-size: 16px;
  }
</style>
