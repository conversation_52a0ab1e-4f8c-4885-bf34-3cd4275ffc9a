<script lang="ts">
  import { onDestroy, onMount } from 'svelte';
  import { Loader, MapStyleToggle } from '$lib/components';
  import {
    DEFAULT_RADAR_CONFIG,
    MAP_BOUNDS,
    formatRegionId,
    getOutageFillLayer
  } from '$lib/config/map';
  import { isDarkMode } from '$lib/stores/theme';
  import store from '$lib/stores/app.svelte';
  import { isSystemic } from '$lib/stores/constants';
  import {
    MapStyle,
    type MapItem,
    type Visibility,
    type Region,
    type RegionData,
    type WeatherData,
    type RadarData
  } from '$lib/types';
  import { fitMapToBounds, formatNumbers } from '$lib/utils';
  import mapboxgl, { type GeoJSONSource, type Map, type MapMouseEvent } from 'mapbox-gl';
  import MapVisibilityToggle from './MapVisibilityToggle.svelte';
  import SettingsPane from './SettingsPane.svelte';
  import MapLegend from './MapLegend.svelte';
  import LayersPane from './LayersPane.svelte';
  import type { Snippet } from 'svelte';
  import WeatherPane from './WeatherPane.svelte';
  import RadarToggle from './RadarToggle.svelte';

  const SYSTEM_VIEW = isSystemic ? 'regions' : 'larger-regions';
  const REGION_VIEW = 'regions';
  const ZOOM_FACTOR = 1.1;

  const regionPopup = new mapboxgl.Popup({
    closeButton: false,
    closeOnClick: false
  });

  interface Props {
    loading?: boolean;
    records?: RegionData[];
    regionConfig?: MapItem[];
    selectedConfig?: MapItem | null;
    selectedRegion: Region;
    systemConfig?: MapItem[];
    onRegionClick: (e: MapMouseEvent) => void;
    children?: Snippet;
  }

  let {
    loading = false,
    records = [],
    regionConfig = [],
    selectedConfig = $bindable(null),
    selectedRegion,
    systemConfig = [],
    onRegionClick,
    children
  }: Props = $props();

  let mapContainer = $state<HTMLDivElement | undefined>();
  let map = $state<Map | undefined>();
  let hoveredRegion: string | null = null;
  let activeRegion: string | null = $state(null);
  let isMapLoaded = $state(false);
  let mapStyle: MapStyle = $state($isDarkMode ? MapStyle.dark : MapStyle.light);
  let attribute = $state<Visibility | undefined>();
  let showCentroids = $state(false);
  let initialZoom: number | null = null;

  let mapId = $derived(selectedRegion.name === 'system' ? SYSTEM_VIEW : REGION_VIEW);
  let config = $derived(selectedRegion.name === 'system' ? systemConfig : regionConfig);
  let regionNames = $derived(records.map((record) => formatRegionId(record.region)));

  let isRadarVisible: boolean = $state(true);
  let colorScheme: string = $state(DEFAULT_RADAR_CONFIG.colorScheme);
  let radarData: WeatherData | null = $state(null);
  let mapFrames: RadarData[] = $state([]);
  let frameIndex: number = $state(0);
  let radarOpacity = $state(DEFAULT_RADAR_CONFIG.opacity);

  $effect(() => {
    if (store.activeMapRegion?.key) {
      updateMapSegment();
    }
  });

  $effect(() => {
    if (activeRegion && !store.activeMapRegion?.key) {
      clearMapSegment();
    }
  });

  $effect(() => {
    if (!map || !isMapLoaded || !records.length) return;

    if (map.getSource(mapId)) {
      onSetFeatureStates();
      setRegionTextDataToSource();
    } else {
      map.once('sourcedata', () => {
        onSetFeatureStates();
        setRegionTextDataToSource();
      });
    }
  });

  $effect(() => {
    if (regionNames.length && isMapLoaded) {
      addRegionFillLayer();
      addRegionBorderLayer();
    }
  });

  $effect(() => {
    if (map && selectedRegion) {
      onChangeViewLevel();
    }
  });

  const onWatchThemeMode = isDarkMode.subscribe((isDark) => {
    if (map && isMapLoaded) {
      mapStyle = isDark ? MapStyle.dark : MapStyle.light;
      changeMapStyle();
    }
  });

  onMount(() => {
    initialize();
  });

  onDestroy(() => {
    map?.off('load', onMapLoad);
    map?.off('mousemove', 'region-fill', onMousemoveRegion);
    map?.off('mouseleave', 'region-fill', onMouseLeaveRegion);
    map?.off('click', 'region-fill', onRegionClick);
    map?.off('resize', () => fitMapToBounds(map, store.appConfig?.maxBounds?.value));
    onWatchThemeMode();
  });

  function initialize() {
    if (!store.appConfig) return;
    map = new mapboxgl.Map({
      center: store.appConfig.center.value,
      container: mapContainer as HTMLDivElement,
      style: `mapbox://styles/mapbox/${mapStyle}`,
      maxBounds: MAP_BOUNDS
    });
    map.on('load', onMapLoad);
    map.on('render', onMapResize);
    map.on('resize', () => fitMapToBounds(map, store.appConfig?.maxBounds?.value));
    map.on('mousemove', 'region-fill', onMousemoveRegion);
    map.on('mouseleave', 'region-fill', onMouseLeaveRegion);
    map.on('click', 'region-fill', onRegionClick);
  }

  function onMapResize() {
    map!.resize();
  }

  function onMapLoad() {
    if (!store.appConfig) return;
    if (mapId) {
      map!.addSource(mapId, {
        promoteId: 'name',
        type: 'vector',
        url: store.appConfig.tilesetUrl.value
      });
    }

    addRegionTextSource();
    setRegionTextDataToSource();
    fitMapToBounds(map, store.appConfig.maxBounds?.value);

    // Layer Ordering (Important)
    addRegionFillLayer();
    addRegionBorderLayer();
    addStateFillLayer();

    fetchRadarData();

    if (showCentroids) {
      addRegionTextLayer();
    }

    map!.once('idle', () => (initialZoom = map!.getZoom()));
    isMapLoaded = true;
  }

  function addRegionFillLayer() {
    const item = config.find((i) => i.default);
    if (item) {
      attribute = item.attributeName as Visibility;
      selectedConfig = item;
      addOutageFillLayer(item);
    }
  }

  function addRegionBorderLayer() {
    if (!map!.getSource(mapId) || !store.appConfig) return;

    if (map!.getLayer('region-borders')) {
      map!.removeLayer('region-borders');
    }

    map!.addLayer({
      ...store.appConfig.regionBorderLayer,
      type: 'line',
      source: mapId,
      'source-layer': mapId,
      ...(isSystemic && { filter: ['in', ['get', 'name'], ['literal', regionNames]] })
    });
  }

  function onChangeTheme() {
    if (!store.appConfig) return;
    if (mapId) {
      map!.addSource(mapId, {
        promoteId: 'name',
        type: 'vector',
        url: store.appConfig.tilesetUrl.value
      });
    }
    addRegionBorderLayer();
    addStateFillLayer();

    addRegionTextSource();
    setRegionTextDataToSource();

    if (isRadarVisible) {
      radarData ? showFrame() : fetchRadarData();
    }

    if (showCentroids) {
      addRegionTextLayer();
    }

    const item = config.find((i) => i.attributeName === attribute);
    if (item) {
      addOutageFillLayer(item);
    }

    onSetFeatureStates();
  }

  function addOutageFillLayer(item: MapItem) {
    if (!map!.getSource(mapId)) return;

    if (map!.getLayer('region-fill')) {
      map!.removeLayer('region-fill');
    }

    map!.addLayer({
      ...getOutageFillLayer(item, mapId),
      ...(isSystemic && { filter: ['in', ['get', 'name'], ['literal', regionNames]] })
    });
  }

  function changeMapStyle() {
    if (!map!.getStyle()?.sprite?.includes(`/${mapStyle}`)) {
      map!.once('style.load', onChangeTheme);
      map!.setStyle(`mapbox://styles/mapbox/${mapStyle}`);
    }
  }

  function onSetFeatureStates() {
    clearStaleFeatureStates();
    records.forEach((record: Record<string, any>) => {
      map!.setFeatureState(
        {
          id: formatRegionId(record.region),
          source: mapId,
          sourceLayer: mapId
        },
        {
          ...record
        }
      );
    });
  }

  function clearStaleFeatureStates() {
    if (!map?.getSource(mapId)) {
      map?.once('sourcedata', () => map!.removeFeatureState({ source: mapId, sourceLayer: mapId }));
    } else {
      map!.removeFeatureState({ source: mapId, sourceLayer: mapId });
    }
  }

  function onUpdateRegionLayer() {
    if (!store.appConfig) return;
    const item = config?.find((i) => i.attributeName === attribute);

    if (item) {
      if (map!.getLayer('region-fill')) {
        map!.removeLayer('region-fill');
      }

      if (map!.getLayer('region-text')) {
        map!.removeLayer('region-text');
      }

      addOutageFillLayer(item);
      selectedConfig = item;
      if (showCentroids) {
        addRegionTextLayer();
      }
    }
  }

  function onMousemoveRegion(e: any) {
    if (map!.getSource(mapId)) {
      const { sourceLayer } = e.features[0];

      if (hoveredRegion) {
        map!.setFeatureState({ id: hoveredRegion, source: mapId, sourceLayer }, { hover: false });
      }
      hoveredRegion = e.features[0].id;

      if (hoveredRegion) {
        map!.setFeatureState({ id: hoveredRegion, source: mapId, sourceLayer }, { hover: true });
        map!.getCanvas().style.cursor = 'pointer';

        const hoveredRegionRecords = records?.find(
          (record: Record<string, any>) => formatRegionId(record.region) === hoveredRegion
        );

        if (hoveredRegionRecords) {
          updatePopupContent(hoveredRegionRecords);
          regionPopup.setLngLat(e.lngLat);
        }
      }
    }
  }

  function updatePopupContent(record: Record<string, any>) {
    const attr = attribute as Visibility;
    regionPopup
      .setHTML(
        `<div class="popup-region-info">
            <p class="popup-feature-name">${record?.region}</p>
            ${
              typeof record?.[attribute as Visibility] === 'string'
                ? `
                  <div class="info-row">
                    <span class="label">Threat Level:</span>
                    <span class="value">${typeof record?.[attr] === 'number' ? formatNumbers(record?.[attr]) : record?.[attr]}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">Forecasted Incidents:</span>
                    <span class="value">${formatNumbers(record?.cumulativeOutages)}</span>
                  </div>`
                : `<div class="info-row">
                    <span class="label">${selectedConfig?.legendName}:</span>
                    <span class="value">${formatNumbers(record?.[attr])}</span>
                  </div>`
            }
          </div>`
      )
      .addTo(map!);
  }

  function onMouseLeaveRegion() {
    if (map!.getSource(mapId)) {
      if (hoveredRegion) {
        map!.setFeatureState(
          { id: hoveredRegion, source: mapId, sourceLayer: mapId },
          { hover: false }
        );
      }
      hoveredRegion = null;

      map!.getCanvas().style.cursor = '';
      regionPopup.remove();
    }
  }

  function updateLayer(layer: Visibility) {
    attribute = layer;
    onUpdateRegionLayer();
  }

  function updateMapSegment() {
    if (store.activeMapRegion && map) {
      const { region, layer, value } = store.activeMapRegion ?? {};

      if (activeRegion) {
        map.setFeatureState(
          { id: activeRegion, source: mapId, sourceLayer: mapId },
          { hover: false }
        );
      }

      if (region) {
        activeRegion = formatRegionId(region);
        map.setFeatureState(
          { id: activeRegion, source: mapId, sourceLayer: mapId },
          { hover: true }
        );
      }

      if (layer && attribute !== layer) {
        updateLayer(layer);
      }

      const centroid = store.regions.find((i) => i.displayName === region)?.centroid;

      if (centroid) {
        regionPopup
          .setLngLat(centroid as [number, number])
          .setHTML(
            `<div class="popup-region-info">
              <p class="popup-feature-name">${region}</p>
              <div class="info-row">
                <span class="label">${selectedConfig?.legendName}:</span>
                <span class="value">${formatNumbers(value)}</span>
              </div>
            </div>`
          )
          .addTo(map);
      }
    }
  }

  function clearMapSegment() {
    if (activeRegion) {
      map!.setFeatureState(
        { id: activeRegion, source: mapId, sourceLayer: mapId },
        { hover: false }
      );
      regionPopup.remove();
      activeRegion = null;
    }
  }

  function onChangeViewLevel() {
    if (!map || !selectedRegion || !initialZoom) return;

    const { centroid, name } = selectedRegion;

    map.flyTo({
      center: centroid,
      duration: 1000,
      essential: true,
      zoom: name === 'system' ? initialZoom : initialZoom * ZOOM_FACTOR
    });

    applyViewLevelChanges();
  }

  function applyViewLevelChanges() {
    if (!store.appConfig || !map) return;

    const prevSource = mapId === REGION_VIEW ? SYSTEM_VIEW : REGION_VIEW;

    if (map.getSource(prevSource)) {
      ['region-fill', 'region-borders', 'region-text'].forEach((layerId) => {
        if (map?.getLayer(layerId)) map?.removeLayer(layerId);
      });
      map.removeSource(prevSource);
    }

    if (!map.getSource(mapId)) {
      map.addSource(mapId, {
        promoteId: 'name',
        type: 'vector',
        url: store.appConfig.tilesetUrl.value
      });
    }

    map!.once('sourcedata', () => {
      addRegionFillLayer();
      addRegionBorderLayer();
      addStateFillLayer();
      if (showCentroids) {
        addRegionTextLayer();
      }
    });
  }

  function addRegionTextSource() {
    map!.addSource('region-text-source', {
      type: 'geojson',
      data: { type: 'FeatureCollection', features: [] }
    });
  }

  function addStateFillLayer() {
    if (!store.appConfig?.stateFillLayer) return;

    map!.addLayer({ ...store.appConfig.stateFillLayer, type: 'fill' });
  }

  function setRegionTextDataToSource() {
    if (!map!.getSource('region-text-source') || !store.regions.length) return;

    const centroidMap = store.regions.reduce(
      (acc, region) => {
        acc[region.name] = region.centroid;
        return acc;
      },
      {} as Record<string, [number, number] | undefined>
    );

    const features = records.map((record) => ({
      type: 'Feature' as const,
      properties: {
        region: formatRegionId(record.region),
        threatLevel: formatNumbers(record.cumulativeOutages),
        cumulativeOutages: formatNumbers(record.cumulativeOutages),
        cumulativeCustomersAffected: formatNumbers(record.cumulativeCustomersAffected)
      },
      geometry: {
        type: 'Point' as const,
        coordinates: centroidMap[formatRegionId(record.region)] || [0, 0]
      }
    }));

    (map!.getSource('region-text-source') as GeoJSONSource)?.setData({
      type: 'FeatureCollection',
      features
    });
  }

  function addRegionTextLayer() {
    if (!map!.getSource('region-text-source') || !attribute) return;

    map!.addLayer({
      id: 'region-text',
      type: 'symbol',
      source: 'region-text-source',
      layout: {
        'text-field': ['coalesce', ['get', attribute], '-'],
        'text-size': 12,
        'text-allow-overlap': true
      },
      paint: {
        'text-color': mapStyle === MapStyle.light || mapStyle === MapStyle.street ? '#222' : '#fff',
        'text-halo-blur': 4
      }
    });
  }

  function showFrame() {
    addRadarLayer(mapFrames[frameIndex].path);
  }

  function fetchRadarData() {
    fetch(DEFAULT_RADAR_CONFIG.radarUrl)
      .then((response) => {
        if (!response.ok) {
          throw new Error('Network response error');
        }
        return response.json();
      })
      .then((data) => {
        if (data) {
          radarData = data;
          mapFrames = [...(data.radar?.past ?? [])];
          frameIndex = data?.radar?.past?.length - 1;
          showFrame();
        }
      })
      .catch(() => {
        console.error('Failed to fetch radar data');
      });
  }

  function addRadarLayer(path: string) {
    if (radarData) {
      const url = `${radarData.host}${path}/256/{z}/{x}/{y}/${colorScheme}/1_1.png`;
      map?.addSource(path, {
        type: 'raster',
        tiles: [url]
      });

      map?.addLayer({
        id: path,
        type: 'raster',
        source: path,
        paint: {
          'raster-opacity': isRadarVisible ? radarOpacity : 0
        }
      });
    }
  }

  function onUpdateRadarLayer() {
    if (isRadarVisible) {
      map?.setPaintProperty(mapFrames[frameIndex].path, 'raster-opacity', radarOpacity);
    } else {
      map?.setPaintProperty(mapFrames[frameIndex].path, 'raster-opacity', 0);
    }
  }

  function onUpdateColor(value: string) {
    colorScheme = value;
    mapFrames.forEach((i) => {
      if (map?.getSource(i.path)) {
        map.removeLayer(i.path);
        map.removeSource(i.path);
      }
    });
    showFrame();
  }
</script>

<div class="map relative w-full h-full">
  {#if isMapLoaded}
    <SettingsPane>
      <MapStyleToggle
        {mapStyle}
        onSelect={(value) => {
          mapStyle = value;
          changeMapStyle();
        }}
      />
    </SettingsPane>

    <LayersPane>
      <MapVisibilityToggle
        onSelect={updateLayer}
        visibility={attribute as Visibility}
        visibilityOptions={config}
        show={showCentroids}
        onToggle={(value) => {
          showCentroids = value;
          showCentroids ? addRegionTextLayer() : map!.removeLayer('region-text');
        }}
      />
    </LayersPane>
    <WeatherPane>
      <RadarToggle
        bind:show={isRadarVisible}
        onSelect={onUpdateRadarLayer}
        {colorScheme}
        bind:opacity={radarOpacity}
        {onUpdateColor}
        onSliderChange={() => {
          map?.setPaintProperty(mapFrames[frameIndex].path, 'raster-opacity', radarOpacity);
        }}
      />
    </WeatherPane>
  {/if}

  <MapLegend {map} />

  {#if typeof window !== 'undefined'}
    <div bind:this={mapContainer} class="mapbox-map w-full h-full absolute top-0 bottom-0"></div>
  {/if}
  {@render children?.()}

  <div
    class="absolute inset-0 flex items-center rounded-md justify-center bg-component !w-full {loading
      ? 'flex'
      : 'hidden'}"
  >
    <Loader />
  </div>
</div>
