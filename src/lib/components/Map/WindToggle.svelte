<script lang="ts">
  import { slide } from 'svelte/transition';
  import { RadioButtonItem, Toggle } from '$lib/components';
  import type { WindSpeed } from '$lib/types';
  import * as RadioGroup from '$lib/components/ui/radio-group';

  interface Props {
    show: boolean;
    windSpeed: WindSpeed;
    onRadioSelect: (value: string) => void;
    onToggle: (value: boolean) => void;
  }

  let { show, windSpeed, onRadioSelect, onToggle }: Props = $props();
</script>

<div class="flex justify-between">
  <div class="flex gap-sm items-center">
    <span class="material-icons !text-sm">air</span>
    <span>Wind</span>
  </div>
  <Toggle size="sm" checked={show} {onToggle} />
</div>

{#if show}
  <div
    transition:slide
    class="flex-col items-center gap-sm ml-md bg-muted/50 p-sm rounded-md border border-border"
  >
    <RadioGroup.Root value={windSpeed} onValueChange={onRadioSelect}>
      <RadioButtonItem label=">20 mph" value="20" />
      <RadioButtonItem label=">30 mph" value="30" />
      <RadioButtonItem label=">40 mph" value="40" />
    </RadioGroup.Root>
  </div>
{/if}
