<script lang="ts">
  import { Slider, Toggle } from '$lib/components';
  import { slide } from 'svelte/transition';
  import * as Select from '$lib/components/ui/select';

  const OPACITY_STEP = 0.05;
  const COLOR_SCHEME_ITEMS = [
    { label: 'Original', value: '1' },
    { label: 'Universal Blue', value: '2' },
    { label: 'TITAN', value: '3' },
    { label: 'The Weather Channel', value: '4' },
    { label: 'Meteored', value: '5' },
    { label: 'NEXRAD Level III', value: '6' },
    { label: 'Rainbow @ SELEX-IS', value: '7' },
    { label: 'Dark Sky', value: '8' }
  ];

  interface Props {
    colorScheme: string;
    opacity: number;
    show: boolean;
    onSelect: (value: boolean) => void;
    onSliderChange: (value: number) => void;
    onUpdateColor: (value: string) => void;
  }

  let {
    colorScheme,
    opacity = $bindable(),
    show = $bindable(false),
    onSelect,
    onSliderChange,
    onUpdateColor
  }: Props = $props();
</script>

<div class="flex flex-col gap-sm">
  <div class="flex justify-between">
    <div class="flex gap-sm items-center">
      <span class="material-icons !text-sm">radar</span>
      <span>Radar</span>
    </div>
    <Toggle size="sm" bind:checked={show} onToggle={onSelect} />
  </div>

  {#if show}
    <div
      transition:slide
      class="flex flex-col gap-sm ml-md bg-muted/50 p-sm rounded-md border border-border"
    >
      <Slider
        label="Opacity ({(opacity * 100).toFixed(0)})"
        max={1}
        maxLabel="100"
        min={0}
        minLabel="0"
        step={OPACITY_STEP}
        bind:value={opacity}
        onChange={onSliderChange}
      />

      <div class="flex flex-col">
        <Select.Label class="text-xs mb-2">Color Scheme</Select.Label>
        <Select.Root type="single" value={colorScheme} onValueChange={onUpdateColor}>
          <Select.Trigger>
            {COLOR_SCHEME_ITEMS.find((i) => i.value === colorScheme)?.label}
          </Select.Trigger>
          <Select.Content>
            {#each COLOR_SCHEME_ITEMS as { label, value }}
              <Select.Item {value}>{label}</Select.Item>
            {/each}
          </Select.Content>
        </Select.Root>
      </div>
    </div>
  {/if}
</div>
