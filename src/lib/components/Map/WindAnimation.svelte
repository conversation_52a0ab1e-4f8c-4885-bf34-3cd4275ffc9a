<script lang="ts">
  import type { WeatherStation } from '$lib/types';
  import mapboxgl from 'mapbox-gl';
  import { onMount, onDestroy } from 'svelte';

  let BOUNDARY: {
    north: number;
    south: number;
    east: number;
    west: number;
  };

  type Particle = {
    x: number;
    y: number;
    age: number;
    opacity: number;
    lifespan: number;
    trail: { x: number; y: number; bearing: number; speed: number }[];
  };

  interface Props {
    map: mapboxgl.Map;
    overallOpacity: number;
    particleCount: number;
    particleLifespan: number;
    particleSize: number;
    speedFactor: number;
    weatherStations: WeatherStation[];
  }

  let {
    map,
    particleCount,
    particleLifespan,
    particleSize,
    overallOpacity,
    speedFactor,
    weatherStations
  }: Props = $props();

  let animationFrameId: number;
  let canvas: HTMLCanvasElement;
  let ctx: CanvasRenderingContext2D;
  let mapMoveTimeout: number;
  let particles: Particle[] = [];
  let speedGrid: number[][];
  let windGrid: number[][];

  let isInitialized = $state<boolean>(false);

  // Constants
  const INIT_RETRY_DELAY = 50;
  const WIND_ANGLE_ADJUSTMENT = 90;
  const GRID_SIZE = 20;
  const RADIANS_FACTOR = 180;
  const DEGREES_TO_RADIANS = Math.PI / RADIANS_FACTOR;
  const ARROW_ANGLE = 6;
  const NORTH = 90,
    EAST = 180,
    SOUTH = -90,
    WEST = -180;

  const EASE_MIDPOINT = 0.5;
  const EASE_MULTIPLIER = 2;
  const EASE_POWER = 2;

  // Configurable constants
  const MIN_SPEED_THRESHOLD = 0.0000000000001; // Minimum speed to consider an arrow moving
  const BOUNDARY_BUFFER = 1; // Buffer in degrees
  const BASE_INFLUENCE_RADIUS = 1; // Base influence radius
  const MAX_INFLUENCE_RADIUS = 2; // Maximum influence radius
  const WIND_SPEED_FACTOR = 0.05; // Factor to adjust influence based on wind speed

  const LIFESPAN_VARIATION = 0.5; // 50% variation in lifespan
  const DEBOUNCE_DELAY = 200;

  const EDGE_SOFTNESS = 0.2; // 20% of the map width/height for a wider soft edge
  const MIN_OPACITY = 0.1; // Minimum opacity at the very edge
  const FADE_IN_DURATION = 0.3; // 30% of lifespan for fade in
  const FADE_OUT_DURATION = 0.6;
  const BASE_OPACITY = 0.001; // Minimum opacity for visible arrows

  const ZOOM_INFLUENCE_FACTOR = 0.7;

  const MIN_LENGTH = 0.8; // Arrows
  const MAX_LENGTH = 10;
  const MIN_TRAIL_LENGTH = 1; // Minimum trail length
  const MAX_TRAIL_LENGTH = 35; // Maximum trail length

  const MIN_PARTICLE_LENGTH = particleSize * MIN_LENGTH; // Minimum length of a particle
  const MAX_PARTICLE_LENGTH = particleSize * MAX_LENGTH; // Maximum length of a particle
  const MAX_WIND_SPEED = Math.max(...weatherStations.map((station) => station.windSpeed));

  const ARROW_HISTORY = 5; // length of the arrow
  const TRAIL_LENGTH = 200; // Number of segments to store in history
  const ARROW_HEAD_FACTOR = 2;
  const ARROW_HEAD_ANGLE = Math.PI / ARROW_ANGLE; // 30 degrees

  /* eslint-disable */
  const WIND_SPEED_COLORS = [
    { speed: 1.5, color: [134, 163, 171] },
    { speed: 2.5, color: [126, 152, 188] },
    { speed: 4.12, color: [110, 143, 208] },
    { speed: 6.17, color: [15, 147, 167] },
    { speed: 9.26, color: [57, 163, 57] },
    { speed: 11.83, color: [194, 134, 62] },
    { speed: 14.92, color: [200, 66, 13] },
    { speed: 18.0, color: [210, 0, 50] },
    { speed: 21.6, color: [175, 80, 136] },
    { speed: 25.21, color: [117, 74, 147] },
    { speed: 29.32, color: [68, 105, 141] },
    { speed: 33.44, color: [194, 251, 119] },
    { speed: 43.72, color: [241, 255, 109] },
    { speed: 50.41, color: [255, 255, 255] },
    { speed: 59.16, color: [0, 255, 255] },
    { speed: 69.44, color: [255, 37, 255] }
  ] as const;

  $effect(() => {
    if (isInitialized && particleCount !== particles.length) {
      updateParticleCount();
    }
  });

  onMount(() => {
    calculateBoundary();
    initializeCanvas();
    if (map) {
      map.on('move', debounceReset);
      map.on('zoom', debounceReset);
      map.on('moveend', updateWindGrid);
      map.on('zoomend', updateWindGrid);
    }
  });

  onDestroy(() => {
    cancelAnimationFrame(animationFrameId);
    clearTimeout(mapMoveTimeout);
    if (map) {
      map.off('move', debounceReset);
      map.off('zoom', debounceReset);
      map.off('moveend', updateWindGrid);
      map.off('zoomend', updateWindGrid);
    }
  });

  function resetAnimation() {
    cancelAnimationFrame(animationFrameId);
    createParticles();
    animate();
  }

  function debounceReset() {
    clearTimeout(mapMoveTimeout);
    mapMoveTimeout = setTimeout(resetAnimation, DEBOUNCE_DELAY) as unknown as number;
  }

  function calculateBoundary() {
    const latitudes = weatherStations.map((station) => station.latitude);
    const longitudes = weatherStations.map((station) => station.longitude);

    BOUNDARY = {
      north: Math.min(NORTH, Math.max(...latitudes) + BOUNDARY_BUFFER),
      south: Math.max(SOUTH, Math.min(...latitudes) - BOUNDARY_BUFFER),
      east: Math.min(EAST, Math.max(...longitudes) + BOUNDARY_BUFFER),
      west: Math.max(WEST, Math.min(...longitudes) - BOUNDARY_BUFFER)
    };
  }

  function initializeCanvas() {
    if (!map || !map.getCanvas()) {
      setTimeout(initializeCanvas, INIT_RETRY_DELAY);
      return;
    }

    canvas.width = map.getCanvas().width;
    canvas.height = map.getCanvas().height;
    ctx = canvas.getContext('2d')!;

    updateWindGrid();
    createParticles();
    animate();
    isInitialized = true;
  }

  function updateWindGrid() {
    windGrid = Array.from({ length: GRID_SIZE }, () => new Array(GRID_SIZE).fill(0));
    speedGrid = Array.from({ length: GRID_SIZE }, () => new Array(GRID_SIZE).fill(0));

    const maxWindSpeed = Math.max(...weatherStations.map((station) => station.windSpeed));
    const currentZoom = map.getZoom();
    const zoomAdjustedMaxInfluence =
      MAX_INFLUENCE_RADIUS * (1 + (currentZoom - map.getMinZoom()) * ZOOM_INFLUENCE_FACTOR);

    for (let y = 0; y < GRID_SIZE; y++) {
      for (let x = 0; x < GRID_SIZE; x++) {
        let totalWeight = 0;
        let weightedBearing = 0;
        let weightedSpeed = 0;

        weatherStations.forEach((station) => {
          const stationPoint = map.project(
            new mapboxgl.LngLat(station.longitude, station.latitude)
          );
          const stationGridX = (stationPoint.x / canvas.width) * (GRID_SIZE - 1);
          const stationGridY = (stationPoint.y / canvas.height) * (GRID_SIZE - 1);

          const influenceRadius =
            BASE_INFLUENCE_RADIUS +
            (zoomAdjustedMaxInfluence - BASE_INFLUENCE_RADIUS) *
              (station.windSpeed / maxWindSpeed) *
              WIND_SPEED_FACTOR;

          const distance = Math.sqrt(Math.pow(x - stationGridX, 2) + Math.pow(y - stationGridY, 2));
          const weight = Math.max(0, (influenceRadius - distance) / influenceRadius);

          if (weight > 0) {
            totalWeight += weight;
            weightedBearing +=
              (WIND_ANGLE_ADJUSTMENT + station.windBearing) * DEGREES_TO_RADIANS * weight;
            weightedSpeed += station.windSpeed * weight;
          }
        });

        if (totalWeight > 0) {
          windGrid[y][x] = weightedBearing / totalWeight;
          speedGrid[y][x] = weightedSpeed / totalWeight;
        }
      }
    }
  }

  function createParticle(): Particle {
    const lng = Math.random() * (BOUNDARY.east - BOUNDARY.west) + BOUNDARY.west;
    const lat = Math.random() * (BOUNDARY.north - BOUNDARY.south) + BOUNDARY.south;
    const point = map.project(new mapboxgl.LngLat(lng, lat));
    const lifespanVariation = (Math.random() * 2 - 1) * LIFESPAN_VARIATION;

    return {
      x: point.x,
      y: point.y,
      age: 0,
      opacity: 0,
      lifespan: particleLifespan * (1 + lifespanVariation),
      trail: Array.from({ length: TRAIL_LENGTH }, () => ({
        x: point.x,
        y: point.y,
        bearing: 0,
        speed: 0
      }))
    };
  }

  function createParticles() {
    particles = Array(particleCount)
      .fill(null)
      .map(() => createParticle());
  }

  function updateParticleCount() {
    if (!isInitialized) return;

    if (particleCount > particles.length) {
      const newParticles = Array(particleCount - particles.length)
        .fill(null)
        .map(createParticle);
      particles = [...particles, ...newParticles];
    } else {
      particles = particles.slice(0, particleCount);
    }
  }

  function bilinearInterpolation(x: number, y: number, values: number[][]): number {
    const x1 = Math.floor(x);
    const x2 = Math.ceil(x);
    const y1 = Math.floor(y);
    const y2 = Math.ceil(y);

    if (x1 < 0 || x2 >= GRID_SIZE || y1 < 0 || y2 >= GRID_SIZE) {
      return 0;
    }

    const q11 = values[y1][x1];
    const q21 = values[y1][x2];
    const q12 = values[y2][x1];
    const q22 = values[y2][x2];

    if (q11 === null || q21 === null || q12 === null || q22 === null) {
      return 0;
    }

    const r1 = ((x2 - x) / (x2 - x1)) * q11 + ((x - x1) / (x2 - x1)) * q21;
    const r2 = ((x2 - x) / (x2 - x1)) * q12 + ((x - x1) / (x2 - x1)) * q22;

    return ((y2 - y) / (y2 - y1)) * r1 + ((y - y1) / (y2 - y1)) * r2;
  }

  function getInterpolatedWindData(x: number, y: number) {
    const gridX = (x / canvas.width) * (GRID_SIZE - 1);
    const gridY = (y / canvas.height) * (GRID_SIZE - 1);

    const interpolatedBearing = bilinearInterpolation(gridX, gridY, windGrid);
    const interpolatedSpeed = bilinearInterpolation(gridX, gridY, speedGrid);

    return { bearing: interpolatedBearing, speed: interpolatedSpeed };
  }

  function isWithinBoundary(lat: number, lng: number): boolean {
    return (
      lat >= BOUNDARY.south && lat <= BOUNDARY.north && lng >= BOUNDARY.west && lng <= BOUNDARY.east
    );
  }

  function calculateParticleLength(speed: number): number {
    const normalizedSpeed = Math.min(speed / MAX_WIND_SPEED, 1);
    return MIN_PARTICLE_LENGTH + normalizedSpeed * (MAX_PARTICLE_LENGTH - MIN_PARTICLE_LENGTH);
  }

  function easeInOutQuad(t: number): number {
    if (t < EASE_MIDPOINT) {
      return EASE_MULTIPLIER * Math.pow(t, EASE_POWER);
    } else {
      const adjustedT = -EASE_MULTIPLIER * t + EASE_MULTIPLIER;
      return 1 - Math.pow(adjustedT, EASE_POWER) / EASE_MULTIPLIER;
    }
  }

  function getEdgeOpacityFactor(lat: number, lng: number): number {
    const latRange = BOUNDARY.north - BOUNDARY.south;
    const lngRange = BOUNDARY.east - BOUNDARY.west;

    const latDistance = Math.min(lat - BOUNDARY.south, BOUNDARY.north - lat) / latRange;
    const lngDistance = Math.min(lng - BOUNDARY.west, BOUNDARY.east - lng) / lngRange;

    const distanceFromEdge = Math.min(latDistance, lngDistance) / EDGE_SOFTNESS;

    return MIN_OPACITY + (overallOpacity - MIN_OPACITY) * Math.min(distanceFromEdge, 1);
  }

  function calculateOpacity(age: number, lifespan: number): number {
    const fadeInDuration = lifespan * FADE_IN_DURATION;
    const fadeOutStart = lifespan * (1 - FADE_OUT_DURATION);

    if (age < fadeInDuration) {
      // Fade in
      return BASE_OPACITY + (overallOpacity - BASE_OPACITY) * easeInOutQuad(age / fadeInDuration);
    } else if (age > fadeOutStart) {
      // Fade out
      return (
        BASE_OPACITY +
        (overallOpacity - BASE_OPACITY) *
          easeInOutQuad((lifespan - age) / (lifespan - fadeOutStart))
      );
    }
    return overallOpacity; // Full opacity in the middle of lifespan
  }

  function interpolateTrail(
    trail: Particle['trail'],
    desiredLength: number
  ): { x: number; y: number }[] {
    const totalDistance = trail.reduce((sum, point, index) => {
      if (index === 0) return 0;
      const prevPoint = trail[index - 1];
      return (
        sum + Math.sqrt(Math.pow(point.x - prevPoint.x, 2) + Math.pow(point.y - prevPoint.y, 2))
      );
    }, 0);

    const segmentLength = totalDistance / (desiredLength - 1);
    const interpolatedTrail: { x: number; y: number }[] = [{ x: trail[0].x, y: trail[0].y }];

    let currentDistance = 0;
    let currentIndex = 0;

    for (let i = 1; i < desiredLength - 1; i++) {
      currentDistance += segmentLength;

      while (currentIndex < trail.length - 1) {
        const point = trail[currentIndex];
        const nextPoint = trail[currentIndex + 1];
        const segmentDistance = Math.sqrt(
          Math.pow(nextPoint.x - point.x, 2) + Math.pow(nextPoint.y - point.y, 2)
        );

        if (currentDistance <= segmentDistance) {
          const t = currentDistance / segmentDistance;
          interpolatedTrail.push({
            x: point.x + t * (nextPoint.x - point.x),
            y: point.y + t * (nextPoint.y - point.y)
          });
          break;
        }

        currentDistance -= segmentDistance;
        currentIndex++;
      }
    }

    interpolatedTrail.push({ x: trail[trail.length - 1].x, y: trail[trail.length - 1].y });
    return interpolatedTrail;
  }

  function getColorForSpeed(speed: number): string {
    for (let i = 0; i < WIND_SPEED_COLORS.length - 1; i++) {
      if (speed <= WIND_SPEED_COLORS[i + 1].speed) {
        const t =
          (speed - WIND_SPEED_COLORS[i].speed) /
          (WIND_SPEED_COLORS[i + 1].speed - WIND_SPEED_COLORS[i].speed);
        const r = Math.round(
          WIND_SPEED_COLORS[i].color[0] +
            t * (WIND_SPEED_COLORS[i + 1].color[0] - WIND_SPEED_COLORS[i].color[0])
        );
        const g = Math.round(
          WIND_SPEED_COLORS[i].color[1] +
            t * (WIND_SPEED_COLORS[i + 1].color[1] - WIND_SPEED_COLORS[i].color[1])
        );
        const b = Math.round(
          WIND_SPEED_COLORS[i].color[2] +
            t * (WIND_SPEED_COLORS[i + 1].color[2] - WIND_SPEED_COLORS[i].color[2])
        );
        return `rgb(${r}, ${g}, ${b})`;
      }
    }

    return `rgb(${WIND_SPEED_COLORS[WIND_SPEED_COLORS.length - 1].color.join(',')})`;
  }

  function animate() {
    if (!ctx) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);

    particles.forEach((particle, i) => {
      const { bearing, speed } = getInterpolatedWindData(particle.x, particle.y);
      const dx = Math.cos(bearing) * speed * speedFactor;
      const dy = Math.sin(bearing) * speed * speedFactor;

      // Update particle position
      particle.x += dx;
      particle.y += dy;
      particle.age++;

      // Calculate particle length based on wind speed
      const particleLength = calculateParticleLength(speed);

      // Scale trail length based on particle length
      const speedScaleFactor =
        (particleLength - MIN_PARTICLE_LENGTH) / (MAX_PARTICLE_LENGTH - MIN_PARTICLE_LENGTH);
      const sizeScaleFactor = particleSize / 2;
      const combinedScaleFactor = (speedScaleFactor + sizeScaleFactor) / 2;
      const trailLength = Math.round(
        MIN_TRAIL_LENGTH + combinedScaleFactor * (MAX_TRAIL_LENGTH - MIN_TRAIL_LENGTH)
      );

      // Adjust trail array length if needed
      if (particle.trail.length < trailLength) {
        particle.trail.push(
          ...Array(trailLength - particle.trail.length).fill({ x: particle.x, y: particle.y })
        );
      } else if (particle.trail.length > trailLength) {
        particle.trail = particle.trail.slice(0, trailLength);
      }

      // Update trail
      particle.trail.unshift({ x: particle.x, y: particle.y, bearing, speed });
      particle.trail = particle.trail.slice(0, TRAIL_LENGTH);

      const lngLat = map.unproject(new mapboxgl.Point(particle.x, particle.y));
      const edgeOpacityFactor = getEdgeOpacityFactor(lngLat.lat, lngLat.lng);
      const ageOpacity = calculateOpacity(particle.age, particle.lifespan);

      particle.opacity = Math.max(
        BASE_OPACITY,
        ageOpacity * edgeOpacityFactor * (BASE_OPACITY + speed * (1 - BASE_OPACITY))
      );

      // Check if particle should be reset
      if (
        !isWithinBoundary(lngLat.lat, lngLat.lng) ||
        particle.age >= particle.lifespan ||
        Math.sqrt(dx * dx + dy * dy) < MIN_SPEED_THRESHOLD
      ) {
        particles[i] = createParticle();
        return;
      }

      const particleColor = getColorForSpeed(speed);

      // Apply opacity to the color
      const [r, g, b] = particleColor.match(/\d+/g)!.map(Number);
      const colorWithOpacity = `rgba(${r}, ${g}, ${b}, ${particle.opacity})`;

      ctx.strokeStyle = colorWithOpacity;
      ctx.fillStyle = colorWithOpacity;
      ctx.lineWidth = particleSize;

      ctx.beginPath();

      // Draw trail using a quadratic Bézier curve
      const trailPoints = interpolateTrail(particle.trail, ARROW_HISTORY);
      const startPoint = trailPoints[0];
      const endPoint = trailPoints[trailPoints.length - 1];
      const controlPoint = trailPoints[Math.floor(trailPoints.length / 2)];

      ctx.moveTo(startPoint.x, startPoint.y);
      ctx.quadraticCurveTo(controlPoint.x, controlPoint.y, endPoint.x, endPoint.y);

      // Draw arrow head
      const headLength = particleSize * ARROW_HEAD_FACTOR;
      const x1 = particle.x - headLength * Math.cos(bearing - ARROW_HEAD_ANGLE);
      const y1 = particle.y - headLength * Math.sin(bearing - ARROW_HEAD_ANGLE);
      const x2 = particle.x - headLength * Math.cos(bearing + ARROW_HEAD_ANGLE);
      const y2 = particle.y - headLength * Math.sin(bearing + ARROW_HEAD_ANGLE);

      ctx.moveTo(particle.x, particle.y);
      ctx.lineTo(x1, y1);
      ctx.moveTo(particle.x, particle.y);
      ctx.lineTo(x2, y2);

      ctx.stroke();
    });

    animationFrameId = requestAnimationFrame(animate);
  }
</script>

<canvas bind:this={canvas} class="absolute top-0 left-0 pointer-events-none"></canvas>
