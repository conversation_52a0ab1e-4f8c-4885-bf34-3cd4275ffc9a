<script lang="ts">
  /* eslint-disable @typescript-eslint/no-magic-numbers */
  import { Button } from '$lib/components/ui/button';
  import * as Popover from '$lib/components/ui/popover';
  import type { Snippet } from 'svelte';

  interface Props {
    children?: Snippet;
  }

  let { children }: Props = $props();
</script>

<Popover.Root>
  <Popover.Trigger>
    <Button
      class="left-2 absolute top-[5.5rem] z-10 rounded-full w-[35px] h-[35px]"
      size="icon"
      variant="outline"
    >
      <span class="material-icons">weather_mix</span>
    </Button>
  </Popover.Trigger>
  <Popover.Content align="start" side="right" sideOffset={50} alignOffset={-5} class="w-fit">
    <p class="font-semibold border-solid border-b border-border mb-sm">Weather</p>
    <div class="flex flex-col gap-md min-w-[250px]">
      {@render children?.()}
    </div>
  </Popover.Content>
</Popover.Root>
