<script lang="ts">
  import { Loader, MapStyleToggle } from '$lib/components';
  import { isDarkMode } from '$lib/stores/theme';
  import store from '$lib/stores/app.svelte';
  import { MapStyle, type DeltaMeterSession, type RetrospectiveStorm } from '$lib/types';
  import { fitMapToBounds, titleCase } from '$lib/utils';
  import { MAP_BOUNDS } from '$lib/config/map';
  import { getAmiData } from '$lib/api';
  import SettingsPane from './SettingsPane.svelte';
  import PlayPause from './PlayPause.svelte';
  import mapboxgl, { type Map } from 'mapbox-gl';
  import { DateTime } from 'luxon';
  import { onDestroy, onMount, type Snippet } from 'svelte';

  const SOURCE = 'meters';
  const SOURCE_LAYER = 'non_clustered';

  const amiPopup = new mapboxgl.Popup({
    closeButton: false,
    closeOnClick: false
  });

  interface Props {
    class?: string;
    loading?: boolean;
    selectedStorm: RetrospectiveStorm | null;
    children?: Snippet;
  }

  let { class: className = '', loading = false, selectedStorm, children }: Props = $props();

  let allMeters: Record<string, string> = {};
  let hoveredMeter: string | null = null;
  let meterHistory: Record<string, boolean>[] = [];

  let amiData = $state<DeltaMeterSession[]>([]);
  let frameIndex = $state<number>(0);
  let isMapLoaded = $state<boolean>(false);
  let map = $state<Map | undefined>();
  let mapContainer = $state<HTMLDivElement | undefined>();
  let mapStyle = $state<MapStyle>($isDarkMode ? MapStyle.dark : MapStyle.light);

  let etrMapId = $derived(store.appConfig?.etrMapId?.value ?? '');
  let currentFrameTime = $derived(getCurrentFrameTime(amiData, frameIndex));

  const onWatchThemeMode = isDarkMode.subscribe((isDark) => {
    if (map && isMapLoaded) {
      mapStyle = isDark ? MapStyle.dark : MapStyle.light;
      changeMapStyle();
    }
  });

  $effect(() => {
    if (map && isMapLoaded && selectedStorm) {
      resetAllMeters();
      fetchAmiData();
    }
  });

  $effect(() => {
    updateLayerWithAmiData(amiData, frameIndex);
  });

  onMount(() => {
    initialize();
  });

  onDestroy(() => {
    map?.off('load', onMapLoad);
    map?.off('mousemove', 'region-fill', onMousemoveRegion);
    map?.off('mouseleave', 'region-fill', onMouseLeaveRegion);
    map?.off('mousemove', SOURCE, onMousemoveRegion);
    map?.off('mouseleave', SOURCE, onMouseLeaveRegion);
    map?.off('resize', () => fitMapToBounds(map, store.appConfig?.maxBounds?.value));
    onWatchThemeMode();
  });

  function initialize() {
    if (!store.appConfig) return;
    map = new mapboxgl.Map({
      center: store.appConfig.center.value,
      container: mapContainer as HTMLDivElement,
      style: `mapbox://styles/mapbox/${mapStyle}`,
      maxBounds: MAP_BOUNDS
    });
    map.on('load', onMapLoad);
    map.on('resize', () => fitMapToBounds(map, store.appConfig?.maxBounds?.value));
    map.on('mousemove', SOURCE, onMousemoveRegion);
    map.on('mouseleave', SOURCE, onMouseLeaveRegion);
  }

  function onMapLoad() {
    if (!store.appConfig) return;
    if (etrMapId) {
      map!.addSource(etrMapId, {
        promoteId: 'name',
        type: 'vector',
        url: store.appConfig.tilesetUrl.value
      });
    }

    addAmiSource();
    fitMapToBounds(map, store.appConfig.maxBounds?.value);
    addRegionBorderLayer();
    addAmiLayer();
    isMapLoaded = true;
  }

  function addRegionBorderLayer() {
    if (!store.appConfig) return;
    map!.addLayer({
      ...store.appConfig.regionBorderLayer,
      type: 'line',
      source: etrMapId,
      'source-layer': etrMapId
    });
  }

  function onChangeTheme() {
    if (!store.appConfig) return;
    if (etrMapId) {
      map!.addSource(etrMapId, {
        promoteId: 'name',
        type: 'vector',
        url: store.appConfig.tilesetUrl.value
      });
    }

    addAmiSource();
    addAmiLayer();
    updateLayerWithAmiData(amiData, frameIndex);
    addRegionBorderLayer();
  }

  function changeMapStyle() {
    if (!map!.getStyle()?.sprite?.includes(`/${mapStyle}`)) {
      map!.once('style.load', onChangeTheme);
      map!.setStyle(`mapbox://styles/mapbox/${mapStyle}`);
    }
  }

  function onMousemoveRegion(e: any) {
    if (hoveredMeter) {
      map!.setFeatureState(
        { id: hoveredMeter, source: SOURCE, sourceLayer: SOURCE_LAYER },
        { hover: false }
      );
    }

    if (!e.features[0]?.state?.status) return;

    hoveredMeter = e.features[0].id as string;

    map!.setFeatureState(
      { id: hoveredMeter, source: SOURCE, sourceLayer: SOURCE_LAYER },
      { hover: true }
    );

    const feature = e.features[0];
    updatePopupContent(feature?.properties?.meter, feature.state?.status);
    amiPopup.setLngLat(e.lngLat).addTo(map!);

    map!.getCanvas().style.cursor = 'pointer';
  }

  function updatePopupContent(meterID: string, status: 'on' | 'off' = 'off') {
    amiPopup.setHTML(`
    <div class="popup-region-info">
      <p class="popup-feature-name">Meter: ${titleCase(meterID)}</p>
      <div class="info-row">
         <span class="label">Status:</span>
        <span class="value">${titleCase(status)}</span>
      </div>
    </div>
  `);
  }

  function onMouseLeaveRegion() {
    if (hoveredMeter) {
      map!.setFeatureState(
        { id: hoveredMeter, source: SOURCE, sourceLayer: SOURCE_LAYER },
        { hover: false }
      );
    }

    hoveredMeter = null;

    amiPopup.remove();
    map!.getCanvas().style.cursor = '';
  }

  function addAmiSource() {
    map!.addSource('meters', {
      type: 'vector',
      url: `mapbox://esource.apc_dv_meter_heatmap`,
      promoteId: 'meter'
    });
  }

  function addAmiLayer() {
    map!.addLayer({
      id: SOURCE,
      type: 'circle',
      source: SOURCE,
      'source-layer': SOURCE_LAYER,
      paint: {
        'circle-color': [
          'case',
          // If the meter was ever off and is now on, show green
          ['all', ['==', ['feature-state', 'status'], 'on']],
          '#00FF00',
          // Show red if the meter is currently off
          ['==', ['feature-state', 'status'], 'off'],
          '#FF0000',
          // Do not display anything otherwise (for when the meter is 'on' but was never 'off')
          'transparent'
        ],
        'circle-radius': 2
      }
    });
  }

  async function fetchAmiData() {
    if (!map || !selectedStorm) return;

    const { stormStartDate, stormEndDate } = selectedStorm ?? {};
    const start = stormStartDate?.toISOString();
    const end = stormEndDate?.toISOString();

    if (!(start && end)) return;

    try {
      const data = await getAmiData(start, end);
      amiData = data.meterSessions;
    } catch {
      console.error('Error fetching AMI data');
    }
  }

  function updateLayerWithAmiData(data: DeltaMeterSession[], index: number) {
    if (!data || !data[index]) return;

    if (index === 0) {
      resetAllMeters();
    }

    if (map!.getSource(SOURCE)) {
      updateMeterFeatures(data[index].meters, index);
    } else {
      map!.once('sourcedata', () => updateMeterFeatures(data[index].meters, index));
    }
  }

  function updateMeterFeatures(meterValues: Record<string, number>, index: number) {
    if (!map!.getSource(SOURCE)) return;

    meterHistory[index] = {};

    Object.keys(meterValues).forEach((meterID) => {
      const currentStatus = meterValues[meterID] ? 'on' : 'off';
      allMeters[meterID] = currentStatus;
      meterHistory[index][meterID] ||= currentStatus === 'on';

      map!.setFeatureState(
        { id: meterID, source: SOURCE, sourceLayer: SOURCE_LAYER },
        { status: currentStatus }
      );
    });
  }

  function onIncrementStep() {
    if (frameIndex >= amiData.length - 1) {
      resetAllMeters();
    }
    frameIndex = frameIndex < amiData.length - 1 ? frameIndex + 1 : 0;
  }

  function onDecrementStep() {
    resetAmiMetersAtIndex(frameIndex);
    frameIndex = frameIndex > 0 ? frameIndex - 1 : amiData.length - 1;
  }

  function getCurrentFrameTime(data: DeltaMeterSession[], index: number) {
    if (!data[index]) return;

    return DateTime.fromISO(data[index].timestamp).toSeconds();
  }

  function resetAmiMetersAtIndex(index: number) {
    if (!meterHistory[index]) return;

    Object.keys(meterHistory[index]).forEach((meterID) => {
      map!.setFeatureState(
        { id: meterID, source: SOURCE, sourceLayer: SOURCE_LAYER },
        { status: null }
      );
    });
  }

  function resetAllMeters() {
    Object.keys(allMeters).forEach((meterID) => {
      map!.setFeatureState(
        { id: meterID, source: SOURCE, sourceLayer: SOURCE_LAYER },
        { status: null }
      );
    });
    frameIndex = 0;
  }
</script>

<div
  class="map relative w-full h-full rounded-md overflow-hidden border-solid border !border-borderColor shadow-md {className}"
>
  {#if isMapLoaded}
    <SettingsPane>
      <div class="flex flex-col gap-md">
        <MapStyleToggle
          {mapStyle}
          onSelect={(value) => {
            mapStyle = value;
            changeMapStyle();
          }}
        />
      </div>
    </SettingsPane>
  {/if}

  <PlayPause
    {currentFrameTime}
    onRewind={onDecrementStep}
    onForward={onIncrementStep}
    maxStep={amiData?.length - 1}
    bind:index={frameIndex}
    onChange={(e) => {
      frameIndex = e;
    }}
    disablePlayback
  />

  {#if typeof window !== 'undefined'}
    <div bind:this={mapContainer} class="mapbox-map w-full h-full absolute top-0 bottom-0"></div>
  {/if}
  {@render children?.()}
  <div
    class="absolute inset-0 flex items-center rounded-md justify-center bg-component !w-full {loading
      ? 'flex'
      : 'hidden'}"
  >
    <Loader />
  </div>
</div>
