<script lang="ts">
  import { onDestroy, onMount } from 'svelte';
  import mapboxgl, { type Map } from 'mapbox-gl';
  import type { MapMouseEvent } from 'mapbox-gl';
  import {
    formatRegionId,
    getRegionalFillLayer,
    MAP_BOUNDS,
    DEFAULT_WIND_CONFIG,
    DEFAULT_RADAR_CONFIG
  } from '$lib/config/map';
  import { fitMapToBounds, formatNumbers } from '$lib/utils';
  import {
    MapStyle,
    type MapConfig,
    type RadarData,
    type Region,
    type SpatialData,
    type WeatherData,
    type WeatherStation,
    type Weather as WeatherType
  } from '$lib/types';
  import { isDarkMode } from '$lib/stores/theme';
  import store from '$lib/stores/app.svelte';
  import { isSystemic, showDailyMapWindAnimation } from '$lib/stores/constants';
  import MapStyleToggle from './MapStyleToggle.svelte';
  import SettingsPane from './SettingsPane.svelte';
  import Weather from './Weather.svelte';
  import Loader from '../Loader/Loader.svelte';
  import MapLegend from './MapLegend.svelte';
  import WindAnimationToggle from './WindAnimationToggle.svelte';
  import WindAnimation from './WindAnimation.svelte';
  import WeatherPane from './WeatherPane.svelte';
  import LayersPane from './LayersPane.svelte';
  import RadarToggle from './RadarToggle.svelte';
  import type { GeoJSONSource } from 'mapbox-gl';

  const ZOOM_FACTOR = 1.1;
  const SYSTEM_VIEW = isSystemic ? 'regions' : 'larger-regions';
  const REGION_VIEW = 'regions';

  const regionPopup = new mapboxgl.Popup({
    closeButton: false,
    closeOnClick: false
  });

  interface Props {
    isLoadingWeather?: boolean;
    loading?: boolean;
    mapConfigs: MapConfig[];
    selectedIndex?: number;
    selectedRegion?: Region | null;
    spatialData: SpatialData[];
    weatherStations?: WeatherStation[];
    onRegionClick?: (e: MapMouseEvent) => void;
    isCurrentDay?: boolean;
  }

  let {
    isLoadingWeather = false,
    loading = false,
    mapConfigs,
    selectedIndex = $bindable(0),
    selectedRegion = null,
    spatialData,
    weatherStations = [],
    onRegionClick = () => {},
    isCurrentDay = false
  }: Props = $props();

  let hoveredRegion: string | null = null;
  let initialZoom: number | null = null;

  let innerWidth = $state<number>(0);
  let isMapLoading = $state<boolean>(true);
  let isWindAnimationVisible = $state<boolean>(false);
  let map = $state<Map | undefined>();
  let mapContainer = $state<HTMLDivElement | undefined>();
  let mapStyle: MapStyle = $state<MapStyle>($isDarkMode ? MapStyle.dark : MapStyle.light);
  let selectedMapConfig = $state<MapConfig>(mapConfigs[selectedIndex]);
  let showCentroids = $state<boolean>(false);

  let particleCount = $state<number>(DEFAULT_WIND_CONFIG.particleCount);
  let particleSize = $state<number>(DEFAULT_WIND_CONFIG.particleSize);
  let particleLifespan = $state<number>(DEFAULT_WIND_CONFIG.particleLifespan);
  let speedFactor = $state<number>(DEFAULT_WIND_CONFIG.speedFactor);
  let overallOpacity = $state<number>(DEFAULT_WIND_CONFIG.overallOpacity);
  let showWind = $derived(
    showDailyMapWindAnimation && weatherStations?.length && !isLoadingWeather
  );

  let regionNames = $derived(spatialData.map((region) => region.name));
  let mapId = $derived(selectedRegion?.name === 'system' ? SYSTEM_VIEW : REGION_VIEW);

  let isRadarVisible: boolean = $state(isCurrentDay);
  let colorScheme: string = $state(DEFAULT_RADAR_CONFIG.colorScheme);
  let radarData: WeatherData | null = $state(null);
  let mapFrames: RadarData[] = $state([]);
  let frameIndex: number = $state(0);
  let radarOpacity = $state(DEFAULT_RADAR_CONFIG.opacity);
  let prevIsCurrentDay: boolean = $state(isCurrentDay);

  $effect(() => {
    if (!isCurrentDay && prevIsCurrentDay) {
      isRadarVisible = false;
      onUpdateRadarLayer();
    }

    if (isCurrentDay && !prevIsCurrentDay) {
      isRadarVisible = true;
      onUpdateRadarLayer();
    }

    prevIsCurrentDay = isCurrentDay;
  });

  $effect(() => {
    selectedMapConfig = mapConfigs[selectedIndex];
  });

  $effect(() => {
    if (!map || !spatialData.length || isMapLoading) return;

    if (map.getSource(mapId)) {
      setRegionalWeatherFeatureStates();
      setRegionTextDataToSource();
    } else {
      map.once('sourcedata', () => {
        setRegionalWeatherFeatureStates();
        setRegionTextDataToSource();
      });
    }
  });
  $effect(() => {
    if (mapConfigs.length && regionNames.length && !isMapLoading) {
      addRegionFillLayer();
      addRegionBorderLayer();
    }
  });

  $effect(() => {
    if (map && selectedRegion) {
      onChangeViewLevel();
    }
  });

  const onWatchThemeMode = isDarkMode.subscribe((isDark) => {
    if (map && !isMapLoading) {
      mapStyle = isDark ? MapStyle.dark : MapStyle.light;
      changeMapStyle();
    }
  });

  onMount(() => {
    initializeMap();
  });

  onDestroy(() => {
    map?.off('load', onMapLoad);
    map?.off('mousemove', 'region-fill', onMousemoveRegion);
    map?.off('mouseleave', 'region-fill', onMouseLeaveRegion);
    map?.off('click', 'region-fill', onRegionClick);
    map?.off('resize', () => fitMapToBounds(map, store.appConfig?.maxBounds?.value));
    onWatchThemeMode();
  });

  function initializeMap() {
    if (!store.appConfig) return;
    const mapSettings = {
      center: store.appConfig.center.value,
      container: mapContainer as HTMLDivElement,
      style: `mapbox://styles/mapbox/${$isDarkMode ? MapStyle.dark : MapStyle.light}`,
      maxBounds: MAP_BOUNDS
    };
    map = new mapboxgl.Map(mapSettings);
    map.on('load', onMapLoad);
    map.on('mousemove', 'region-fill', onMousemoveRegion);
    map.on('mouseleave', 'region-fill', onMouseLeaveRegion);
    map.on('click', 'region-fill', onRegionClick);
    map.on('resize', () => fitMapToBounds(map, store.appConfig?.maxBounds?.value));
  }

  function onMapLoad() {
    if (!store.appConfig) return;
    map!.addSource(mapId, {
      promoteId: 'name',
      type: 'vector',
      url: store.appConfig.tilesetUrl.value
    });
    fetchRadarData();
    setRegionalWeatherFeatureStates();
    addRegionTextSource();
    setRegionTextDataToSource();
    addStateFillLayer();
    fitMapToBounds(map, store.appConfig.maxBounds?.value);
    map!.once('idle', () => (initialZoom = map!.getZoom()));
    isMapLoading = false;
  }

  function addRegionBorderLayer() {
    if (!store.appConfig?.regionBorderLayer || map!.getLayer('region-borders')) return;
    map!.addLayer({
      ...store.appConfig.regionBorderLayer,
      type: 'line',
      source: mapId,
      'source-layer': mapId,
      ...(isSystemic && { filter: ['in', ['get', 'name'], ['literal', regionNames]] })
    });
  }

  function addRegionFillLayer() {
    if (!selectedMapConfig || map!.getLayer('region-fill')) return;
    map!.addLayer({
      ...getRegionalFillLayer(selectedMapConfig, mapId),
      ...(isSystemic && { filter: ['in', ['get', 'name'], ['literal', regionNames]] })
    });
  }

  function addStateFillLayer() {
    if (!store.appConfig?.stateFillLayer) return;
    map!.addLayer({
      ...store.appConfig.stateFillLayer,
      type: 'fill'
    });
  }

  function onChangeTheme() {
    if (!store.appConfig) return;

    map!.addSource(mapId, {
      promoteId: 'name',
      type: 'vector',
      url: store.appConfig.tilesetUrl.value
    });

    addRegionFillLayer();
    addRegionBorderLayer();
    addStateFillLayer();
    setRegionalWeatherFeatureStates();
    addRegionTextSource();
    setRegionTextDataToSource();

    if (isRadarVisible) {
      radarData ? showFrame() : fetchRadarData();
    }

    if (showCentroids) {
      addRegionTextLayer();
    }
  }

  function changeMapStyle() {
    if (!map?.getStyle()?.sprite?.includes(`/${mapStyle}`)) {
      map!.once('style.load', onChangeTheme);
      map!.setStyle(`mapbox://styles/mapbox/${mapStyle}`);
    }
  }

  function setRegionalWeatherFeatureStates() {
    clearStaleFeatureStates();
    spatialData.forEach((data: SpatialData) => {
      if (map!.getSource(mapId)) {
        map!.setFeatureState(
          { id: data.name, source: mapId, sourceLayer: mapId },
          { ...data.attributes }
        );
      }
    });
  }

  function clearStaleFeatureStates() {
    if (!map?.getSource(mapId)) {
      map?.once('sourcedata', () => map!.removeFeatureState({ source: mapId, sourceLayer: mapId }));
    } else {
      map!.removeFeatureState({ source: mapId, sourceLayer: mapId });
    }
  }

  function updateRegionLayer() {
    // No API to update metadata so cannot use "map.setPaintProperty(...)"
    if (map!.getLayer('region-fill')) map!.removeLayer('region-fill');
    if (map!.getLayer('region-text')) map!.removeLayer('region-text');

    addRegionFillLayer();
    if (showCentroids) {
      addRegionTextLayer();
    }
  }

  function onMousemoveRegion(e: any) {
    if (map!.getSource(mapId)) {
      // 1. Highlight hovered territory
      const { sourceLayer } = e.features[0];

      if (hoveredRegion) {
        map!.setFeatureState({ id: hoveredRegion, source: mapId, sourceLayer }, { hover: false });
      }

      hoveredRegion = e.features[0]?.id;

      if (hoveredRegion) {
        map!.setFeatureState({ id: hoveredRegion, source: mapId, sourceLayer }, { hover: true });
        const hoveredRecord = spatialData.find((record) => record.name === hoveredRegion);
        if (!hoveredRecord) return;
        // 2. Enable popup display
        map!.getCanvas().style.cursor = 'pointer';
        updatePopupContent(hoveredRecord);
        regionPopup.setLngLat(e.lngLat);
      }
    }
  }

  function updatePopupContent(record: SpatialData) {
    const { unit, attributeType, precision } = selectedMapConfig as MapConfig;
    const {
      label = '',
      precision: secondaryTooltipPrecision = 0,
      attributeType: secondaryTooltipAttributeType = ''
    } = selectedMapConfig?.secondaryTooltip || {};

    const value = record?.attributes?.[attributeType];

    const secondaryTooltipValue =
      record?.attributes?.[secondaryTooltipAttributeType as keyof typeof record.attributes];

    const regionName = record.name ?? record.displayName;

    regionPopup
      .setHTML(
        `<div class="popup-region-info">
              <p class="popup-feature-name">${regionName}</p>
              <div class="info-row">
                <span class="label">${selectedMapConfig!.tooltipLabel ?? selectedMapConfig!.displayName}:</span>
                <span class="value">
                      ${typeof value === 'number' ? value?.toFixed(precision) : value} ${unit}
                </span>
              </div>
                ${
                  selectedMapConfig?.secondaryTooltip
                    ? `<div class="info-row">
                        <span class="label">${label}:</span>
                        <span class="value">${typeof secondaryTooltipValue === 'number' ? secondaryTooltipValue?.toFixed(secondaryTooltipPrecision) : secondaryTooltipValue}</span>
                      </div>`
                    : ''
                }
            </div>`
      )
      .addTo(map!);
  }

  function onMouseLeaveRegion() {
    // 1. Clean-up hover layer highlight
    if (map!.getSource(mapId)) {
      if (hoveredRegion) {
        map!.setFeatureState(
          { id: hoveredRegion, source: mapId, sourceLayer: mapId },
          { hover: false }
        );
      }
      hoveredRegion = null;

      // 2. Clean-up hover popup display
      map!.getCanvas().style.cursor = '';
      regionPopup.remove();
    }
  }

  function onMapConfigSelected(value: WeatherType) {
    const config = mapConfigs.find((c) => c.attributeType === value);

    if (config) {
      selectedMapConfig = config;
      updateRegionLayer();
    }
  }

  function onChangeViewLevel() {
    if (!map || !selectedRegion || !initialZoom) return;

    const { centroid, name } = selectedRegion;

    map.flyTo({
      center: centroid,
      duration: 1000,
      essential: true,
      zoom: name === 'system' ? initialZoom : initialZoom * ZOOM_FACTOR
    });

    applyViewLevelChanges();
  }

  function addRegionTextSource() {
    map!.addSource('region-text-source', {
      type: 'geojson',
      data: { type: 'FeatureCollection', features: [] }
    });
  }

  function setRegionTextDataToSource() {
    if (!store.regions.length) return;

    const centroidMap = store.regions.reduce(
      (acc, region) => {
        acc[region.name] = region.centroid;
        return acc;
      },
      {} as Record<string, [number, number] | undefined>
    );

    const features = spatialData.map((record) => ({
      type: 'Feature' as const,
      properties: {
        region: formatRegionId(record.name),
        ...mapConfigs.reduce(
          (acc, item: MapConfig) => {
            const source = record.attributes[item.attributeType];
            const precision = item.precision;

            acc[item.attributeType] =
              typeof source === 'string' ? source : formatNumbers(source, precision);
            return acc;
          },
          {} as Record<string, string>
        ),
        threatLevel: formatNumbers(record.attributes.outages, 0)
      },
      geometry: {
        type: 'Point' as const,
        coordinates: centroidMap[formatRegionId(record.name)] || [0, 0]
      }
    }));

    (map!.getSource('region-text-source') as GeoJSONSource)?.setData({
      type: 'FeatureCollection',
      features
    });
  }

  function addRegionTextLayer() {
    if (!selectedMapConfig?.attributeType) return;
    map!.addLayer({
      id: 'region-text',
      type: 'symbol',
      source: 'region-text-source',
      layout: {
        'text-field': ['coalesce', ['get', selectedMapConfig.attributeType], '-'],
        'text-size': 12,
        'text-allow-overlap': true
      },
      paint: {
        'text-color': mapStyle === MapStyle.light || mapStyle === MapStyle.street ? '#222' : '#fff',
        'text-halo-blur': 4
      }
    });
  }

  function onToggleWindAnimation(value: boolean) {
    isWindAnimationVisible = value;
  }

  function applyViewLevelChanges() {
    if (!store.appConfig || !map) return;

    const prevSource = mapId === REGION_VIEW ? SYSTEM_VIEW : REGION_VIEW;

    if (map.getSource(prevSource)) {
      ['region-fill', 'region-borders', 'region-text'].forEach((layerId) => {
        if (map?.getLayer(layerId)) map?.removeLayer(layerId);
      });
      map.removeSource(prevSource);
    }

    if (!map.getSource(mapId)) {
      map.addSource(mapId, {
        promoteId: 'name',
        type: 'vector',
        url: store.appConfig.tilesetUrl.value
      });
    }

    map!.once('sourcedata', () => {
      addRegionFillLayer();
      addRegionBorderLayer();
      addStateFillLayer();
      if (showCentroids) {
        addRegionTextLayer();
      }
    });
  }

  function showFrame() {
    addRadarLayer(mapFrames[frameIndex].path);
  }

  function fetchRadarData() {
    fetch(DEFAULT_RADAR_CONFIG.radarUrl)
      .then((response) => {
        if (!response.ok) {
          throw new Error('Network response error');
        }
        return response.json();
      })
      .then((data) => {
        if (data) {
          radarData = data;
          mapFrames = [...(data.radar?.past ?? [])];
          frameIndex = data?.radar?.past?.length - 1;
          showFrame();
        }
      })
      .catch(() => {
        console.error('Failed to fetch radar data');
      });
  }

  function addRadarLayer(path: string) {
    if (radarData) {
      const url = `${radarData.host}${path}/256/{z}/{x}/{y}/${colorScheme}/1_1.png`;
      map?.addSource(path, {
        type: 'raster',
        tiles: [url]
      });

      map?.addLayer({
        id: path,
        type: 'raster',
        source: path,
        paint: {
          'raster-opacity': isRadarVisible ? radarOpacity : 0
        }
      });
    }
  }

  function onUpdateRadarLayer() {
    if (isRadarVisible) {
      map?.setPaintProperty(mapFrames[frameIndex].path, 'raster-opacity', radarOpacity);
    } else {
      map?.setPaintProperty(mapFrames[frameIndex].path, 'raster-opacity', 0);
    }
  }

  function onUpdateColor(value: string) {
    colorScheme = value;
    mapFrames.forEach((i) => {
      if (map?.getSource(i.path)) {
        map.removeLayer(i.path);
        map.removeSource(i.path);
      }
    });
    showFrame();
  }
</script>

<svelte:window bind:innerWidth />

<div
  class="w-full min-h-[250px] relative rounded-md overflow-hidden border-solid border !border-borderColor shadow-md"
>
  <div class="map relative h-full">
    <div bind:this={mapContainer} class="map-container w-full absolute top-0 bottom-0"></div>
    <SettingsPane>
      <MapStyleToggle
        {mapStyle}
        onSelect={(value) => {
          mapStyle = value;
          changeMapStyle();
        }}
      />
    </SettingsPane>

    <LayersPane>
      <Weather
        layer={selectedMapConfig?.attributeType}
        onSelect={(value, index) => {
          onMapConfigSelected(value);
          selectedIndex = index;
        }}
        {mapConfigs}
        show={showCentroids}
        onToggle={(value) => {
          showCentroids = value;
          showCentroids ? addRegionTextLayer() : map!.removeLayer('region-text');
        }}
      />
    </LayersPane>

    {#if isCurrentDay || showWind}
      <WeatherPane>
        {#if isCurrentDay}
          <RadarToggle
            bind:show={isRadarVisible}
            onSelect={onUpdateRadarLayer}
            {colorScheme}
            bind:opacity={radarOpacity}
            {onUpdateColor}
            onSliderChange={() => {
              map?.setPaintProperty(mapFrames[frameIndex].path, 'raster-opacity', radarOpacity);
            }}
          />
        {/if}
        {#if showWind}
          <WindAnimationToggle
            show={isWindAnimationVisible}
            {onToggleWindAnimation}
            bind:particleCount
            bind:particleSize
            bind:particleLifespan
            bind:speedFactor
            bind:overallOpacity
          />
        {/if}
      </WeatherPane>
    {/if}
  </div>

  <MapLegend {map} />

  {#if isWindAnimationVisible && map}
    <WindAnimation
      {map}
      {weatherStations}
      {particleCount}
      {particleSize}
      {particleLifespan}
      {speedFactor}
      {overallOpacity}
    />
  {/if}

  <div
    class="absolute inset-0 flex items-center rounded-md justify-center bg-component !w-full {loading
      ? 'flex'
      : 'hidden'}"
  >
    <Loader />
  </div>
</div>
