<script lang="ts">
  import { type ControlPosition, type Map } from 'mapbox-gl';
  import LegendControl from 'mapboxgl-legend';

  const BREAKPOINT = 767;

  interface Props {
    collapseLegend?: boolean;
    layers?: string[];
    map: Map | undefined;
    position?: ControlPosition;
  }

  let {
    collapseLegend = false,
    layers = ['region-fill'],
    map,
    position = 'top-right'
  }: Props = $props();

  const legend = new LegendControl({
    layers,
    collapsed: collapseLegend || window.innerWidth < BREAKPOINT
  });

  $effect(() => {
    map?.addControl(legend, position);
  });
</script>
