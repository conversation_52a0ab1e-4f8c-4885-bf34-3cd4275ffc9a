<script lang="ts">
  import { Toggle } from '$lib/components';
  import type { MapConfig, Weather } from '$lib/types';

  interface Props {
    layer: Weather;
    mapConfigs: MapConfig[];
    show: boolean;
    onSelect: (value: Weather, index: number) => void;
    onToggle: (value: boolean) => void;
  }

  let { layer = $bindable(), mapConfigs, show, onSelect, onToggle }: Props = $props();
</script>

{#if mapConfigs.length === 0}
  <span class="text-accentText">No options available</span>
{:else}
  <div class="flex flex-col gap-sm w-max">
    <p class="font-semibold border-solid border-b border-border">Layers</p>
    <Toggle size="sm" checked={show} label="Show Label on Map" {onToggle} />
    <div class="grid grid-cols-2 gap-x-md gap-y-sm w-fit">
      {#each mapConfigs as option, index}
        <label
          title={option.displayName}
          class="inline-block cursor-pointer"
          class:checked={option.attributeType === layer}
        >
          <div class="flex gap-sm items-center">
            <span class="material-icons icon">{option.icon}</span>
            <p class="text-xs labelText">{option.displayName}</p>
            <input
              type="radio"
              bind:group={layer}
              value={option.attributeType}
              checked={option.attributeType === layer}
              onchange={() => onSelect(option.attributeType, index)}
            />
          </div>
        </label>
      {/each}
    </div>
  </div>
{/if}

<style>
  .checked .icon,
  .checked .labelText {
    color: var(--color-primary);
    -webkit-text-stroke-width: 0.5px;
  }
  input {
    display: none;
  }
</style>
