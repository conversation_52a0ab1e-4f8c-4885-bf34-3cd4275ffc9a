<script lang="ts">
  import { Toggle } from '$lib/components';

  interface Props {
    show: boolean;
    onToggle: (value: boolean) => void;
  }

  let { show, onToggle }: Props = $props();
</script>

<div class="flex justify-between">
  <div class="flex gap-sm items-center mr-8">
    <span class="material-icons !text-sm">engineering</span>
    <span>Active Resources</span>
  </div>
  <Toggle size="sm" checked={show} {onToggle} />
</div>
