<script lang="ts">
  import { onDestroy } from 'svelte';
  import mapboxgl from 'mapbox-gl';
  import { DEFAULT_MAP_UNITS_SYSTEM } from '$lib/config/map';

  interface Props {
    map?: mapboxgl.Map;
  }

  let { map }: Props = $props();

  const MAIN_PITCH = 45;
  let control: mapboxgl.ScaleControl | null = null;

  $effect(() => {
    const compass = new mapboxgl.NavigationControl({ showZoom: false, visualizePitch: true });
    compass._container.classList.add('compass');
    const tiltButton = document.createElement('button');
    tiltButton.textContent = '45\u00B0';
    tiltButton.style.color = 'var(--color-text)';
    tiltButton.style.backgroundColor = 'var(--color-component)';
    tiltButton.addEventListener('click', togglePitch);
    compass._container.appendChild(tiltButton);
    map?.addControl(new mapboxgl.ScaleControl({ unit: DEFAULT_MAP_UNITS_SYSTEM }), 'bottom-left');
    map?.addControl(compass, 'bottom-left');
  });

  function togglePitch() {
    if (!map) return;
    const pitch = Math.round(map.getPitch());
    if (pitch === 0) {
      map.easeTo({ pitch: MAIN_PITCH });
    } else if (pitch === MAIN_PITCH) {
      map.easeTo({ pitch: 0 });
    } else {
      const closest = Math.abs(pitch - MAIN_PITCH) < pitch ? MAIN_PITCH : 0;
      map.easeTo({ pitch: closest });
    }
  }

  onDestroy(() => {
    if (map && control) {
      map.removeControl(control);
    }
  });
</script>
