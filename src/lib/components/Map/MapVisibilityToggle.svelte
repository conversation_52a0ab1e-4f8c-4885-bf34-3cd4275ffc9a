<script lang="ts">
  import { Toggle } from '$lib/components';
  import type { MapItem, Visibility, OutageSource } from '$lib/types';

  interface Props {
    show: boolean;
    source?: OutageSource | null;
    visibility: Visibility;
    visibilityOptions?: MapItem[];
    onSelect: (value: Visibility) => void;
    onToggle: (value: boolean) => void;
  }

  let {
    show = false,
    source = null,
    visibility = $bindable(),
    visibilityOptions = [],
    onSelect,
    onToggle
  }: Props = $props();

  let filteredVisibilityOptions = $derived(
    visibilityOptions.filter((option) => option.source === source || !option.source)
  );
</script>

<div class="flex flex-col gap-md">
  {#if filteredVisibilityOptions.length === 0}
    <span class="text-accentText">No options available</span>
  {:else}
    <p class="font-semibold border-solid border-b border-border">Layers</p>
    <Toggle size="sm" checked={show} label="Show Label on Map" {onToggle} />
    <div class="flex items-center flex-wrap gap-md">
      {#each filteredVisibilityOptions as option}
        <label
          title={option.name}
          class="flex items-center !cursor-pointer"
          class:checked={option.attributeName === visibility}
        >
          <div class="flex flex-col items-center gap-sm">
            <span class="material-icons icon">{option.icon}</span>
            <p class="text-xs font-semibold labelText">
              {option.name}
            </p>
            <input
              type="radio"
              bind:group={visibility}
              value={option.attributeName}
              checked={option.attributeName === visibility}
              onchange={() => onSelect(option.attributeName)}
            />
          </div>
        </label>
      {/each}
    </div>
  {/if}
</div>

<style>
  .checked .icon,
  .checked .labelText {
    color: var(--color-primary);
    -webkit-text-stroke-width: 0.5px;
  }

  input {
    display: none;
  }
</style>
