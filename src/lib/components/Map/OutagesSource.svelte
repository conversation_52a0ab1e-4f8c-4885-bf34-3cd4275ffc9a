<script lang="ts">
  import type { OutageSource, MapOutageConfig } from '$lib/types';
  import store from '$lib/stores/app.svelte';
  import { capitalize } from '$lib/utils';
  import { Tooltip } from '$lib/components';

  interface Props {
    outagesSource: MapOutageConfig[];
    source: OutageSource;
    onSelect: (value: OutageSource) => void;
  }

  let { outagesSource = [], source = $bindable(), onSelect }: Props = $props();
</script>

<p class="font-semibold border-solid border-b border-border">
  {capitalize(store.outageLabel)} Data Source
</p>
<div class="flex flex-col gap-md">
  {#if outagesSource.length === 0}
    <span class="text-accentText">No options available</span>
  {:else}
    <div class="flex items-center flex-wrap gap-md">
      {#each outagesSource as option}
        <Tooltip>
          {#snippet content()}
            <div class="w-[180px]">
              {option.tooltip}
            </div>
          {/snippet}
          {#snippet trigger()}
            <label
              title={option.name}
              class="flex items-center !cursor-pointer"
              class:checked={option.attributeName === source}
            >
              <div class="flex flex-col items-center gap-sm">
                <span class="material-icons icon">{option.icon}</span>
                <p class="text-xs font-semibold labelText">
                  {option.name}
                </p>
                <input
                  type="radio"
                  bind:group={source}
                  value={option.attributeName}
                  checked={option.attributeName === source}
                  onchange={() => onSelect(option.attributeName)}
                />
              </div>
            </label>
          {/snippet}
        </Tooltip>
      {/each}
    </div>
  {/if}
</div>

<style>
  .checked .icon,
  .checked .labelText {
    color: var(--color-primary);
    -webkit-text-stroke-width: 0.5px;
  }

  input {
    display: none;
  }
</style>
