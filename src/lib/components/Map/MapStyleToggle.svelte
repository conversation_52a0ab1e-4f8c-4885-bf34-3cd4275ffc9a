<script lang="ts">
  import { MAP_STYLE_OPTIONS } from '$lib/config/map';
  import type { MapStyle } from '$lib/types';

  interface Props {
    mapStyle: MapStyle;
    onSelect: (value: MapStyle) => void;
  }

  let { mapStyle, onSelect }: Props = $props();
</script>

<div class="flex flex-col gap-sm">
  <p class="font-semibold border-solid border-b border-border">Map Styles</p>
  <div class="flex items-center gap-sm">
    <div class="flex items-center flex-wrap gap-md">
      {#each MAP_STYLE_OPTIONS as option}
        <label
          title={option.label}
          class="flex items-center !cursor-pointer"
          class:checked={option.value === mapStyle}
        >
          <div class="flex flex-col items-center gap-sm">
            <img class="icon border" alt={option.imgSrc} src={option.imgSrc} />
            <p class="labelText text-xs">
              {option.label.split(' ')[0]}
            </p>
            <input
              type="radio"
              bind:group={mapStyle}
              value={option.value}
              checked={option.value === mapStyle}
              onchange={() => onSelect(option.value)}
            />
          </div>
        </label>
      {/each}
    </div>
  </div>
</div>

<style>
  .border {
    border: 1px solid var(--boder-color);
    border-radius: var(--border-radius);
  }

  .checked .border {
    border: 2px solid var(--color-primary);
  }

  .checked .labelText {
    color: var(--color-primary);
    -webkit-text-stroke-width: 0.5px;
  }

  .icon {
    height: 1.8rem;
    width: 1.8rem;
    border-radius: var(--border-radius);
  }

  label:hover img,
  .checked img {
    cursor: pointer;
  }

  input {
    display: none;
  }

  @media (max-width: 540px) {
    .labelText {
      display: none;
    }

    .icon {
      height: 2rem;
      width: 2rem;
    }
  }
</style>
