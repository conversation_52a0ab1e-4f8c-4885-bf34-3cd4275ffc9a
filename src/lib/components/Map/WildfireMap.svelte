<script lang="ts">
  import { onD<PERSON>roy, onMount, type Snippet } from 'svelte';
  import { slide } from 'svelte/transition';
  import { base } from '$app/paths';
  import { DateTime } from 'luxon';
  import mapboxgl, { type Map } from 'mapbox-gl';
  import type { Feature, Point } from 'geojson';
  import LoaderCircle from 'lucide-svelte/icons/loader-circle';
  import {
    Loader,
    MapStyleToggle,
    ButtonGroup,
    Slider,
    Icon,
    Tooltip,
    DesktopView,
    MobileView
  } from '$lib/components';
  import { Button } from '../ui/button';
  import * as Select from '../ui/select';
  import { isDarkMode } from '$lib/stores/theme';
  import store from '$lib/stores/app.svelte';
  import { isSystemic } from '$lib/stores/constants';
  import { formatDate, formatTime } from '$lib/config/utils';
  import { getLatestWeatherData } from '$lib/api';
  import { client } from '$lib/stores/constants';
  import {
    MapStyle,
    type FireData,
    type RadarData,
    type WeatherData,
    type WeatherStation
  } from '$lib/types';
  import { fitMapToBounds, titleCase } from '$lib/utils';
  import { MAP_BOUNDS, DEFAULT_WIND_CONFIG, DEFAULT_RADAR_CONFIG } from '$lib/config/map';
  import RadarToggle from './RadarToggle.svelte';
  import SettingsPane from './SettingsPane.svelte';
  import MapLegend from './MapLegend.svelte';
  import DeviceIcons from '../DeviceIcon/DeviceIcons';
  import Switch from '../ui/switch/switch.svelte';
  import LayersPane from './LayersPane.svelte';
  import WeatherPane from './WeatherPane.svelte';
  import CompassControl from './CompassControl.svelte';
  import WindAnimationToggle from './WindAnimationToggle.svelte';
  import WindAnimation from './WindAnimation.svelte';

  interface Props {
    children?: Snippet;
  }

  let { children }: Props = $props();

  /* eslint-disable @typescript-eslint/no-magic-numbers */
  const FEEDER_COLOR_LIST = [
    1,
    '#E44126',
    2,
    '#fde96b',
    3,
    '#8ea757',
    4,
    '#03e6e6',
    5,
    '#5a8ee5',
    6,
    '#e600e6',
    7,
    '#FFFFFF',
    8,
    '#A9A9A9',
    10,
    '#DC143C',
    11,
    '#FA8072',
    12,
    '#CB4154',
    14,
    '#C0B0A0',
    30,
    '#FFA500',
    32,
    '#B5651D',
    34,
    '#A52A2A',
    86,
    '#195905',
    191,
    '#E6E6FA',
    226,
    '#673AB7'
  ];

  const NETWORK_FEEDER_COLOR = '#007AFF';

  const FIRMS_API_KEY = 'bcc98ccae9b8bebddfa4ae4df3d075d5';

  const baseFireForecastLayer = 'wms-fire-outlook-day-';
  const baseFireDangerLayer = 'fire-danger-';

  const DAYS_TO_SHOW = 5;

  const CONFIDENCE_MAP = {
    n: 'Nominal',
    l: 'Low',
    h: 'HIGH'
  } as const;

  const FIRE_POTENTIAL_LEGEND = [
    { color: '#888888', text: '', tooltip: 'This data is unavailable' },
    { color: '#84ca0a', text: '', tooltip: 'Little or no risk for large fires' },
    {
      color: '#ffff00',
      text: '',
      tooltip: 'Low risk of large fires in the absence of a "High Risk" event'
    },
    {
      color: '#ad7c23',
      text: '',
      tooltip: 'Moderate risk of large fires in the absence of a "High Risk" event'
    },
    {
      color: '#ff9900',
      text: 'W',
      tooltip:
        'Critical wind speeds and low relative humidity resulting in an elevated chance for significant fires and the potential for significant fire growth on existing fires'
    },
    {
      color: '#ff9900',
      text: 'B',
      tooltip:
        'Burn Environment – An elevated chance of significant fires and potential for large fire growth due to either hot temperatures, instability, and wind, or a combination of these factors'
    },
    {
      color: '#ff0000',
      text: 'L',
      tooltip: 'Lightning – A greater than 20% chance of significant fires due to thunderstorms'
    },
    {
      color: '#ff0000',
      text: 'R',
      tooltip:
        'Recreation – A greater than 20% chance of significant fires due to human activity and very dry background conditions'
    }
  ] as const;

  // Layers
  const WILDFIRE_CIRCLE = 'wildfire_circle';
  const WILDFIRE_CLUSTER = 'wildfire_cluster';
  const DEVICES = 'devices';
  const NETWORK_DISTRIBUTION = 'network_distribution';

  const popup = new mapboxgl.Popup({
    closeButton: false,
    closeOnClick: false
  });

  let loading: boolean = false;
  let isMapLoaded = $state(false);
  let isLoadingWeather = $state(true);
  let isLoadingWildfireData = $state(true);

  let mapContainer: HTMLDivElement | undefined = $state();
  let map: Map | undefined = $state();
  let mapStyle: MapStyle = $state(MapStyle.streetSatellite);
  let mapSource: 'regions' | 'larger-regions' = $state(!isSystemic ? 'regions' : 'larger-regions');

  let isActiveWildfiresVisible: boolean = $state(false);
  let fireData: FireData | null = $state(null);

  let isRadarVisible: boolean = $state(false);
  let radarData: WeatherData | null = $state(null);
  let mapFrames: RadarData[] = $state([]);
  let frameIndex: number = $state(0);
  let radarOpacity = $state(DEFAULT_RADAR_CONFIG.opacity);
  let weatherStations: WeatherStation[] = $state([]);

  let isWindAnimationVisible = $state(false);
  let particleCount = $state<number>(DEFAULT_WIND_CONFIG.particleCount);
  let particleSize = $state<number>(DEFAULT_WIND_CONFIG.particleSize);
  let particleLifespan = $state<number>(DEFAULT_WIND_CONFIG.particleLifespan);
  let speedFactor = $state<number>(DEFAULT_WIND_CONFIG.speedFactor);
  let overallOpacity = $state<number>(DEFAULT_WIND_CONFIG.overallOpacity);

  let isNetworkLayerChecked = $state(!!store.appConfig?.networkDistributionUrl);
  let isDeviceLayerChecked = $state(!!store.appConfig?.devicesUrl);
  let networkLayerTheme: 'default' | 'network' = $state('default');

  let hoveredAsset: string | null | undefined | number = $state(null);
  let colorScheme: string = $state(DEFAULT_RADAR_CONFIG.colorScheme);
  let showTerritoryBounds = $state(true);

  let selectedForecastIndex: number = $state(0);

  let isFirePotentialLayerActive: boolean = $state(true);
  let firePotentialLayerId: string = $state(`${baseFireForecastLayer}0`);
  let firePotentialOpacity = $state(0.5);

  let fireDangerLayerActive: boolean = $state(false);
  let fireDangerLayerId: string = $state(`${baseFireDangerLayer}0`);
  let fireDangerOpacity = $state(0.5);

  let isLoadingForecasts = $state(true);

  const onWatchThemeMode = isDarkMode.subscribe((isDark) => {
    if (map) {
      if (mapStyle === MapStyle.dark || mapStyle === MapStyle.light) {
        mapStyle = isDark ? MapStyle.dark : MapStyle.light;
        changeMapStyle();
      }
    }
  });

  onMount(() => {
    initialize();
  });

  onDestroy(() => {
    map?.off('load', onMapLoad);
    map?.off('mousemove', DEVICES, onMouseMoveDevice);
    map?.off('mouseleave', DEVICES, onMouseLeaveDevice);
    map?.off('mousemove', WILDFIRE_CIRCLE, onMouseMoveWildfire);
    map?.off('mouseleave', WILDFIRE_CIRCLE, onMouseLeaveWildfire);
    map?.off('resize', () => fitMapToBounds(map, store.appConfig?.maxBounds?.value));
    onWatchThemeMode();
  });

  function initialize() {
    if (!store.appConfig || !mapContainer) return;
    map = new mapboxgl.Map({
      center: store.appConfig.center.value,
      container: mapContainer,
      style: `mapbox://styles/mapbox/${mapStyle}`,
      maxBounds: MAP_BOUNDS
    });
    map.on('load', onMapLoad);
    map.on('render', onMapResize);
    map.on('resize', () => fitMapToBounds(map, store.appConfig?.maxBounds?.value));
    map.on('mousemove', DEVICES, onMouseMoveDevice);
    map.on('mouseleave', DEVICES, onMouseLeaveDevice);

    map.on('mousemove', WILDFIRE_CIRCLE, onMouseMoveWildfire);
    map.on('mouseleave', WILDFIRE_CIRCLE, onMouseLeaveWildfire);
  }

  function onMapResize() {
    map?.resize();
  }

  function onMapLoad() {
    if (!store.appConfig || !map) return;
    addMapSource();
    addNetworkDistributionSource();
    addDeviceSource();
    fetchWeatherData();
    fetchRadarData();
    fetchWildfireData();
    fitMapToBounds(map, store.appConfig.maxBounds?.value);

    // Layer Ordering (Important)
    addForecastLayers();
    addNetworkDistributionLayer();
    addDeviceLayer();
    addRegionBorderLayer();
    isMapLoaded = true;
  }

  function addMapSource() {
    if (!store.appConfig) return;
    map?.addSource(mapSource, {
      promoteId: 'name',
      type: 'vector',
      url: store.appConfig.tilesetUrl.value
    });
  }

  function addRegionBorderLayer() {
    if (!store.appConfig) return;

    map?.addLayer({
      id: 'region-fill',
      type: 'fill',
      source: mapSource,
      'source-layer': mapSource,
      paint: {
        'fill-color': '#888',
        'fill-opacity': 0.2
      }
    });

    map?.addLayer({
      ...store.appConfig.regionBorderLayer,
      type: 'line',
      source: mapSource,
      'source-layer': mapSource
    });
  }

  function addNetworkDistributionSource() {
    if (!store.appConfig) return;
    map?.addSource(NETWORK_DISTRIBUTION, {
      type: 'vector',
      url: store.appConfig?.networkDistributionUrl?.value
    });
  }

  function addNetworkDistributionLayer() {
    if (map?.getLayer(NETWORK_DISTRIBUTION)) return;
    map?.addLayer({
      'source-layer': NETWORK_DISTRIBUTION,
      id: NETWORK_DISTRIBUTION,
      type: 'line',
      source: NETWORK_DISTRIBUTION,
      paint: {
        'line-color':
          networkLayerTheme === 'default'
            ? ['match', ['get', 'color'], ...FEEDER_COLOR_LIST, '#fff']
            : NETWORK_FEEDER_COLOR,
        'line-width': ['interpolate', ['linear'], ['zoom'], 10, 2, 20, 5],
        'line-dasharray': [
          'step',
          ['zoom'],
          [1, 0],
          15,
          ['match', ['get', 'type'], 'ug', [2, 2], 'oh', [1, 0], [1, 0]]
        ]
      }
    });
  }

  function addDeviceSource() {
    if (!store.appConfig) return;

    map?.addSource(DEVICES, {
      type: 'vector',
      url: store.appConfig?.devicesUrl?.value,
      promoteId: 'device_id'
    });
  }

  function addDeviceLayer() {
    if (map?.getLayer(DEVICES)) return;

    Object.keys(DeviceIcons).forEach((icon_name) => {
      const icon_src = DeviceIcons[icon_name];

      map?.loadImage(icon_src, (error, image) => {
        if (error) throw error;

        if (!map?.hasImage(icon_name) && image) {
          map?.addImage(icon_name, image, { sdf: true });
        }
      });
    });

    map?.addLayer({
      source: DEVICES,
      'source-layer': DEVICES,
      id: DEVICES,
      type: 'symbol',
      minzoom: 13,
      layout: {
        'icon-image': ['coalesce', ['get', 'icon'], 'point'],
        'icon-allow-overlap': true,
        'icon-size': ['interpolate', ['linear'], ['zoom'], 13, 0.1, 14, 0.15, 15, 0.4, 16, 0.6]
      },
      paint: {
        'icon-color': $isDarkMode ? '#fff' : '#333',
        'icon-halo-width': [
          'case',
          [
            'any',
            ['boolean', ['feature-state', 'hover'], false],
            ['boolean', ['feature-state', 'selected'], false]
          ],
          3,
          0
        ],
        'icon-halo-color': 'rgba(255,215,0,0.5)'
      },
      filter: ['!=', ['get', 'type'], 'unknown']
    });
  }

  function onChangeTheme() {
    if (!store.appConfig) return;
    addMapSource();
    addNetworkDistributionSource();
    addWildfireSource();
    addDeviceSource();

    if (isNetworkLayerChecked) addNetworkDistributionLayer();
    if (isActiveWildfiresVisible) addWildfireLayer();
    if (isDeviceLayerChecked) addDeviceLayer();

    if (isRadarVisible) {
      radarData ? showFrame() : fetchRadarData();
    }

    if (showTerritoryBounds) {
      addRegionBorderLayer();
    }
  }

  function changeMapStyle() {
    if (!map?.getStyle()?.sprite?.includes(`/${mapStyle}`)) {
      map?.once('style.load', onChangeTheme);
      map?.setStyle(`mapbox://styles/mapbox/${mapStyle}`);
    }
  }

  function fetchRadarData() {
    fetch(DEFAULT_RADAR_CONFIG.radarUrl)
      .then((response) => {
        if (!response.ok) {
          throw new Error('Network response error');
        }
        return response.json();
      })
      .then((data) => {
        if (data) {
          radarData = data;
          mapFrames = [...(data.radar?.past ?? [])];
          frameIndex = data?.radar?.past?.length - 1;
          showFrame();
        }
      })
      .catch(() => {
        console.error('Failed to fetch radar data');
      });
  }

  async function fetchWeatherData() {
    if (weatherStations.length > 0) return;
    isLoadingWeather = true;
    try {
      const weatherData = await getLatestWeatherData();
      weatherStations = Object.entries(weatherData.weatherStations).map(([id, station]) => ({
        id,
        ...station,
        windBearing: station.weatherAttributes.windBearingDeg,
        windSpeed: station.weatherAttributes.windSpeedMph
      }));
    } catch (error) {
      console.error('Failed to fetch weather data:', error);
    } finally {
      isLoadingWeather = false;
    }
  }

  function showFrame() {
    addRadarLayer(mapFrames[frameIndex].path);
  }

  function addRadarLayer(path: string) {
    if (radarData) {
      const url = `${radarData.host}${path}/256/{z}/{x}/{y}/${colorScheme}/1_1.png`;
      map?.addSource(path, {
        type: 'raster',
        tiles: [url]
      });

      map?.addLayer({
        id: path,
        type: 'raster',
        source: path,
        layout: {
          visibility: isRadarVisible ? 'visible' : 'none'
        },
        paint: {
          'raster-opacity': radarOpacity
        }
      });
    }
  }

  function onUpdateRadarLayer() {
    map?.setLayoutProperty(
      mapFrames[frameIndex].path,
      'visibility',
      isRadarVisible ? 'visible' : 'none'
    );
  }

  function onUpdateColor(value: string) {
    colorScheme = value;
    mapFrames.forEach((i) => {
      if (map?.getSource(i.path)) {
        map.removeLayer(i.path);
        map.removeSource(i.path);
      }
    });
    showFrame();
  }

  function onMouseMoveDevice(e: any) {
    if (!map) return;

    const id = e?.features?.[0].id;
    const coordinates = e.lngLat;
    const props = e?.features?.[0]?.properties;

    if (id && e?.features?.length) {
      if (e.features.length > 0) {
        if (hoveredAsset != null) {
          map.setFeatureState(
            { source: DEVICES, sourceLayer: DEVICES, id: hoveredAsset },
            { hover: false }
          );
        }
        hoveredAsset = e.features[0].id;
        if (hoveredAsset != null) {
          map.setFeatureState(
            { source: DEVICES, sourceLayer: DEVICES, id: hoveredAsset },
            { hover: true }
          );
        }
      }
    }

    popup
      .setLngLat(coordinates)
      .setHTML(
        `<div class="popup-region-info">
          <p class="popup-feature-name !normal-case">Device: ${props?.device}</p>
          <div class="info-row">
            <span class="label">Type:</span>
            <span class="value">${titleCase(props?.type)}</span>
          </div>
          <div class="info-row">
            <span class="label">Substation:</span>
            <span class="value">${props?.substation}</span>
          </div>
          <div class="info-row">
            <span class="label">Feeder:</span>
            <span class="value">${props?.feeder}</span>
          </div>
          <div class="info-row">
            <span class="label">PD Area:</span>
            <span class="value">${props?.pd_area}</span>
          </div>
          <div class="info-row">
            <span class="label">Workgroup:</span>
            <span class="value">${props?.workgroup}</span>
          </div>
          <div class="info-row">
            <span class="label">Tags:</span>
            <span class="value">${props?.tags.split(',').join(', ')}</span>
          </div>
        </div>`
      )
      .addTo(map);

    map.getCanvas().style.cursor = 'pointer';
  }

  function onMouseMoveWildfire(e: any) {
    if (!map) return;

    const id = e?.features?.[0].id;
    const coordinates = e.lngLat;
    const props = e?.features?.[0]?.properties;

    if (id && e?.features?.length) {
      if (e.features.length > 0) {
        if (hoveredAsset != null) {
          map.setFeatureState(
            { source: WILDFIRE_CIRCLE, sourceLayer: WILDFIRE_CIRCLE, id: hoveredAsset },
            { hover: false }
          );
        }
        hoveredAsset = e.features[0].id;
        if (hoveredAsset != null) {
          map.setFeatureState(
            { source: WILDFIRE_CIRCLE, sourceLayer: WILDFIRE_CIRCLE, id: hoveredAsset },
            { hover: true }
          );
        }
      }
    }

    popup
      .setLngLat(coordinates)
      .setHTML(
        `<div class="popup-region-info">
          <p class="popup-feature-name !normal-case">Fire Detection</p>
          <div class="info-row">
            <span class="label">Date:</span>
            <span class="value">${formatDate(props?.acq_date)}</span>
          </div>
          <div class="info-row">
            <span class="label">Time:</span>
            <span class="value">${formatTime(props?.acq_time)}</span>
          </div>
          <div class="info-row">
            <span class="label">Brightness:</span>
            <span class="value">${props?.bright_ti4 ?? '-'} K</span>
          </div>
          <div class="info-row">
            <span class="label">Fire Radiative Power:</span>
            <span class="value">${parseFloat(props.frp) ?? '-'} MW</span>
          </div>
          <div class="info-row">
            <span class="label">Confidence:</span>
            <span class="value">${CONFIDENCE_MAP[props?.confidence as keyof typeof CONFIDENCE_MAP] ?? 'N/A'}</span>
          </div>
        </div>`
      )
      .addTo(map);

    map.getCanvas().style.cursor = 'pointer';
  }

  function onMouseLeaveDevice() {
    if (!map) return;

    if (hoveredAsset !== null && hoveredAsset !== undefined) {
      map.setFeatureState(
        { source: DEVICES, sourceLayer: DEVICES, id: hoveredAsset },
        { hover: false }
      );
    }

    popup.remove();
    map.getCanvas().style.cursor = 'default';
  }

  function onMouseLeaveWildfire() {
    if (!map) return;

    if (hoveredAsset !== null && hoveredAsset !== undefined) {
      map.setFeatureState(
        { source: WILDFIRE_CIRCLE, sourceLayer: WILDFIRE_CIRCLE, id: hoveredAsset },
        { hover: false }
      );
    }

    popup.remove();
    map.getCanvas().style.cursor = 'default';
  }

  async function fetchWildfireData() {
    try {
      const response = await fetch(
        `https://firms.modaps.eosdis.nasa.gov/api/area/csv/${FIRMS_API_KEY}/VIIRS_SNPP_NRT/${store.appConfig?.maxBounds.value}/3`
      );

      const csvData = await response.text();

      const lines = csvData.split('\n');
      const features = [];

      const headers = lines[0].split(',');

      for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line) {
          const values = line.split(',');

          const feature: Feature<Point, { [key: string]: any; id: string }> = {
            type: 'Feature' as const,
            geometry: {
              type: 'Point' as const,
              coordinates: [parseFloat(values[1]), parseFloat(values[0])]
            },
            properties: {
              id: `fire-${i}`
            }
          };

          headers.forEach((header, index) => {
            if (index < values.length) {
              feature.properties[header] = values[index];
            }
          });

          if (feature.properties.bright_ti4) {
            feature.properties.brightness = parseFloat(feature.properties.bright_ti4);
          }

          features.push(feature);
        }
      }

      fireData = {
        type: 'FeatureCollection',
        features
      };

      isActiveWildfiresVisible = selectedForecastIndex === 0;

      addWildfireSource();
      addWildfireLayer();
    } catch (error) {
      console.error('Error fetching wildfire data:', error);
    } finally {
      isLoadingWildfireData = false;
    }
  }

  function addWildfireSource() {
    if (fireData)
      map?.addSource('wildfires', {
        type: 'geojson',
        data: fireData,
        cluster: false
      });
  }

  function addWildfireLayer() {
    if (map?.getSource('wildfires')) {
      map.addLayer({
        id: WILDFIRE_CLUSTER,
        type: 'heatmap',
        source: 'wildfires',
        maxzoom: 9.2,
        paint: {
          'heatmap-weight': ['interpolate', ['linear'], ['get', 'brightness'], 300, 0, 330, 1],
          'heatmap-intensity': ['interpolate', ['linear'], ['zoom'], 0, 0.5, 9, 2],
          'heatmap-color': [
            'interpolate',
            ['linear'],
            ['heatmap-density'],
            0,
            'rgba(0, 0, 0, 0)',
            0.1,
            'rgba(128, 0, 0, 0.4)',
            0.3,
            'rgba(255, 0, 0, 0.6)',
            0.5,
            'rgba(255, 85, 0, 0.7)',
            0.7,
            'rgba(255, 140, 0, 0.8)',
            1,
            'rgba(255, 200, 0, 0.85)'
          ],
          'heatmap-radius': ['interpolate', ['linear'], ['zoom'], 0, 2, 9, 20],
          'heatmap-opacity': ['interpolate', ['linear'], ['zoom'], 8.8, 1, 9.2, 0]
        }
      });

      map.addLayer({
        id: WILDFIRE_CIRCLE,
        type: 'circle',
        source: 'wildfires',
        minzoom: 8.8,
        paint: {
          'circle-radius': ['interpolate', ['linear'], ['get', 'brightness'], 295, 4, 335, 10],
          'circle-color': [
            'interpolate',
            ['linear'],
            ['get', 'brightness'],
            295,
            '#ffff00',
            310,
            '#ff9900',
            335,
            '#ff0000'
          ],
          'circle-opacity': ['interpolate', ['linear'], ['zoom'], 8.8, 0, 9.2, 1],
          'circle-stroke-width': 1,
          'circle-stroke-color': '#000'
        }
      });
    }
  }

  function onToggleWindAnimation(value: boolean) {
    isWindAnimationVisible = value;
  }

  function addFireDangerLayer(dayIndex: number, time: string) {
    if (!map) return;

    const id = baseFireDangerLayer + dayIndex;
    if (map.getSource(id)) return;

    const formattedTime = DateTime.fromISO(time, { zone: 'utc' }).toFormat('yyyy-MM-dd');

    // Layer names are 1-indexed, so we add 2 to the dayIndex
    const dayLayerIndex = dayIndex + 2;
    const layerName = `firedanger_wfpi-forecast-${dayLayerIndex}_conus_day_data:wfpi-forecast-${dayLayerIndex}_conus_day_data`;
    const jsonLayerId = `wfpi_forecast_${dayLayerIndex}`;

    const tileUrl =
      'https://dmsdata.cr.usgs.gov/geoserver/wms?' +
      'REQUEST=GetMap&SERVICE=WMS&VERSION=1.3.0&FORMAT=image%2Fpng' +
      '&STYLES=firedanger_daily_wfpi_conus_raster&TRANSPARENT=true' +
      `&LAYERS=${encodeURIComponent(layerName)}` +
      '&TILED=true&SRS=EPSG%3A3857' +
      `&jsonLayerId=${jsonLayerId}` +
      `&TIME=${formattedTime}` +
      '&WIDTH=256&HEIGHT=256&CRS=EPSG%3A3857&BBOX={bbox-epsg-3857}';

    map.addSource(id, {
      type: 'raster',
      tiles: [tileUrl],
      tileSize: 256
    });

    map.addLayer({
      id,
      type: 'raster',
      source: id,
      layout: {
        visibility: 'none'
      },
      paint: {
        'raster-opacity': fireDangerOpacity
      }
    });
  }

  function addForecastLayer(dayIndex: number, timeString: string) {
    if (!map) return;

    const id = baseFireForecastLayer + dayIndex;
    if (map.getSource(id)) return;

    const tileUrl =
      `https://fsapps.nwcg.gov/psp/arcgis/services/npsg/current_forecast/MapServer/WMSServer?` +
      `service=WMS&request=GetMap&layers=0&styles=&format=image/png&transparent=true&version=1.1.1` +
      `&height=512&width=512&TIME=${timeString}&srs=EPSG:3857&bbox={bbox-epsg-3857}`;

    map.addSource(id, {
      type: 'raster',
      tiles: [tileUrl],
      tileSize: 512
    });

    map.addLayer({
      id,
      type: 'raster',
      source: id,
      layout: {
        visibility: 'none'
      },
      paint: { 'raster-opacity': firePotentialOpacity }
    });
  }

  async function addForecastLayers() {
    if (!map) return;

    isLoadingForecasts = true;

    for (let i = 0; i < DAYS_TO_SHOW; i++) {
      const time = DateTime.utc().plus({ days: i }).startOf('day').toISO();
      const id = baseFireForecastLayer + i;

      addForecastLayer(i, time);
      addFireDangerLayer(i, time);

      if (i === 0) {
        firePotentialLayerId = id;
        map.setLayoutProperty(id, 'visibility', 'visible');
      }
    }

    await map.once('idle');

    isLoadingForecasts = false;
  }

  function onChangeForecastDate() {
    if (!map) return;

    const isToday = selectedForecastIndex === 0;

    if (isToday !== isActiveWildfiresVisible) {
      isActiveWildfiresVisible = isToday;

      map?.setLayoutProperty(WILDFIRE_CLUSTER, 'visibility', isToday ? 'visible' : 'none');
      map?.setLayoutProperty(WILDFIRE_CIRCLE, 'visibility', isToday ? 'visible' : 'none');
    }

    map.setLayoutProperty(firePotentialLayerId, 'visibility', 'none');
    map.setLayoutProperty(fireDangerLayerId, 'visibility', 'none');

    firePotentialLayerId = `${baseFireForecastLayer}${selectedForecastIndex}`;
    fireDangerLayerId = `${baseFireDangerLayer}${selectedForecastIndex}`;

    if (isFirePotentialLayerActive) {
      map.setLayoutProperty(firePotentialLayerId, 'visibility', 'visible');
    }

    if (fireDangerLayerActive) {
      map.setLayoutProperty(fireDangerLayerId, 'visibility', 'visible');
    }
  }
</script>

<div
  class="wildfire-map relative w-full h-full rounded-md overflow-hidden border-solid border !border-borderColor shadow-md"
>
  {#if isMapLoaded}
    <SettingsPane>
      <MapStyleToggle
        {mapStyle}
        onSelect={(value) => {
          mapStyle = value;
          changeMapStyle();
        }}
      />
      {#if !isSystemic}
        <div class="flex flex-col gap-sm">
          <div
            class="border-solid border-b border-border flex items-center justify-between min-w-[225px]"
          >
            <p class="font-semibold">Territory Bounds</p>
            <Switch
              size="sm"
              bind:checked={showTerritoryBounds}
              onCheckedChange={(value) => {
                showTerritoryBounds = value;
                if (showTerritoryBounds) {
                  addRegionBorderLayer();
                } else {
                  map?.removeLayer('region-fill');
                  map?.removeLayer('region-borders');
                }
              }}
            />
          </div>

          {#if showTerritoryBounds}
            <div transition:slide>
              <ButtonGroup
                bind:value={mapSource}
                size="xs"
                variant="ghost"
                onClick={() => {
                  map?.removeLayer('region-borders');
                  if (!map?.getSource(mapSource)) {
                    addMapSource();
                  }
                  addRegionBorderLayer();
                }}
              >
                <Button value="larger-regions">{store.regionsLabel}</Button>
                <Button value="regions">Workgroups</Button>
              </ButtonGroup>
            </div>
          {/if}
        </div>
      {/if}
    </SettingsPane>

    <LayersPane>
      <div class="flex gap-md flex-col w-[335px]">
        <div class="flex flex-col gap-sm">
          <p class="font-semibold border-solid border-b border-border">
            Strategic Areas and Assets
          </p>
          {#if store.appConfig?.networkDistributionUrl}
            <div class="flex justify-between gap-md">
              <div class="flex gap-sm items-center">
                <span class="material-icons !text-sm">hub</span>
                <span>Distribution</span>
              </div>
              <Switch
                size="sm"
                bind:checked={isNetworkLayerChecked}
                onCheckedChange={(checked) =>
                  map?.setLayoutProperty(
                    NETWORK_DISTRIBUTION,
                    'visibility',
                    checked ? 'visible' : 'none'
                  )}
              />
            </div>

            {#if isNetworkLayerChecked}
              <div transition:slide class="ml-md bg-muted/50 p-sm rounded-md border border-border">
                <p class="text-accentText text-xs mb-xs">Visualization</p>
                <ButtonGroup
                  bind:value={networkLayerTheme}
                  size="xs"
                  variant="ghost"
                  onClick={() => {
                    map?.setPaintProperty(
                      NETWORK_DISTRIBUTION,
                      'line-color',
                      networkLayerTheme === 'default'
                        ? ['match', ['get', 'color'], ...FEEDER_COLOR_LIST, '#fff']
                        : NETWORK_FEEDER_COLOR
                    );
                  }}
                >
                  <Button value="default">{client?.toUpperCase()} Default</Button>
                  <Button value="network">Network</Button>
                </ButtonGroup>
              </div>
            {/if}
          {/if}
          {#if store.appConfig?.devicesUrl}
            <div class="flex justify-between gap-md">
              <div class="flex gap-sm items-center">
                <span class="material-icons !text-sm">sensors</span>
                <span>Devices</span>
              </div>
              <Switch
                size="sm"
                bind:checked={isDeviceLayerChecked}
                onCheckedChange={(checked) => {
                  map?.setLayoutProperty(DEVICES, 'visibility', checked ? 'visible' : 'none');
                }}
              />
            </div>
          {/if}
          <div class="flex justify-between gap-md">
            <div class="flex gap-sm items-center">
              <Icon name="pole" />
              <span>Poles</span>
            </div>
            <Switch size="sm" disabled />
          </div>
          <div class="flex justify-between gap-md">
            <div class="flex gap-sm items-center">
              <Icon name="transmission" />
              <span>Transmission</span>
            </div>
            <Switch size="sm" disabled />
          </div>
        </div>
        <div class="flex flex-col gap-sm">
          <p class="font-semibold border-solid border-b border-border">Wildfire and Alerts</p>
          <div class="flex justify-between gap-md">
            <div class="flex gap-sm items-center">
              <span class="material-icons !text-sm">emergency_heat</span>
              <span>Active Fires (FIRMS)</span>
            </div>
            <Switch
              size="sm"
              bind:checked={isActiveWildfiresVisible}
              onCheckedChange={(checked) => {
                map?.setLayoutProperty(
                  WILDFIRE_CLUSTER,
                  'visibility',
                  checked ? 'visible' : 'none'
                );
                map?.setLayoutProperty(WILDFIRE_CIRCLE, 'visibility', checked ? 'visible' : 'none');
              }}
              disabled={!fireData || selectedForecastIndex !== 0}
            />
          </div>
          <div class="flex flex-col gap-sm">
            <div class="flex justify-between gap-md">
              <div class="flex gap-sm items-center">
                <span class="material-icons !text-sm">crisis_alert</span>
                <span>Fire Danger Forecast (WFPI)</span>
              </div>
              <Switch
                size="sm"
                bind:checked={fireDangerLayerActive}
                onCheckedChange={(checked) => {
                  if (checked) {
                    map?.setLayoutProperty(fireDangerLayerId, 'visibility', 'visible');
                  } else {
                    map?.setLayoutProperty(fireDangerLayerId, 'visibility', 'none');
                  }
                }}
              />
            </div>
            {#if fireDangerLayerActive}
              <div
                transition:slide
                class="flex flex-col gap-sm ml-md bg-muted/50 p-sm rounded-md border border-border"
              >
                <div class="w-full">
                  <span class="text-xs text-accentText"> Risk Legend</span>
                  <img
                    class="w-full h-[15px]"
                    src={`${base}/images/fire_danger.png`}
                    alt="Fire Danger Legend"
                  />
                  <div class="flex justify-between gap-md text-xs text-accentText">
                    <span>0</span>
                    <span>151</span>
                  </div>
                </div>
                <Slider
                  label="Opacity ({(fireDangerOpacity * 100).toFixed(0)})"
                  max={1}
                  maxLabel="100"
                  min={0}
                  minLabel="0"
                  step={0.05}
                  bind:value={fireDangerOpacity}
                  onChange={() => {
                    map?.setPaintProperty(fireDangerLayerId, 'raster-opacity', fireDangerOpacity);
                  }}
                />
              </div>
            {/if}
          </div>
          <div class="flex flex-col gap-sm">
            <div class="flex justify-between gap-md">
              <div class="flex gap-sm items-center">
                <span class="material-icons !text-sm">emergency_heat_2</span>
                <span>Significant Fire Potential (NICC)</span>
              </div>
              <Switch
                size="sm"
                bind:checked={isFirePotentialLayerActive}
                onCheckedChange={(checked) => {
                  if (checked) {
                    map?.setLayoutProperty(firePotentialLayerId, 'visibility', 'visible');
                    if (map?.getLayer(NETWORK_DISTRIBUTION)) {
                      map?.moveLayer(firePotentialLayerId, NETWORK_DISTRIBUTION);
                    }
                  } else {
                    map?.setLayoutProperty(firePotentialLayerId, 'visibility', 'none');
                  }
                }}
              />
            </div>
            {#if isFirePotentialLayerActive}
              <div
                transition:slide
                class="flex flex-col gap-sm ml-md bg-muted/50 p-sm rounded-md border border-border"
              >
                <div class="flex flex-col gap-xs">
                  <span class="text-xs text-accentText">Legend</span>
                  <div class="flex gap-sm">
                    {#each FIRE_POTENTIAL_LEGEND as item}
                      <Tooltip delayDuration={0} disableHoverableContent>
                        {#snippet content()}
                          <span>{item.tooltip}</span>
                        {/snippet}
                        {#snippet trigger()}
                          <div
                            class="h-5 w-[30px] rounded-sm border border-border text-xs text-center text-black"
                            style="background-color: {item.color}"
                          >
                            {item.text}
                          </div>
                        {/snippet}
                      </Tooltip>
                    {/each}
                  </div>
                </div>
                <Slider
                  label="Opacity ({(firePotentialOpacity * 100).toFixed(0)})"
                  max={1}
                  maxLabel="100"
                  min={0}
                  minLabel="0"
                  step={0.05}
                  bind:value={firePotentialOpacity}
                  onChange={() => {
                    map?.setPaintProperty(
                      firePotentialLayerId,
                      'raster-opacity',
                      firePotentialOpacity
                    );
                  }}
                />
              </div>
            {/if}
          </div>
        </div>
      </div>
    </LayersPane>
    <WeatherPane>
      <RadarToggle
        bind:show={isRadarVisible}
        onSelect={onUpdateRadarLayer}
        {colorScheme}
        bind:opacity={radarOpacity}
        {onUpdateColor}
        onSliderChange={() => {
          map?.setPaintProperty(mapFrames[frameIndex].path, 'raster-opacity', radarOpacity);
        }}
      />

      <WindAnimationToggle
        show={isWindAnimationVisible}
        {onToggleWindAnimation}
        bind:particleCount
        bind:particleSize
        bind:particleLifespan
        bind:speedFactor
        bind:overallOpacity
      />
    </WeatherPane>
  {/if}

  {#if typeof window !== 'undefined'}
    <div bind:this={mapContainer} class="mapbox-map w-full h-full absolute top-0 bottom-0"></div>
  {/if}
  {@render children?.()}

  <MapLegend {map} />
  <CompassControl {map} />

  {#if isWindAnimationVisible && map && !isLoadingWeather}
    <WindAnimation
      {map}
      {weatherStations}
      {particleCount}
      {particleSize}
      {particleLifespan}
      {speedFactor}
      {overallOpacity}
    />
  {/if}

  {#if isLoadingWildfireData}
    <div
      class="absolute bottom-4 right-4 z-50 flex items-center gap-2 rounded-md border border-solid border-border p-sm bg-muted text-sm text-muted-foreground text-wrap text-center"
    >
      <LoaderCircle class="h-4 w-4 animate-spin" />
      <span>Loading wildfire data...</span>
    </div>
  {/if}

  <div
    class="absolute inset-0 flex items-center rounded-md justify-center bg-component !w-full {loading
      ? 'flex'
      : 'hidden'}"
  >
    <Loader />
  </div>

  <DesktopView>
    <ButtonGroup
      class="absolute top-sm left-16"
      bind:value={selectedForecastIndex}
      onClick={onChangeForecastDate}
      variant="outline"
      disabled={isLoadingForecasts}
    >
      <!--eslint-disable @typescript-eslint/no-unused-vars -->
      {#each Array(DAYS_TO_SHOW) as _, i (i)}
        <Button value={i}>
          <div class="flex flex-col text-center gap-xs">
            <span class="text-xs">
              {DateTime.utc().plus({ days: i }).toFormat('ccc, LLL d')}
            </span>
          </div>
        </Button>
      {/each}
    </ButtonGroup>
  </DesktopView>

  <MobileView>
    <Select.Root
      type="single"
      value={selectedForecastIndex.toString()}
      onValueChange={(val: string) => {
        selectedForecastIndex = +val;
        onChangeForecastDate();
      }}
      disabled={isLoadingForecasts}
    >
      <Select.Trigger class="absolute w-44 top-sm left-16">
        {DateTime.utc().plus({ days: selectedForecastIndex }).toFormat('cccc, LLLL d')}
      </Select.Trigger>
      <Select.Content>
        <!--eslint-disable @typescript-eslint/no-unused-vars -->
        {#each Array(DAYS_TO_SHOW) as _, i (i)}
          <Select.Item value={String(i)}>
            {DateTime.utc().plus({ days: i }).toFormat('ccc, LLL d')}
          </Select.Item>
        {/each}
      </Select.Content>
    </Select.Root>
  </MobileView>
</div>

<style>
  :global(.wildfire-map .mapboxgl-ctrl-legend) {
    display: none;
  }
</style>
