<script lang="ts">
  import type { Snippet } from 'svelte';

  interface Props {
    class?: string;
    children: Snippet;
  }

  let { class: className = '', children }: Props = $props();
</script>

<div class="mobile-view {className}">
  {@render children()}
</div>

<style>
  .mobile-view {
    display: none;
  }

  @media (max-width: 767px) {
    .mobile-view {
      display: flex;
    }
  }
</style>
