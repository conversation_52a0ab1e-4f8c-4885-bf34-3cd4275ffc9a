<script lang="ts">
  import { base } from '$app/paths';
  import { page } from '$app/state';
  import { Icon } from '$lib/components';
  import { Button } from '$lib/components/ui/button';
  import { isDarkMode } from '$lib/stores/theme';
  import store from '$lib/stores/app.svelte';
  import { Components } from '$lib/types';
  import { getInvalidRouteTooltip, validateRoleRouteAccess } from '$lib/utils';
  import type { ICONS } from '../Icon/icons';

  const imageProps = { width: '150', height: '50' };
  const smallImageProps = { width: '50', height: '50' };

  interface Props {
    isExpanded: boolean;
    onToggle: () => void;
  }

  let { isExpanded, onToggle }: Props = $props();

  let navigationData = $derived(store.componentsConfig?.[Components.NAVIGATION_ITEMS]?.items ?? []);
  let externalNavigationData = $derived(
    store.componentsConfig?.[Components.EXTERNAL_NAVIGATION_ITEMS]?.items ?? []
  );
  let clientLogo = $derived(store.componentsConfig?.[Components.CLIENT_LOGO]);

  function isActive(itemRoute: string, isExact: boolean = false) {
    if (isExact) {
      return page.route.id === itemRoute;
    }
    return page.route.id === itemRoute || page.route.id?.includes(`${itemRoute}/`);
  }
</script>

{#snippet navigationButton(
  icon: string,
  route: string,
  name: string,
  isExact: boolean,
  disabled = false,
  tooltip: string = ''
)}
  <Button
    class={`${isActive(route, isExact) ? 'active' : ''} !no-underline justify-${isExpanded ? 'start' : 'center'} ${
      isExpanded ? 'w-full' : 'w-fit'
    }`}
    variant="ghost"
    size="sm"
    {...disabled ? { disabled: true } : { href: `${base}${route}` }}
    tooltip={isExpanded ? '' : tooltip || name}
  >
    <div class="flex items-center gap-sm">
      <span class="material-icons !text-[18px]">{icon}</span>
      {#if isExpanded}
        <span class="capitalize text-left">{name}</span>
      {/if}
    </div>
  </Button>
{/snippet}

<nav
  class="side-bar flex fixed z-30 h-full border-r border-borderColor text-sidebarText bg-sidebarBg mobile:hidden {isExpanded
    ? 'expanded'
    : 'collapsed'}"
>
  <div
    class="expand-icon absolute right-[-15px] top-5 opacity-0 hover:opacity-100 transition-opacity duration-300"
  >
    <Button
      class="rounded-full"
      size="icon"
      variant="outline"
      tooltip={isExpanded ? 'Collapse' : 'Expand'}
      onclick={onToggle}
    >
      <span class="material-icons !text-sm">{isExpanded ? 'menu_open' : 'menu'}</span>
    </Button>
  </div>

  <div class="flex flex-col justify-between p-6 h-full w-full">
    <div class="flex flex-col text-white">
      <div class="{isExpanded ? 'self-start' : 'self-center'} mb-sm h-[50px]">
        {#if clientLogo}
          {#if isExpanded}
            <img
              src={`${base}/images/${$isDarkMode ? clientLogo.dark : clientLogo.light}`}
              alt={clientLogo.label}
              {...imageProps}
            />
          {:else}
            <img
              src={`${base}/images/${$isDarkMode ? clientLogo.darkSmall : clientLogo.lightSmall}`}
              alt={clientLogo.label}
              {...smallImageProps}
            />
          {/if}
        {:else if store.componentsConfig}
          <Icon
            name={(($isDarkMode ? 'logo_light' : 'logo_dark') +
              (isExpanded ? '' : '_small')) as keyof typeof ICONS}
            width={isExpanded ? '140' : '45'}
            height="45"
          />
        {/if}
      </div>
      <div class="flex flex-col gap-md">
        {#each navigationData as { header, items }}
          <div class="flex flex-col gap-xs items-center w-full">
            <p
              class="text-xs font-semibold uppercase w-full {isExpanded
                ? 'text-left'
                : 'text-center'}"
            >
              {header}
            </p>
            {#each items as { icon, route, name, isExact, disabled }}
              {#if validateRoleRouteAccess(route)}
                {@render navigationButton(icon, route, name, isExact, disabled)}
              {:else}
                {@render navigationButton(
                  icon,
                  route,
                  name,
                  isExact,
                  true,
                  getInvalidRouteTooltip(route)
                )}
              {/if}
            {/each}
          </div>
        {/each}
      </div>
      {#if externalNavigationData.length}
        <div class="bg-white h-[1px] my-4 w-full"></div>
        {#each externalNavigationData as { items }}
          <div class="flex flex-col gap-sm items-center">
            {#each items as { icon, route, name }}
              <Button
                class="!no-underline justify-{isExpanded ? 'start' : 'center'} {isExpanded
                  ? 'w-full'
                  : 'w-fit'}"
                variant="ghost"
                onclick={() => window.open(route, '_blank')}
                tooltip={isExpanded ? '' : name}
              >
                <div class="flex items-center gap-sm">
                  <span class="material-icons !text-[18px]">{icon}</span>
                  {#if isExpanded}
                    <span class="capitalize text-left">{name}</span>
                  {/if}
                </div>
              </Button>
            {/each}
          </div>
        {/each}
      {/if}
    </div>
    {#if store.componentsConfig?.[Components.EXTERNAL_RESOURCES_LIST]}
      <div class="flex flex-col items-center justify-self-end">
        {#each store.componentsConfig[Components.EXTERNAL_RESOURCES_LIST].items as item}
          <a
            class="text-xs text-white p-4 text-center"
            href={item.url}
            rel="noreferrer noopener"
            target="_blank"
          >
            {item.label}
          </a>
        {/each}
      </div>
    {/if}
  </div>
</nav>

<style>
  .side-bar:hover .expand-icon {
    opacity: 100;
  }

  :global(.side-bar .active) {
    background: var(--color-nav-active);
  }

  .expanded {
    width: var(--size-sidebar-expanded);
  }

  .collapsed {
    width: var(--size-sidebar);
  }
</style>
