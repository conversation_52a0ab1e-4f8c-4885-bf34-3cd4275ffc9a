<script lang="ts">
  import { base } from '$app/paths';
  import { page } from '$app/state';
  import { goto } from '$app/navigation';
  import { signOut } from '$lib/authentication';
  import { NotificationSettingsModal, Banner } from '$lib/components';
  import { Button } from '$lib/components/ui/button';
  import * as Popover from '$lib/components/ui/popover';
  import { isDarkMode } from '$lib/stores/theme';
  import store from '$lib/stores/app.svelte';
  import { isNotificationSettingsEnabled, showOutagesBanner } from '$lib/stores/constants';
  import { Components } from '$lib/types';
  import { getInvalidRouteTooltip, onToggleTheme, validateRoleRouteAccess } from '$lib/utils';
  import Icon from '../Icon/Icon.svelte';
  import { fade, slide } from 'svelte/transition';

  interface Props {
    isDesktopView: boolean;
  }

  let { isDesktopView }: Props = $props();

  let showDrawer = $state<boolean>(false);

  let navigationData = $derived(store.componentsConfig?.[Components.NAVIGATION_ITEMS]?.items ?? []);
  let externalNavigationData = $derived(
    store.componentsConfig?.[Components.EXTERNAL_NAVIGATION_ITEMS]?.items ?? []
  );

  $effect(() => {
    if (isDesktopView) showDrawer = false;
  });

  function isActive(itemRoute: string, isExact: boolean = false) {
    if (isExact) {
      return page.route.id === itemRoute;
    }
    return page.route.id === itemRoute || page.route.id?.includes(`${itemRoute}/`);
  }
</script>

<header class="header flex flex-wrap mobile:z-[999]">
  {#if isDesktopView}
    <div class={`pt-5 px-lg bg-bgColor flex gap-md justify-between w-full mb-sm`}>
      {#if showOutagesBanner}
        <Banner />
      {/if}
      <div class="item flex items-center h-fit gap-md ml-auto flex-shrink-0">
        {#if store.componentsConfig?.[Components.HEADER_LOGO]}
          <img
            src="{base}/images/{$isDarkMode
              ? store.componentsConfig[Components.HEADER_LOGO].dark
              : store.componentsConfig[Components.HEADER_LOGO].light}"
            alt={store.componentsConfig[Components.HEADER_LOGO].label}
            width="150"
          />
        {/if}

        <Button
          size="icon"
          variant="ghost"
          onclick={onToggleTheme}
          tooltip="Switch to {$isDarkMode ? 'Light' : 'Dark'} Theme"
        >
          <span class="material-icons">
            {$isDarkMode ? 'light_mode' : 'dark_mode'}
          </span>
        </Button>

        <Popover.Root>
          <Popover.Trigger>
            <Button aria-label="Account" class="button-profile">
              {#if store.authStore}
                <span class="text-white">
                  {store.authStore?.user?.firstName?.[0]}{store.authStore?.user?.lastName?.[0]}
                </span>
              {:else}
                <span class="material-icons text-white !text-lg p-sm">person</span>
              {/if}
            </Button>
          </Popover.Trigger>

          <Popover.Content align="end">
            <div
              class="nav-popup overflow-hidden flex flex-col items-center"
              transition:fade={{ delay: 100, duration: 100 }}
            >
              {#if store.authStore}
                <div class="flex p-sm w-full items-center ml-sm gap-sm">
                  <div class="avatar">
                    {store.authStore?.user?.firstName?.[0]}{store.authStore?.user?.lastName?.[0]}
                  </div>
                  {store.authStore.user.email}
                </div>
              {/if}

              {#if isNotificationSettingsEnabled}
                <NotificationSettingsModal />
              {/if}

              <Button class="!justify-start w-full " variant="ghost" onclick={signOut}>
                <span class="material-icons">logout</span>
                Log Out
              </Button>
            </div>
          </Popover.Content>
        </Popover.Root>
      </div>
    </div>
  {:else}
    <div
      class="fixed top-0 flex w-full flex-sm px-sm border-solid border-b !border-borderColor items-center justify-between h-[var(--size-topbar)] bg-sidebarBg"
    >
      {#if store.componentsConfig?.[Components.CLIENT_LOGO]}
        <img
          src="{base}/images/{$isDarkMode
            ? store.componentsConfig[Components.CLIENT_LOGO].dark
            : store.componentsConfig[Components.CLIENT_LOGO].light}"
          alt={store.componentsConfig[Components.CLIENT_LOGO].label}
          width="140"
        />
      {:else if store.componentsConfig}
        <Icon name={$isDarkMode ? 'logo_light' : 'logo_dark'} width="150" height="40" />
      {/if}
      <Button
        size="icon"
        class="text-white"
        variant="ghost"
        onclick={() => (showDrawer = !showDrawer)}
      >
        <span class="material-icons">
          {showDrawer ? 'menu_open' : 'menu'}
        </span>
      </Button>
    </div>
  {/if}
</header>

{#if showDrawer}
  <div
    transition:slide={{ delay: 100, duration: 250 }}
    class="nav-popup-mobile flex flex-col items-center z-50 px-sm"
  >
    {#each navigationData as { header, items }}
      <p class="text-xs text-accentText self-start p-2 !border-none">{header}</p>
      <div class="self-start w-full">
        {#each items as { icon, route, name, isExact, disabled }}
          {#if validateRoleRouteAccess(route)}
            <Button
              class="!justify-start w-full"
              variant={isActive(route, isExact) ? 'default' : 'ghost'}
              onclick={() => {
                showDrawer = false;
                goto(`${base}${route}`);
              }}
              disabled={!!disabled}
            >
              <span class="material-icons">{icon}</span>
              {name}
            </Button>
          {:else}
            <div class="flex">
              <Button
                class="!justify-start w-full"
                disabled={true}
                tooltip={getInvalidRouteTooltip(route)}
                variant="ghost"
              >
                <span class="material-icons">{icon}</span>
                {name}
              </Button>
            </div>
          {/if}
        {/each}
      </div>
      <hr class="border-t border-borderColor w-full" />
    {/each}

    {#each externalNavigationData as { items }}
      <div class="self-start w-full">
        {#each items as { icon, route, name }}
          <Button
            class="!justify-start w-full"
            variant="ghost"
            onclick={() => {
              showDrawer = false;
              window.open(route, '_blank');
            }}
          >
            <span class="material-icons">{icon}</span>
            {name}
          </Button>
        {/each}
      </div>
    {/each}

    <hr class="border-t border-borderColor w-full" />

    {#if isNotificationSettingsEnabled}
      <NotificationSettingsModal />
    {/if}

    <Button class="!justify-start w-full" variant="ghost" onclick={onToggleTheme}>
      <span class="material-icons">
        {$isDarkMode ? 'light_mode' : 'dark_mode'}
      </span>
      Switch to {$isDarkMode ? 'Light' : 'Dark'} Theme
    </Button>

    <Button class="!justify-start w-full" variant="ghost" onclick={signOut}>
      <span class="material-icons">logout</span>
      Log Out
    </Button>

    {#if store.authStore}
      <div class="flex p-sm w-full items-center ml-sm gap-sm">
        <div class="avatar">
          {store.authStore?.user?.firstName?.[0]}{store.authStore?.user?.lastName?.[0]}
        </div>
        {store.authStore.user.email}
      </div>
    {/if}

    {#if store.componentsConfig?.[Components.EXTERNAL_RESOURCES_LIST]}
      <div class="flex w-full p-4 gap-10 flex-wrap justify-center">
        {#each store.componentsConfig?.[Components.EXTERNAL_RESOURCES_LIST].items as item}
          <a
            class="external-resource-link text-link"
            href={item.url}
            rel="noreferrer noopener"
            target="_blank"
          >
            {item.label}
          </a>
        {/each}
      </div>
    {/if}
  </div>
{/if}

<style>
  .nav-popup-mobile {
    background: var(--color-nav-drawer);
    color: var(--color-primary-text);
    position: fixed;
    left: 0;
    text-align: center;
    top: var(--size-topbar);
    width: 100%;
    z-index: 50;
  }

  .external-resource-link {
    text-align: center;
    display: none;
    text-decoration: underline;
  }

  .avatar {
    background: var(--color-primary);
    border-radius: 50%;
    color: white;
    font-size: 12px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    text-transform: uppercase;
    width: 28px;
  }

  :global(.header .button-profile) {
    background: var(--color-primary);
    color: var(--black);
    border-radius: 50%;
    font-size: 14px;
    height: 32px;
    line-height: 32px;
    text-transform: uppercase;
    width: 32px;
  }

  @media (max-width: 767px) {
    .external-resource-link {
      display: block;
    }

    .header {
      width: 100%;
    }
  }
</style>
