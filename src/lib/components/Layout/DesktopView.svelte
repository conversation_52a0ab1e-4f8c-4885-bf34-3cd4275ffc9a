<script lang="ts">
  import type { Snippet } from 'svelte';

  interface Props {
    class?: string;
    children: Snippet;
  }

  let { class: className = '', children }: Props = $props();
</script>

<div class="desktop-view {className}">
  {@render children()}
</div>

<style>
  .desktop-view {
    display: flex;
  }

  @media (max-width: 767px) {
    .desktop-view {
      display: none;
    }
  }
</style>
