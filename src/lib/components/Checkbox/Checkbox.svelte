<script lang="ts">
  import { Checkbox } from '$lib/components/ui/checkbox';
  import { Label } from '$lib/components/ui/label';

  interface Props {
    checked: boolean;
    disabled?: boolean;
    id?: string;
    indeterminate?: boolean;
    label?: string;
    ref?: HTMLElement | null;
    onCheck?: (isChecked: boolean) => void;
  }

  let {
    checked = $bindable(false),
    disabled = false,
    id = crypto.randomUUID(),
    indeterminate = $bindable(false),
    label = '',
    ref = $bindable(null),
    onCheck,
    ...restProps
  }: Props = $props();
</script>

<div class="flex items-center gap-2">
  <Checkbox
    bind:checked
    {disabled}
    {id}
    bind:indeterminate
    bind:ref
    onCheckedChange={onCheck}
    {...restProps}
  />
  {#if label}
    <Label for={id}>{label}</Label>
  {/if}
</div>
