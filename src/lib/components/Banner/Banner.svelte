<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { isDarkMode } from '$lib/stores/theme';
  import store from '$lib/stores/app.svelte';
  import { findEventLevel, formatAsOfTimestamp } from '$lib/config/utils';
  import { getActiveOutages } from '$lib/api';
  import { Components, type OutageRecord } from '$lib/types';
  import { formatNumbers } from '$lib/utils';
  import { EventsLegend } from '$lib/components';
  import * as Alert from '$lib/components/ui/alert';
  import TogglingInfoModal from '../Modal/TogglingInfoModal.svelte';
  import { Skeleton } from '$lib/components/ui/skeleton';

  const POLL = 600000; // 10 minutes

  let activeSystemRecord = $state<OutageRecord | null>(null);
  let isLoading = $state<boolean>(true);
  let pollingInterval: ReturnType<typeof setInterval> | null = null;

  let eventLevel = $derived(findEventLevel(activeSystemRecord?.activeIncidents));
  let color = $derived(`--color: ${eventLevel?.color?.[$isDarkMode ? 'dark' : 'light']}`);

  onMount(() => {
    getActiveOutagesData();
    startPolling();
  });

  onDestroy(() => {
    stopPolling();
  });

  async function getActiveOutagesData() {
    try {
      const d = await getActiveOutages();
      if (d) {
        activeSystemRecord = d.outageRecords.find(({ region }) => region === 'System') ?? null;
      }
    } catch (e) {
      console.error('Error fetching active outages:', e);
    } finally {
      isLoading = false;
    }
  }

  function startPolling() {
    if (!pollingInterval) {
      pollingInterval = setInterval(async () => {
        try {
          await getActiveOutagesData();
        } catch (e) {
          console.error('Polling error:', e);
        }
      }, POLL);
    }
  }

  function stopPolling() {
    if (pollingInterval) {
      clearInterval(pollingInterval);
      pollingInterval = null;
    }
  }
</script>

{#if isLoading}
  <Skeleton class="flex-1 max-w-[874px] h-[48px] mb-sm" />
{:else if activeSystemRecord}
  <div class="banner mb-sm md:max-w-fit max-w-full" style={color}>
    <div class="flex flex-col gap-sm">
      <div
        class="event-label hidden justify-center items-center font-semibold gap-xs !text-sm truncate {$isDarkMode
          ? 'brightness-125'
          : 'brightness-75'}"
      >
        <span class="material-icons !text-sm"> warning </span>
        <span>Threat Level: {eventLevel?.eventCode} - {eventLevel?.category}</span>
      </div>
      <div class="flex md:justify-between justify-center items-center gap-sm">
        {#if store.componentsConfig?.[Components.BANNER_INFO_ICON]}
          <div class="info-icon">
            <TogglingInfoModal config={store.componentsConfig[Components.BANNER_INFO_ICON]} />
          </div>
        {/if}

        <div class="flex gap-x-md md:gap-x-md gap-y-xs lg:justify-start justify-center flex-wrap">
          <div>
            <div class="flex justify-between font-semibold gap-xl text-xs min-w-42">
              <span>Active {store.capitalizedOutagesLabel}: </span>
              <span class="text-accentText">
                {formatNumbers(activeSystemRecord.activeIncidents)}
              </span>
            </div>
            <div class="flex justify-between font-semibold gap-xl text-xs min-w-42">
              <span>Restored {store.capitalizedOutagesLabel}: </span>
              <span class="text-accentText">
                {formatNumbers(activeSystemRecord.restoredIncidents)}
              </span>
            </div>
          </div>
          <div>
            <div class="flex justify-between font-semibold gap-xl text-xs min-w-42">
              <span>Customers {store.customersAffectedLabel}: </span>
              <span class="text-accentText">
                {formatNumbers(activeSystemRecord.activeCustomerOutages)}
              </span>
            </div>
            <div class="flex justify-between font-semibold gap-xl text-xs min-w-42">
              <span>Restored Customers: </span>
              <span class="text-accentText">
                {formatNumbers(activeSystemRecord.restoredCustomerOutages)}
              </span>
            </div>
          </div>
        </div>
        <div class="events-legend ml-md">
          <EventsLegend currentEventLevel={eventLevel} />
        </div>
      </div>
    </div>
  </div>
{:else}
  <Alert.Root variant="error">
    <Alert.Title>Error:</Alert.Title>
    <Alert.Description>
      No available outage records as of {formatAsOfTimestamp(new Date())}.
    </Alert.Description>
  </Alert.Root>
{/if}

<style>
  .banner {
    padding: 0.3rem 0.5rem;
    position: relative;
    border: 1px solid var(--color);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--color);
  }

  .banner::before {
    content: '';
    position: absolute;
    inset: 0;
    background-color: var(--color);
    opacity: 0.075;
    border-radius: var(--border-radius);
    pointer-events: none;
  }

  .event-label {
    color: var(--color);
  }

  @media (max-width: 1430px) {
    .events-legend,
    .info-icon {
      display: none;
    }

    .event-label {
      display: flex;
    }
  }
</style>
