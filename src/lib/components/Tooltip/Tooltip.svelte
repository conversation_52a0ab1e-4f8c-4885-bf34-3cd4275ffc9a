<script lang="ts">
  import type { Snippet } from 'svelte';
  import type { Tooltip as TooltipPrimitive } from 'bits-ui';
  import * as Tooltip from '../ui/tooltip';

  const TOOLTIP_DELAY_DURATION = 400;

  interface Props {
    className?: string;
    content: Snippet;
    trigger: Snippet;
    align?: 'start' | 'center' | 'end';
    delayDuration?: number;
  }

  let {
    className,
    content,
    trigger,
    align = 'center',
    delayDuration = TOOLTIP_DELAY_DURATION,
    ...restProps
  }: Props & TooltipPrimitive.ProviderProps = $props();
</script>

<Tooltip.Provider {delayDuration} {...restProps}>
  <Tooltip.Root>
    <Tooltip.Trigger class={className}>
      {@render trigger()}
    </Tooltip.Trigger>
    <Tooltip.Content class="max-w-[300px] !z-[1000] border border-solid border-borderColor" {align}>
      {@render content()}
    </Tooltip.Content>
  </Tooltip.Root>
</Tooltip.Provider>
