<script lang="ts">
  import { base } from '$app/paths';
  import { page } from '$app/state';
  import { pieChartTooltipRenderer } from '$lib/config/chart';
  import { formatNumbers, formatDuration } from '$lib/utils';
  import store from '$lib/stores/app.svelte';
  import { showMapWeatherComponents } from '$lib/stores/constants';
  import { formatDateTime } from '$lib/config/utils';
  import { Components, type PieChartTooltipParams, type RetrospectiveStorm } from '$lib/types';
  import { Card } from '$lib/components/ui/card';

  import AgChart from '../Chart/AgChart.svelte';
  import EtrMap from '../Map/EtrMap.svelte';
  import { Button } from '$lib/components/ui/button';
  import { Skeleton } from '$lib/components/ui/skeleton';

  interface Props {
    allowRemove?: boolean;
    isPrimary?: boolean;
    loading?: boolean;
    selectedIndex?: number;
    storm: RetrospectiveStorm | null;
    onClick?: () => void;
  }

  let {
    allowRemove = true,
    isPrimary = false,
    loading = false,
    selectedIndex = 0,
    storm = null,
    onClick = () => {}
  }: Props = $props();

  let selectedConfig = $derived(
    store.componentsConfig?.[Components.SPATIAL_MAP]?.items[selectedIndex]
  );
</script>

<div class="w-[360px]">
  <a
    class="flex flex-col rounded-t-md mb-[-1px] !p-md relative border-solid border !border-borderColor !rounded-b-none !no-underline
   {isPrimary ? 'bg-primary text-white' : 'bg-muted text-primaryText'}"
    href={`${base}/etr-retrospective/events/${storm?.id}${page.url.search}`}
  >
    {#if loading || !storm}
      <div class="flex flex-col gap-xs">
        <Skeleton class="w-[175px]" />
        <Skeleton class="w-[250px]" />
      </div>
    {:else}
      <div class="flex flex-col">
        <p class="text-sm w-[320px]">
          {formatDateTime(storm?.stormStartDate)}
        </p>
        <p class="text-xs">
          Restoration Time: {formatDuration(
            storm?.stormEndDate.getTime() - storm?.stormStartDate.getTime()
          )}
        </p>
      </div>

      {#if isPrimary && allowRemove}
        <div class="absolute top-1 right-1 p-1">
          <Button
            onclick={(e) => {
              e.preventDefault();
              onClick();
            }}
            size="icon"
            variant="ghost"
            tooltip="Remove Storm"
          >
            <span class="material-icons">close</span>
          </Button>
        </div>
      {/if}
    {/if}
  </a>

  <Card class="flex-col flex gap-sm self-stretch !rounded-t-none !p-2">
    <Card class="flex !pb-0 !shadow-none ">
      <div class="w-6/12 flex flex-col">
        <p class="text-center text-xs">
          Total Customers {store.customersAffectedLabel}
        </p>
        <AgChart
          data={storm?.outageRecords || []}
          {loading}
          options={{
            height: 135,
            legend: { enabled: false },
            series: [
              {
                sectorSpacing: 2,
                angleKey: 'totalCumulativeCustomersAffected',
                calloutLabelKey: 'region',
                innerRadiusRatio: 0.8,
                tooltip: {
                  renderer: (data: PieChartTooltipParams) => pieChartTooltipRenderer(data, false)
                },
                type: 'donut',
                calloutLabel: {
                  enabled: false
                },
                innerLabels: [
                  {
                    text: formatNumbers(
                      storm?.outageRecords?.reduce(
                        (a, v) => a + v.totalCumulativeCustomersAffected,
                        0
                      )
                    ),
                    fontWeight: 'bold',
                    fontSize: 14
                  }
                ]
              }
            ]
          }}
        />
      </div>
      <div class="w-6/12 flex flex-col">
        <p class="text-center text-xs">
          Total {store.capitalizedOutagesLabel}
        </p>
        <AgChart
          data={storm?.outageRecords || []}
          {loading}
          options={{
            height: 135,
            legend: { enabled: false },
            series: [
              {
                sectorSpacing: 2,
                angleKey: 'totalCumulativeIncidents',
                calloutLabelKey: 'region',
                innerRadiusRatio: 0.8,
                tooltip: {
                  renderer: (data: PieChartTooltipParams) => pieChartTooltipRenderer(data, false)
                },
                type: 'donut',
                calloutLabel: {
                  enabled: false
                },
                innerLabels: [
                  {
                    text: formatNumbers(
                      storm?.outageRecords?.reduce((a, v) => a + v.totalCumulativeIncidents, 0)
                    ),
                    fontWeight: 'bold',
                    fontSize: 14
                  }
                ]
              }
            ]
          }}
        />
      </div>
    </Card>
    {#if showMapWeatherComponents}
      <Card class="w-full !h-[132px] p-md !shadow-none" {loading}>
        <p class="text-center text-xs mb-sm overflow-hidden whitespace-nowrap text-ellipsis">
          Wind
        </p>
        {#if storm?.windGustThresholds}
          <div class="flex flex-wrap justify-between gap-x-md">
            <span class="text-accentText text-xs">Avg. Sustained Speed:</span>
            <span class="text-right text-xs">
              {formatNumbers(storm?.averageWindSpeed, 2)} mph
            </span>
          </div>
          <div class="flex flex-wrap justify-between gap-x-md">
            <span class="text-accentText text-xs">Hourly Gusts > 20 mph:</span>
            <span class="text-right text-xs">
              {formatNumbers(storm?.windGustThresholds?.over20mph)}
            </span>
          </div>
          <div class="flex flex-wrap justify-between gap-x-md">
            <span class="text-accentText text-xs">Hourly Gusts > 30 mph:</span>
            <span class="text-right text-xs">
              {formatNumbers(storm?.windGustThresholds?.over30mph)}
            </span>
          </div>
          <div class="flex flex-wrap justify-between gap-x-md">
            <span class="text-accentText text-xs">Hourly Gusts > 40 mph:</span>
            <span class="text-right text-xs">
              {formatNumbers(storm?.windGustThresholds?.over40mph)}
            </span>
          </div>
        {:else}
          <p class="text-center text-xs mt-10">No data to display</p>
        {/if}
      </Card>
    {/if}
    <div class="h-[200px]">
      {#if loading}
        <Skeleton class="h-full w-full" />
      {:else}
        <EtrMap
          records={storm?.outageRecords}
          config={store.componentsConfig?.[Components.SPATIAL_MAP]?.items}
          collapseLegend
          {selectedConfig}
          showCentroids={false}
          restrictMapBounds
        />
      {/if}
    </div>
  </Card>
</div>
