<script lang="ts" module>
  import { type VariantProps, tv } from 'tailwind-variants';

  export const alertVariants = tv({
    base: '[&>svg]:text-foreground flex items-center gap-sm w-full h-fit rounded-md border border-solid p-3 [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg~*]:pl-7 shadow-sm relative alert-before',
    variants: {
      variant: {
        default: 'bg-background text-foreground',
        error: 'error border-danger',
        warning: 'warning border-warning',
        info: 'info border-info',
        success: 'success border-success'
      }
    },
    defaultVariants: {
      variant: 'default'
    }
  });

  export type AlertVariant = VariantProps<typeof alertVariants>['variant'];
</script>

<script lang="ts">
  import type { HTMLAttributes } from 'svelte/elements';
  import type { WithElementRef } from 'bits-ui';
  import { cn } from '$lib/utils';

  let {
    ref = $bindable(null),
    class: className,
    variant = 'default',
    children,
    ...restProps
  }: WithElementRef<HTMLAttributes<HTMLDivElement>> & {
    variant?: AlertVariant;
  } = $props();
</script>

<div bind:this={ref} class={cn(alertVariants({ variant }), className)} {...restProps} role="alert">
  {#if variant === 'error'}
    <span class="material-icons text-danger">error</span>
  {/if}
  {#if variant === 'success'}
    <span class="material-icons text-success">check_circle</span>
  {/if}
  {#if variant === 'warning'}
    <span class="material-icons text-warning">warning</span>
  {/if}
  {#if variant === 'info'}
    <span class="material-icons text-info">info</span>
  {/if}
  <div class="flex flex-wrap items-center gap-1">
    {@render children?.()}
  </div>
</div>

<style>
  .alert-before::before {
    content: '';
    position: absolute;
    inset: 0;
    opacity: 0.1;
    z-index: -1;
  }

  .error::before {
    background-color: var(--color-danger);
  }

  .success::before {
    background-color: var(--color-success);
  }

  .info::before {
    background-color: var(--color-info);
  }

  .warning::before {
    background-color: var(--color-warning);
  }
</style>
