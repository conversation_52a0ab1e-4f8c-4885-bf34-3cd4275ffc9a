<script lang="ts">
  import type { Snippet } from 'svelte';
  import type { HTMLAttributes } from 'svelte/elements';
  import type { WithElementRef } from 'bits-ui';
  import { cn } from '$lib/utils';
  import { Skeleton } from '$lib/components/ui/skeleton';

  const DEFAULT_SKELETON_COUNT = 10;

  interface Props extends WithElementRef<HTMLAttributes<HTMLDivElement>> {
    loading?: boolean;
    selected?: boolean;
    skeletonCount?: number;
    children?: Snippet;
    onclick?: (event: MouseEvent) => void;
    ref?: HTMLDivElement | null;
    onkeydown?: (event: KeyboardEvent) => void;
    class?: string;
  }

  let {
    loading = false,
    selected = false,
    onclick = () => {},
    onkeydown = () => {},
    ref = $bindable(null),
    class: className,
    children,
    skeletonCount = DEFAULT_SKELETON_COUNT,
    ...restProps
  }: Props = $props();
</script>

<div
  bind:this={ref}
  class={cn(
    'bg-component rounded-md border border-solid border-borderColor p-4 shadow-sm cursor-auto relative',
    selected && 'selected',
    className
  )}
  {onclick}
  {onkeydown}
  {...restProps}
>
  {@render children?.()}
  {#if loading}
    <div
      class="absolute inset-0 z-10 flex flex-col items-center justify-evenly rounded-md bg-component p-md gap-xs"
    >
      <!--eslint-disable @typescript-eslint/no-unused-vars -->
      {#each Array(skeletonCount) as _}
        <Skeleton class="w-full rounded-lg" style={`height: ${100 / skeletonCount}%`} />
      {/each}
    </div>
  {/if}
</div>

<style>
  .selected {
    box-shadow: 0 0 0 3px var(--color-primary);
  }
</style>
