<script lang="ts">
  import { Accordion as AccordionPrimitive, type WithoutChild } from 'bits-ui';
  import { cn } from '$lib/utils';

  let {
    ref = $bindable(null),
    class: className,
    children,
    ...restProps
  }: WithoutChild<AccordionPrimitive.ContentProps> = $props();
</script>

<AccordionPrimitive.Content
  bind:ref
  class={cn(
    'data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down text-sm transition-all bg-component p-md rounded-bl-md rounded-br-md border border-solid  border-border overflow-hidden',
    className
  )}
  {...restProps}
>
  <div>
    {@render children?.()}
  </div>
</AccordionPrimitive.Content>
