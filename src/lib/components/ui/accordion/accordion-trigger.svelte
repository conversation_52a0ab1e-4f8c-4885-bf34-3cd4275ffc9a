<script lang="ts">
  /* eslint-disable @typescript-eslint/no-magic-numbers */
  import { Accordion as AccordionPrimitive, type WithoutChild } from 'bits-ui';
  import ChevronDown from '@lucide/svelte/icons/chevron-down';
  import { cn } from '$lib/utils';

  let {
    ref = $bindable(null),
    class: className,
    level = 3,
    children,
    ...restProps
  }: WithoutChild<AccordionPrimitive.TriggerProps> & {
    level?: AccordionPrimitive.HeaderProps['level'];
  } = $props();
</script>

<AccordionPrimitive.Header {level} class="flex">
  <AccordionPrimitive.Trigger
    bind:ref
    class={cn(
      'flex flex-1 items-center justify-between hover:bg-muted/50 rounded-md font-medium transition-all [&[data-state=open]>svg]:rotate-180 p-md bg-muted data-[state=open]:rounded-bl-none data-[state=open]:rounded-br-none border data-[state=open]:border-b-0 border-border',
      className
    )}
    {...restProps}
  >
    {@render children?.()}
    <ChevronDown class="size-4 shrink-0 transition-transform duration-200" />
  </AccordionPrimitive.Trigger>
</AccordionPrimitive.Header>
