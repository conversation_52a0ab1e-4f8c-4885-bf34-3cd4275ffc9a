<script lang="ts">
  import { Switch as SwitchPrimitive, type WithoutChildrenOrChild } from 'bits-ui';
  import { cn } from '$lib/utils';

  let {
    ref = $bindable(null),
    class: className,
    checked = $bindable(false),
    size = 'rg',
    ...restProps
  }: WithoutChildrenOrChild<SwitchPrimitive.RootProps> & { size?: 'sm' | 'rg' } = $props();

  const rootSizeClasses = {
    sm: 'h-5 w-9',
    rg: 'h-6 w-11'
  };

  const thumbSizeClasses = {
    sm: 'size-4 data-[state=checked]:translate-x-4',
    rg: 'size-5 data-[state=checked]:translate-x-5'
  };
</script>

<SwitchPrimitive.Root
  bind:ref
  bind:checked
  class={cn(
    'bg-gray-500 focus-visible:ring-ring focus-visible:ring-offset-background data-[state=checked]:bg-primary data-[state=unchecked]:bg-muted peer inline-flex shrink-0 cursor-pointer items-center rounded-lg border border-border transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
    rootSizeClasses[size],
    className
  )}
  {...restProps}
>
  <SwitchPrimitive.Thumb
    class={cn(
      'bg-white pointer-events-none block rounded-full shadow-lg ring-0 transition-transform data-[state=unchecked]:translate-x-0',
      thumbSizeClasses[size]
    )}
  />
</SwitchPrimitive.Root>
