<script lang="ts" module>
  import type { WithElementRef } from 'bits-ui';
  import type { HTMLAnchorAttributes, HTMLButtonAttributes } from 'svelte/elements';
  import { type VariantProps, tv } from 'tailwind-variants';
  import { Tooltip } from '$lib/components';
  import { getButtonGroupCtx } from '$lib/components/Button/ButtonGroup.svelte';

  export const buttonVariants = tv({
    base: 'cursor-pointer ring-offset-background focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0',
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90 text-white',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'bg-background hover:bg-accent hover:text-accent-foreground border border-border',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
        tertiary: 'bg-muted text-muted-foreground hover:bg-muted/80'
      },
      size: {
        default: 'h-10 px-4 py-2',
        xs: 'h-6 rounded-md px-2 !text-xs',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
        icon: 'h-10 w-10'
      }
    },
    defaultVariants: {
      variant: 'default',
      size: 'default'
    }
  });

  export type ButtonVariant = VariantProps<typeof buttonVariants>['variant'];
  export type ButtonSize = VariantProps<typeof buttonVariants>['size'];

  export type ButtonProps = WithElementRef<HTMLButtonAttributes> &
    WithElementRef<HTMLAnchorAttributes> & {
      variant?: ButtonVariant;
      size?: ButtonSize;
      tooltip?: string;
      href?: string;
      value?: string | number;
    };
</script>

<script lang="ts">
  import { cn } from '$lib/utils';

  const ctx = getButtonGroupCtx();

  let {
    class: className,
    variant = 'default',
    size = 'default',
    ref = $bindable(null),
    href = undefined,
    type = 'button',
    children,
    tooltip = '',
    onclick,
    value,
    disabled = false,
    ...restProps
  }: ButtonProps = $props();

  let derivedSize = $derived(ctx?.size ? ctx.size : size);
  let derivedVariant = $derived(
    ctx?.variant ? (ctx?.getValue() === value ? 'default' : ctx.variant) : variant
  );

  function handleClick(e: MouseEvent) {
    if (value !== undefined) ctx?.setValue(value);
    if (onclick) onclick(e as MouseEvent & { currentTarget: EventTarget & HTMLAnchorElement });
    if (ctx?.onClick) ctx.onClick(e);
  }
</script>

{#snippet component()}
  {#if href}
    <a
      bind:this={ref}
      class={cn(buttonVariants({ variant: derivedVariant, size: derivedSize }), className)}
      {href}
      {...restProps}
      onclick={handleClick}
    >
      {@render children?.()}
    </a>
  {:else}
    <button
      bind:this={ref}
      class={cn(buttonVariants({ variant: derivedVariant, size: derivedSize }), className)}
      {type}
      {...restProps}
      onclick={handleClick}
      disabled={ctx?.disabled?.() || disabled}
    >
      {@render children?.()}
    </button>
  {/if}
{/snippet}

{#if tooltip}
  <Tooltip {className}>
    {#snippet content()}
      {tooltip}
    {/snippet}
    {#snippet trigger()}
      {@render component()}
    {/snippet}
  </Tooltip>
{:else}
  {@render component()}
{/if}
