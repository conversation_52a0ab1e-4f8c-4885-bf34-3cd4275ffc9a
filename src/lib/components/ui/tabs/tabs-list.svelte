<script lang="ts">
  import { Tabs } from 'bits-ui';
  import { cn } from '$lib/utils';
  import { getTabsVariant } from './tabs-root.svelte';

  let { ref = $bindable(null), class: className, ...restProps }: Tabs.ListProps = $props();

  const variant = getTabsVariant();
</script>

<Tabs.List
  bind:ref
  class={cn(
    'flex items-center justify-start rounded-md p-1 overflow-x-auto whitespace-nowrap scroll-smooth',
    variant === 'solid' && 'bg-muted text-muted-foreground w-fit',
    className
  )}
  {...restProps}
/>
