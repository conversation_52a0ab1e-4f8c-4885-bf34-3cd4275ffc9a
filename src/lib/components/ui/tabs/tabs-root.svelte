<script lang="ts" module>
  import { getContext, setContext } from 'svelte';
  import type { TabTriggerVariant } from './tabs-trigger.svelte';

  function setTabsVariant(variant: TabTriggerVariant) {
    setContext('tabs', variant);
  }

  export function getTabsVariant(): TabTriggerVariant {
    return getContext<TabTriggerVariant>('tabs');
  }
</script>

<script lang="ts">
  import { Tabs } from 'bits-ui';
  let {
    ref = $bindable(null),
    children,
    variant = 'outline',
    ...restProps
  }: Tabs.RootProps & { variant?: TabTriggerVariant } = $props();

  setTabsVariant(variant);
</script>

<Tabs.Root bind:ref {...restProps}>
  {@render children?.()}
</Tabs.Root>
