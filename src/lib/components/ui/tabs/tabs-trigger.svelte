<script lang="ts" module>
  import { tv, type VariantProps } from 'tailwind-variants';

  export const tabTriggerVariants = tv({
    base: 'ring-offset-background focus-visible:ring-ring inline-flex items-center justify-center whitespace-nowrap px-3 py-1.5 text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
    variants: {
      variant: {
        solid:
          'data-[state=active]:bg-primary data-[state=active]:text-white data-[state=active]:shadow-sm rounded-sm',
        outline:
          'border-b-2 data-[state=inactive]:border-b-border data-[state=active]:border-b-primary'
      }
    },
    defaultVariants: {
      variant: 'solid'
    }
  });

  export type TabTriggerVariant = VariantProps<typeof tabTriggerVariants>['variant'];
</script>

<script lang="ts">
  import { Tabs as TabsPrimitive } from 'bits-ui';
  import { getTabsVariant } from './tabs-root.svelte';
  import { cn } from '$lib/utils';

  let {
    ref = $bindable(null),
    class: className,
    ...restProps
  }: TabsPrimitive.TriggerProps = $props();

  const variant = getTabsVariant();
</script>

<TabsPrimitive.Trigger
  bind:ref
  class={cn(tabTriggerVariants({ variant }), className)}
  {...restProps}
/>
