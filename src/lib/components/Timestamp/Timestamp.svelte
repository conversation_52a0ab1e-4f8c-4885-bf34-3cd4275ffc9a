<script lang="ts">
  let { isDataOutdated, lastUpdatedDate } = $props();
  import { Tooltip } from '..';
</script>

<Tooltip className="w-fit">
  {#snippet trigger()}
    <span
      class={`${isDataOutdated ? 'text-white inline-block rounded-sm bg-danger p-xs' : 'text-accentText'} text-xs`}
    >
      As of {lastUpdatedDate}
    </span>
  {/snippet}
  {#snippet content()}
    {#if isDataOutdated}
      <span class="text-accentText">Data is older than an hour</span>
    {:else}
      <span class="text-accentText">Data is up to date</span>
    {/if}
  {/snippet}
</Tooltip>
