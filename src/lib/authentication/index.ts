import { env } from '$env/dynamic/public';
import store from '$lib/stores/app.svelte';
import {
  acquireAzureToken,
  initialize,
  signIn as signInAzure,
  signOut as signOutAzure
} from './azure';
import { InteractionRequiredAuthError } from '@azure/msal-browser';
import { KeycloakAuthorization } from '$lib/security/keycloak-auth';
import { eventServiceClient } from '$lib/api/http-client';

export const ACCESS_TOKEN_MIN_VALIDITY = 15;
const PUBLIC_CLIENT_AUTHORIZATION_METHOD = env.PUBLIC_CLIENT_AUTHORIZATION_METHOD;
const IS_LOG_USERS_ENABLED: boolean = env.PUBLIC_IS_LOG_USERS_ENABLED === 'true';

interface UserData {
  username: string;
  email: string;
}

export enum AccountRoles {
  STORM_RESOURCE_MANAGER = 'storm-resource-modify' // Can modify resources. Enabled for APC & PGE, conditional for DLC.
}

type EventType = 'login' | 'logout';

export function getCurrentUserData(): UserData | null {
  if (PUBLIC_CLIENT_AUTHORIZATION_METHOD === 'keycloak') {
    const auth = store.authStore;
    if (auth?.user?.email) {
      return {
        username: auth.user.username || auth.user.email,
        email: auth.user.email
      };
    }
  }

  if (PUBLIC_CLIENT_AUTHORIZATION_METHOD === 'azure') {
    const user = store.authenticatedAzureUser;

    if (user) {
      return {
        username: user,
        email: user
      };
    }
  }

  return null;
}

export function azureSignInLog() {
  const userData = getCurrentUserData();
  if (userData) {
    trackUserEvent('login', userData.username, userData.email);
  }
}

export async function trackUserEvent(type: EventType, userName: string, email: string) {
  if (!IS_LOG_USERS_ENABLED) {
    return;
  }

  try {
    if (type === 'login') {
      const lastLoginTracked = sessionStorage.getItem('lastLoginTracked');
      if (lastLoginTracked) {
        return;
      }
      await eventServiceClient.post('events/login', { userName, email });
      sessionStorage.setItem('lastLoginTracked', 'true');
    } else {
      await eventServiceClient.post('events/logout', { userName, email });
    }
  } catch (error) {
    console.error(`${type} event tracking failed:`, error);
  }
}

export async function signIn() {
  if (PUBLIC_CLIENT_AUTHORIZATION_METHOD === 'keycloak') {
    const auth = new KeycloakAuthorization({
      url: env.PUBLIC_KEYCLOAK_APP_URL,
      realm: env.PUBLIC_KEYCLOAK_REALM,
      clientId: env.PUBLIC_KEYCLOAK_CLIENT_ID
    });
    await auth.initializeSession();
    store.authStore = auth;
    const userData = getCurrentUserData();

    if (userData) {
      await trackUserEvent('login', userData.username, userData.email);
    }
  }

  if (PUBLIC_CLIENT_AUTHORIZATION_METHOD === 'azure') {
    await signInAzure();
  }

  store.isAuthenticated = true;
}

export async function initAuthClient() {
  if (PUBLIC_CLIENT_AUTHORIZATION_METHOD === 'azure') {
    await initialize();
  }
}

export async function signOut() {
  try {
    const userData = getCurrentUserData();
    if (IS_LOG_USERS_ENABLED && userData) {
      await trackUserEvent('logout', userData.username, userData.email);
      sessionStorage.removeItem('lastLoginTracked');
    }

    if (PUBLIC_CLIENT_AUTHORIZATION_METHOD === 'keycloak') {
      const auth = store.authStore;

      if (!auth) {
        console.warn('No Keycloak auth instance found during sign out');
        return;
      }

      try {
        await auth.signOut();
      } catch (error) {
        console.error('Error during Keycloak sign out:', error);
      }
    }

    if (PUBLIC_CLIENT_AUTHORIZATION_METHOD === 'azure') {
      try {
        await signOutAzure();
      } catch (error) {
        console.error('Error during Azure sign out:', error);
      }
    }

    store.isAuthenticated = false;
    store.authStore = null;
  } catch (error) {
    console.error('Unexpected error during sign out:', error);
    store.isAuthenticated = false;
    store.authStore = null;
  }
}

export function getUserData() {
  if (PUBLIC_CLIENT_AUTHORIZATION_METHOD === 'keycloak') {
    const auth = store.authStore;

    if (!auth?.user) {
      console.warn('Keycloak user data not found');
      return {
        email: null,
        userId: null
      };
    }

    return {
      email: auth.user.email || null,
      userId: auth.user.id || null
    };
  }

  if (PUBLIC_CLIENT_AUTHORIZATION_METHOD === 'azure') {
    const user = store.authenticatedAzureUser;

    if (!user) {
      console.warn('Azure user data not found');
      return {
        email: null,
        userId: null
      };
    }

    return {
      email: user,
      userId: user
    };
  }

  console.warn('Unknown authorization method');
  return {
    email: null,
    userId: null
  };
}

export async function getAuthToken() {
  let token;

  if (PUBLIC_CLIENT_AUTHORIZATION_METHOD === 'azure') {
    const res = await acquireAzureToken();
    token = res.accessToken;
  } else {
    const currentAuth = store.authStore;

    await currentAuth?.keycloak?.updateToken(ACCESS_TOKEN_MIN_VALIDITY);
    token = currentAuth?.keycloak?.token;
  }

  return token;
}

export function handleAuthError(error: any) {
  if (error instanceof InteractionRequiredAuthError) {
    console.error('Error: ' + error);
    signInAzure();
    return;
  }
  return Promise.reject(error);
}

export function getUserRoles() {
  const auth = store.authStore;
  return auth?.keycloak?.realmAccess?.roles || [];
}
