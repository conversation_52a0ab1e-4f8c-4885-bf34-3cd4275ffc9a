import { env } from '$env/dynamic/public';
import store from '$lib/stores/app.svelte';
import { PublicClientApplication } from '@azure/msal-browser';
import { azureSignInLog } from '$lib/utils/azureUserTracking';

const IS_LOG_USERS_ENABLED: boolean = env.PUBLIC_IS_LOG_USERS_ENABLED === 'true';

export const msalInstance = new PublicClientApplication({
  auth: {
    clientId: env.PUBLIC_AUTH_CLIENT_ID,
    authority: env.PUBLIC_AUTH_AUTHORITY,
    redirectUri: env.PUBLIC_AUTH_REDIRECT_URI
  },
  cache: {
    cacheLocation: 'sessionStorage'
  }
});

export function initialize() {
  return msalInstance
    .initialize()
    .then(() => {
      msalInstance
        .handleRedirectPromise()
        .then((response) => {
          // If response is truthy, the app returned from a redirect operation, and it completed successfully
          if (response !== null) msalInstance.setActiveAccount(response.account);
          store.isAuthenticated = checkAuthenticated();
          store.authenticatedAzureUser = msalInstance.getActiveAccount()?.username;

          if (IS_LOG_USERS_ENABLED) {
            try {
              azureSignInLog();
            } catch (error) {
              console.error('Error during azureSignInLog execution:', error);
            }
          }
        })
        // If .catch is called, this means the app was returning from a redirect operation and an error occurred.
        .catch((error) => {
          console.error(error);
        });
    })
    .catch((error) => {
      console.error(error);
    });
}

export function checkAuthenticated(): boolean {
  return msalInstance.getAllAccounts().length > 0;
}

export function signIn() {
  return msalInstance.loginRedirect({
    scopes: [env.PUBLIC_AUTH_API_SCOPE],
    prompt: 'select_account'
  });
}

export function signOut() {
  return msalInstance.logoutRedirect({});
}

export function acquireAzureToken() {
  return msalInstance.acquireTokenSilent({
    scopes: [env.PUBLIC_AUTH_API_SCOPE]
  });
}
